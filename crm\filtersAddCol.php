<?php
	include("assets/php/functions.php");
	$link=connectUsers();
	$result=$link->query("SELECT * FROM filters WHERE tableUniqueId='CRM-offers-management'");
	while($row=$result->fetch_object())
	{
		$filters=json_decode($row->filters);
		$cols=$filters->columns;
		$addCol=array(array(
			'visible'=>"false",
			'search'=>array( 
				'search'=>"",
				'smart'=> "true",
				'regex'=> "false", 
				'caseInsensitive'=>"true",
				'colName'=>"market"
				)
			));
		$addCol[0]['search']=(object)$addCol[0]['search'];
		$addCol[0]=(object)$addCol[0];
		$addCol=(object)$addCol;
		array_insert($cols,11,$addCol);
		array_insert($cols,16,$addCol);
		$filters->columns=$cols;
		$link->query(sprintf("UPDATE filters SET filters='%s' WHERE id='%s'",
			mysqli_real_escape_string($link, json_encode($filters)),
			mysqli_real_escape_string($link, $row->id)
		));
		print_r($filters);
		echo"<br><br>";
	}
	$link->close();
?>