<?php require('header.php'); ?>
<style>
.select2-container{
	width:200px !important;
	text-align:left;
}
.select2-container .select2-selection--single{
	height:36px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
	height:34px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
	line-height:32px;
}
.select2-container--default .select2-selection--single{
	border: 1px solid #ced4da;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
	color: #495057;
	font-size:1rem;
	font-family: inherit;
	font-weight:430;
}
td.details-control {
    background: url('https://datatables.net/examples/resources/details_open.png') no-repeat center center;
    cursor: pointer;
}
tr.shown td.details-control {
    background: url('https://datatables.net/examples/resources/details_close.png') no-repeat center center;
}
</style>
<body class="widescreen adminbody-void">
<?php 
	$_SESSION['url']='client-management.php';
	if($_SESSION['plasticonDigitalUser']['crm']['clientManagement']!='1')
		header("Location: index.php");
	require('menu.php');
	$id=$_GET['id'];
	if(hasRightsForClient($id, $_SESSION['plasticonDigitalUser']['id'])==false)
		header("Location: home.php");
	if(substr($_SESSION['plasticonDigitalUser']['crm']['clientsBack'],0,9)!="offer.php")
		$_SESSION['backbutton']="client-one-management.php?id=$id";
	$client=getClientInfo($id);
?>
	<div class="content-page">
        <div class="content">    
			<div class="container-fluid">
				<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
					<div class="card mb-3">
						<div class="card-body position-relative">
							<div class="row justify-content-center"><h2><?php echo $client['clientLongName'];?></div>
							<div class="row text-left" id="zakladki">
								<div class="col-1 pointer text-center article-menu-item article-menu-item-active" onclick='showPage("general")' id="general-menu">General Info</div>
								<div class="col-1 pointer text-center article-menu-item" onclick='showPage("contactsTab")' id="contactsTab-menu">Contacts</div>
								<div class="col-1 pointer text-center article-menu-item" onclick='showPage("offers")' id="offers-menu">Offers</div>
								<div class="col-1 pointer text-center article-menu-item" onclick='showPage("followUps")' id="followUps-menu">Follow ups</div>
								<div class="col" style="border-bottom: 1px solid black"></div>
							</div>
							<div class="row">
								<div class="col-lg-12 article-page-item" id='general'>
									<div class="row">
										<div class="col-12" style="padding: 20px;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Details&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#edit"><i class="fas fa-pen"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="row">
																<div class="col-lg-6">
																	<div class="row">
																		<div class="col-lg-6">Enterprise:</div>
																		<div class="col-lg-6"><strong><?php echo $client['enterprise']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Client short name:</div>
																		<div class="col-lg-6"><strong><?php echo $client['clientShortName']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Client long name:</div>
																		<div class="col-lg-6"><strong><?php echo $client['clientLongName']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Previous name:</div>
																		<div class="col-lg-6"><strong><?php echo $client['previousName']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">VAT number:</div>
																		<div class="col-lg-6"><strong><?php echo $client['vatNumber']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">ID:</div>
																		<div class="col-lg-6"><strong><?php echo $client['id']; ?></strong></div>
																	</div>
																</div>
																<div class="col-lg-6">
																	<div class="row">
																		<div class="col-lg-6">Type:</div>
																		<div class="col-lg-6"><strong>
																		<?php 
																			foreach(str_split($client['rei']) as $reiOneShow)
																				switch($reiOneShow)
																				{
																					case 'R':
																						echo "Reseller, ";
																						break;
																					case 'E':
																						echo "End client, ";
																						break;
																					case 'I':
																						echo "Intercompany, ";
																						break;
																				}													
																		?>
																		</strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Sub type:</div>
																		<div class="col-lg-6"><strong><?php echo $client['subType']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Industry:</div>
																		<div class="col-lg-6"><strong><?php echo translateMarket($client['market']); ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Category:</div>
																		<div class="col-lg-6"><strong><?php echo $client['type']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Hitrate:</div>
																		<div class="col-lg-6"><strong>
																		<?php 
																			$hitrate=$client['hitrate'];
																			if($hitrate<31)
																				$bg="bg-danger";
																			if($hitrate>=31&&$hitrate<=50)
																				$bg="bg-warning";
																			if($hitrate>50)
																				$bg="bg-success";
																		?>
																		<div class="progress mt5" style="width:200px;height:15px;"><div class="progress-bar progress-bar-animated progress-bar-striped <?php echo $bg; ?>" role="progressbar" style="width:<?php echo $hitrate; ?>%;color:black;" aria-valuenow="<?php echo $hitrate; ?>" aria-valuemin="0" aria-valuemax="100"><?php echo $hitrate; ?>%</div>
																		</div>
																		</strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Orders / offers value:</div>
																		<div class="col-lg-6"><strong><?php echo $client['ordersValue']. " </strong>/<strong> ".$client['offersValue']." k&euro;"; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Orders / offers count:</div>
																		<div class="col-lg-6"><strong><?php echo $client['orders']. " </strong>/<strong> ".$client['offers'];; ?></strong></div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-6" style="padding: 20px;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Address&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#editAddr"><i class="fas fa-pen"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="row">
																<div class="col-lg-12">
																	<div class="row">
																		<div class="col-lg-6">Street and number:</div>
																		<div class="col-lg-6"><strong><?php echo $client['adres']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">City:</div>
																		<div class="col-lg-6"><strong><?php echo $client['city']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Zip:</div>
																		<div class="col-lg-6"><strong><?php echo $client['zip']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Country:</div>
																		<div class="col-lg-6"><strong><?php echo $client['country']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">State / Province / Region:</div>
																		<div class="col-lg-6"><strong><?php echo $client['region']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Website URL:</div>
																		<div class="col-lg-6"><strong><?php echo $client['url']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">E-mail:</div>
																		<div class="col-lg-6"><strong><?php echo $client['email']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Phone:</div>
																		<div class="col-lg-6"><strong><?php echo $client['phone']; ?></strong></div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="col-6" style="padding: 20px;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Invoice address&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#editAddrInv"><i class="fas fa-pen"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="row">
																<div class="col-lg-12">
																	<div class="row">
																		<div class="col-lg-6">Address:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invAddr']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Extra mailing address:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invExtraMA']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">City:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invCity']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Post code:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invZip']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">Country:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invCountry']; ?></strong></div>
																	</div>
																	<div class="row">
																		<div class="col-lg-6">IBAN:</div>
																		<div class="col-lg-6"><strong><?php echo $client['invIBAN']; ?></strong></div>
																	</div>
																	<div class="row">
																		&nbsp;
																	</div>
																	<div class="row">
																		&nbsp;
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-lg-12 article-page-item" id='contactsTab'>
									<div class="row">
										<div class="col-lg-6" style="padding: 20px;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Responsible&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#addMatrixRow"><i class="fas fa-plus"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body" style="padding:0 !important;">
															<div class="row">
																<div class="col-lg-12">
																	<div class="row">
																		<div class="table-responsive" style="margin: 0px 20px;">
																			<table id="matrix" class="table table-bordered table-hover display" style="margin:0!important;width:100%;">
																				<thead>
																					<tr>
																						<th style="width:20%;">Company</th>
																						<th style="width:20%;">TL</th>
																						<th style="width:20%;">KAM</th>
																						<th style="width:20%;">ASM</th>
																						<th style="width:20%;">Actions</th>
																					</tr>
																				</thead>
																			</table>
																		</div>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="col-lg-6" style="padding: 20px;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Platform accesses&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#addPlatformRow"><i class="fas fa-plus"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body" style="padding:0 !important;">
															<div class="col-lg-12">
																<div class="table-responsive">
																	<table id="platforms" class="table table-bordered table-hover display" style="margin:0!important;width:100%;">
																		<thead>
																			<tr>
																				<th style="width:25%;">URL / Desc.</th>
																				<th style="width:25%;">Login</th>
																				<th style="width:25%;">Password</th>
																				<th style="width:25%;">Actions</th>
																			</tr>
																		</thead>
																	</table>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-lg-12" style="padding: 20px !important;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Contacts&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#addContact"><i class="fas fa-plus"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="col-lg-12">
																<div class="row">
																	<table id="clietsContacts" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
																		<thead>
																			<tr>
																				<th>ID</th>
																				<th>Gender</th>
																				<th>Name</th>
																				<th>Surname</th>
																				<th>E-mail</th>
																				<th>Direct number</th>
																				<th>Mobile number</th>
																				<th>Position</th>
																				<th>Available</th>
																				<th>More</th>
																			</tr>
																		</thead>
																		<tbody>
																			<?php listClientContacts($id); ?>
																		</tbody>
																	</table>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-lg-12 article-page-item" id='offers'>
									<div class="row">
										<div class="col-lg-12" style="padding: 20px !important;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Offers
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="col-lg-12">
																<div class="row">
																	<table id="clientsOffers" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
																		<thead>
																			<tr>
																				<th></th>
																				<th>QuoteNo</th>
																				<th title="Amount of Sales Articles">ASA</th>
																				<th title="Quote Type">QT</th>
																				<th>Scope</th>
																				<th title="Responsible">R</th>
																				<th title="Company">C</th>
																				<th>Req. Order Date</th>
																				<th>Order date</th>
																				<th title="Order Value Estimated">OVE (k&euro;)</th>
																				<th>GxG (%)</th>
																				<th>Next contact date</th>
																				<th>Last comment</th>
																			</tr>
																		</thead>
																	</table>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-lg-12 article-page-item" id='followUps'>
									<div class="row">
										<div class="col-lg-12" style="padding: 20px !important;">
											<div class="w100p">
												<div class="card">
													<div class="card-header">
														Follow ups&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#followUp"><i class="fas fa-plus"></i></button>
													</div>
													<div id="collapseOne" class="collapse show">
														<div class="card-body">
															<div class="col-lg-12">
																<div class="row">
																	<table id="followups" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
																		<thead>
																			<tr>
																				<th>S</th>
																				<th>ID</th>
																				<th>Follow up</th>
																				<th>Contact with</th>
																				<th>Date</th>
																				<th>Next contact</th>
																				<th>Note</th>
																				<th>More</th>
																			</tr>
																		</thead>
																	</table>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<hr>
							<div class="row justify-content-center">
								<?php if($_SESSION['plasticonDigitalUser']['crm']['admin']==1) { ?>
								<div class="col-lg-3 text-center">
									<button class="btn btn-primary form-100" data-toggle="modal" data-target="#replace">Replace client</button>
								</div>
								<?php } ?>
								<?php if($_SESSION['plasticonDigitalUser']['crm']['externalCompany']==0) { ?>
								<div class="col-lg-3 text-center">
									<button class="btn btn-danger form-100" data-toggle="modal" data-target="#delete">Delete client</button>
								</div>
								<?php } ?>
								<div class="col-lg-3 text-center"><a href="<?php echo $_GET['source']=='search' ? "javascript:window.open('','_self').close()" : $_SESSION['plasticonDigitalUser']['crm']['clientsBack']; ?>"><button class="btn btn-danger form-100">Close</button></a></div>
							</div>
							<?php
								if(isset($_GET['saveFail']))
									alertDanger("Fill all required fields!");
								if(isset($_GET['saveFail1']))
									alertDanger("Unknown error. Try again later.");
								if(isset($_GET['saveSuccess']))
									alertSuccess("Changes have been saved.");
								if(isset($_GET['noteSuccess']))
									alertSuccess("Comment has been added.");
								if(isset($_GET['addContact']))
									alertSuccess("New contact has been added.");
								if(isset($_GET['deleteContact']))
									alertSuccess("Contact has been removed.");
								if(isset($_GET['editContact']))
									alertSuccess("Contact has been updated.");
								if(isset($_GET['addContactPerson']))
									alertSuccess("Added contact person!");
								if(isset($_GET['follow']))
									alertSuccess("Follow up has been added!");
								if(isset($_GET['deletedFU']))
									alertSuccess("Follow up has been deleted!");
								if(isset($_GET['foEdited']))
									alertSuccess("Follow up has been edited!");
							?>
							<?php if($_SESSION['plasticonDigitalUser']['crm']['externalCompany']==0) { ?>
							<div id="slideOut">
							    <div class="slideOutTab pointer">
									<div>
										<p>Comments</p>
									</div>
								</div>
								<div class="modal-content-slide">
									<div class="comments-list-box" style="height:320px;overflow-y:scroll;overflow-x:hidden;">
								
									</div>
									<form method="POST">
										<div class="row mt20">
											<div id="commentsBox" style="width:96%;">
								
											</div>
										</div>
									</form>
									<div class="modal-footer-slide"> </div>
							    </div>
							</div>
							<?php } ?>
						</div>
					</div>
				</div>
				<div class="modal fade" id="addMatrixRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Add responsibility</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="saveMatrix(); return false;">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Company:<br>
												<select name="mxCompany" class="form-control select2" required>
													<option value="" selected>Sales company</option>
													<?php echo getCompanies(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>TL:<br>
												<select name="mxTL" class="form-control select2">
													<?php listTLs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>KAM:<br>
												<select name="mxKAM" class="form-control select2">
													<?php listKAMs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>ASM:<br>
												<select name="mxASM" class="form-control select2">
													<?php listASMs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="submit" onclick="redBorder()" name="saveMatrixB" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="addPlatformRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Add platform</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="savePlatform(); return false;">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>URL / Despription:<br>
												<input type="text" name="platformsDescAdd" id="platformsDescAdd" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Login:<br>
												<input type="text" name="platformsLoginAdd" id="platformsLoginAdd" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Password:<br>
												<input type="text" name="platformsPasswordAdd" id="platformsPasswordAdd" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="submit" onclick="redBorder()" name="savePlatformAdd" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="editMatrixRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit responsibility</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="saveEditMatrix(); return false;">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Company:<br>
												<select name="mxCompanyE" class="form-control select2" required>
													<option value="" selected>Sales company</option>
													<?php echo getCompanies(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>TL:<br>
												<select name="mxTLE" class="form-control select2">
													<?php listTLs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>KAM:<br>
												<select name="mxKAME" class="form-control select2">
													<?php listKAMs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>ASM:<br>
												<select name="mxASME" class="form-control select2">
													<?php listASMs(); ?>
												</select>
											</label>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
								<input type="hidden" name="mxEditId">
									<input type="submit" onclick="redBorder()" name="saveMatrixBE" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="editPlatformRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit platform</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="saveEditPlatform(); return false;">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>URL / Despription:<br>
												<input type="text" name="platformsDesc" id="platformsDesc" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Login:<br>
												<input type="text" name="platformsLogin" id="platformsLogin" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">
										<div class="row justify-content-center">
											<label>Password:<br>
												<input type="text" name="platformsPassword" id="platformsPassword" class="form-control">
											</label>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
								<input type="hidden" name="platformsEditId">
									<input type="submit" onclick="redBorder()" name="savePlatform" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="deleteMatrixRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Are you sure to delete this responsibility row?</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="saveDelMatrix(); return false;">
								<div class="modal-footer">
									<input type="hidden" name="mxDelId">
									<input type="submit" onclick="redBorder()" name="saveMatrixB" value="Delete" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="deletePlatformRow">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Are you sure to delete this row?</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST" onsubmit="saveDelPlatform(); return false;">
								<div class="modal-footer">
									<input type="hidden" name="platformsDelId">
									<input type="submit" onclick="redBorder()" name="savePlatform" value="Delete" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="followUp">
					<div class="modal-dialog" style="min-width:50%;">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Follow up: <?php echo $client['clientLongName']; ?></h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="col-lg-6 text-center borderRight">
											<div class="row justify-content-center">
												<div class="row justify-content-center">
													<label style="width:200px;">Follow up *:<br>
														<select name="foPerson" class="form-control select2" required>
															<?php listFollowUpUsersAdd(); ?>
														</select>
													</label>
												</div>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Contact With: *<br>
													<select style="max-width:200px;" name="foContactWith" id="foContactWith" class="form-control select2 incCont" required>
														<option value='' selected disabled>Select contact</option>
														<?php listContactsPerson($client['id']); ?> 
														<option value='0' selected disabled>Other</option>
													</select><span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary incCont" data-dismiss="modal" data-toggle="modal" data-target="#addCon"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Contact date: *<br>
													<input type="date" class="form-control form-100 incCont" name="foCurrent" id="foCurrent" value="<?php echo date('Y-m-d'); ?>" required>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Next contact date: *<br>
													<input type="date" class="form-control form-100 incCont" name="foNext" id="foNext" value="<?php echo $client['nextContactDate']; ?>" required>
												</label>
											</div>
											<div class="row justify-content-center">
												<button type="button" class="btn addMonths" amount="1">+1m</button>&nbsp;
												<button type="button" class="btn addMonths" amount="3">+3m</button>&nbsp;
												<button type="button" class="btn addMonths" amount="6">+6m</button>&nbsp;
												<button type="button" class="btn addMonths" amount="12">+12m</button>
											</div>
										</div>
										<div class="col-lg-6 text-center">
											<div class="row justify-content-center">
												<label style="width:80%;">Comment: *<br>
													<textarea class="form-control form-100 incCont" style="height:200px;width:100%" name="foLastCommenct" id="foLastCommenct" required></textarea>
												</label>
											</div>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="submit" onclick="redBorder()" name="saveFollow" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="followUpEdit">
					<div class="modal-dialog" style="min-width:50%;">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Follow up edit</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="col-lg-6 text-center borderRight">
											<div class="row justify-content-center">
												<div class="row justify-content-center">
													<label style="width:200px;">Follow up *:<br>
														<select name="foEditPerson" id="foEditPerson" class="form-control select2" required>
															<?php listFollowUpUsersAdd(); ?>
														</select>
													</label>
												</div>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Contact With: *<br>
													<select style="max-width:200px;" name="foEditContactWith" id="foEditContactWith" class="form-control select2 incCont" required>
														<option value='' selected disabled>Select contact</option>
														<?php listContactsPerson($client['id']); ?> 
														<option value='0' selected disabled>Other</option>
													</select>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Contact date: *<br>
													<input type="date" class="form-control form-100" name="foEditCurrent" id="foEditCurrent" required>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Next contact date: *<br>
													<input type="date" class="form-control form-100" name="foEditNext" id="foEditNext" required>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name" style="width:200px;">Status: *<br>
													<select style="max-width:200px;" name="foEditStatus" id="foEditStatus" class="form-control select2" required>
														<option value='New'>New</option>
														<option value='In progress'>In progress</option>
														<option value='Closed'>Closed</option>
													</select>
												</label>
											</div>
										</div>
										<div class="col-lg-6 text-center">
											<div class="row justify-content-center">
												<label style="width:80%;">Comment: *<br>
													<textarea class="form-control form-100" style="height:200px;width:100%" name="foEditLastCommenct" id="foEditLastCommenct" required></textarea>
												</label>
											</div>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="hidden" name="foEditId" id="foEditId">
									<input type="submit" onclick="redBorder()" name="saveEditFollow" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="followUpRead">
					<div class="modal-dialog" style="min-width:50%;">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Follow up read</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<div class="modal-body">
								<div class="row" id="fuReadHTML">
									
								</div>
							</div>
							<div class="modal-footer">
								<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
							</div>
						</div>
					</div>
				</div>
				<div class="modal fade" id="followUpDelete">
					<div class="modal-dialog" style="min-width:50%;">
						<form method="POST">
							<div class="modal-content">
								<div class="modal-header">
									<h4 class="modal-title">Do you want to delete follow number <span id="fuDeleteIdTitle"></span>?</h4>
									<button type="button" class="close" data-dismiss="modal">&times;</button>
								</div>
								<div class="modal-footer">
									<input type="hidden" name="fuDeleteId" id="fuDeleteId" required>
									<input type="submit" class="btn btn-primary form-100" name="fuDeleteSubmit" value="Delete">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div class="modal fade" id="addCon">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Adding new contact person</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<label>Gender: *<br>
											<select name="contactGender" class="select2 form-control" required>
												<option value="" selected disabled>Select</option>
												<option value="Male">Male</option>
												<option value="Female">Female</option>
											</select>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Name: *<br>
											<input type="text" placeholder="Name" class="form-control form-100" name="contactName" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Surname: *<br>
											<input type="text" placeholder="Surname" class="form-control form-100" name="contactSurname" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Adress e-mail: *<br>
											<input type="email" placeholder="Adress e-mail" class="form-control form-100" name="contactEmail" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Direct number: *<br>
											<input type="text" placeholder="Direct number" class="form-control form-100" name="contactPhone1" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Mobile number:<br>
											<input type="text" placeholder="Mobile number" class="form-control form-100" name="contactPhone2">
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Position:<br>
											<input type="text" placeholder="Position" class="form-control form-100" name="contactPosition">
										</label>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="submit" onclick="redBorder()" name="addContactPerson" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#followUp">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="edit">
					<div class="modal-dialog mw1000">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit client.</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="col-lg-6 text-center">
											<div class="row justify-content-center">
												<label for="name">Enterprise: *<br>
												<input class="form-control form-100" type="text" name="enterprise" placeholder="Enterprise" required value="<?php echo $client["enterprise"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="clientShortName">Client short name: *<br>
												<input class="form-control form-100" type="text" name="clientShortName" placeholder="Short name" required value="<?php echo $client["clientShortName"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="clientLongName">Client long name: *<br>
												<input class="form-control form-100" type="text" name="clientLongName" placeholder="Long name" required value="<?php echo $client["clientLongName"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">VAT number:<br>
												<input class="form-control form-100" type="text" name="vatNumber" placeholder="VAT number" value="<?php echo $client["vatNumber"]; ?>"></label>
											</div>
										</div>
										<div class="col-lg-6 text-center">
											<div class="row justify-content-center">
												<label for="name">Type: *<br>
													<select class="form-control select2" name="rei[]" required id="rei" multiple="multiple">
														<option value="">Select type</option>
														<option value="R">Reseller</option>
														<option value="E">End client</option>
														<option value="I">Intercompany</option>
													</select>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Sub type:<br>
													<select class="form-control select2" name="subType" id="subType">
														<option value="">Select sub type</option>
														<option value="Contractor">Contractor</option>
														<option value="Engineering">Engineering</option>
														<option value="Installation company">Installation company</option>
													</select>
												</label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Industry: *<br>
												<select name="market[]" id="market" class="form-control select2" required multiple>
													<option value="" selected disabled>Select industry</option>
													<option value="CH">Chemical</option>
													<option value="EW">Energy from waste</option>
													<option value="ME">Metal</option>
													<option value="NN">Not defined</option>
													<option value="PA">Paper</option>
													<option value="PW">Power</option>
													<option value="RE">Refinery</option>
													<option value="SC">Semiconductor</option>
													<option value="WA">Water</option>
													<option value="FO">Food</option>
													<option value="PR">Projects</option>
													<option value="CN">Construction</option>
													<option value="AD">Administration</option>
													<option value="PD">Production</option>
												</select></label>
											</div>
											<div class="row justify-content-center">
												<label for="example1">Category: *<br>
												<select class="form-control select2" id="type" name="type" required>    
													<option value="" disabled selected>Select type</option>
													<option value="A+" <?php if($client["type"]=="A+") echo "selected"; ?>>A+</option>
													<option value="B+" <?php if($client["type"]=="B+") echo "selected"; ?>>B+</option>
													<option value="A-" <?php if($client["type"]=="A-") echo "selected"; ?>>A-</option>
													<option value="B-" <?php if($client["type"]=="B-") echo "selected"; ?>>B-</option>
													<option value="New" <?php if($client["type"]=="New") echo "selected"; ?>>New</option>
												</select></label>
											</div>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input class="hidden" type="number" name="id" value="<?php echo $id; ?>">
									<input type="submit" name="saveClient" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="editAddr">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit client address.</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="col-lg-12 text-center">
											<div class="row justify-content-center">
												<label for="name">General address: <br>
											</div>
											<div class="row justify-content-center">
												<label for="name">Address:<br>
												<input class="form-control form-100" type="text" name="adres" placeholder="Address" value="<?php echo $client["adres"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">City: *<br>
												<input class="form-control form-100" type="text" name="city" placeholder="City" required value="<?php echo $client["city"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Zip:<br>
												<input class="form-control form-100" type="text" name="zip" placeholder="Zip" value="<?php echo $client["zip"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="example1">Country: *<br>
												<select class="form-control select2" id="countries" name="country" required>    
													<?php listCountries(); ?>
												</select></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">State / Province / Region:<br>
												<input class="form-control form-100" type="text" name="region" placeholder="State / Province / Region" value="<?php echo $client["region"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Website URL:<br>
												<input class="form-control form-100" type="text" name="url" placeholder="https://www.example.com" value="<?php echo $client["url"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">E-mail:<br>
												<input class="form-control form-100" type="email" name="email" placeholder="<EMAIL>" value="<?php echo $client["email"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Phone:<br>
												<input class="form-control form-100" type="text" name="phone" placeholder="Phone" value="<?php echo $client["phone"]; ?>"></label>
											</div>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input class="hidden" type="number" name="invId" value="<?php echo $id; ?>">
									<input type="submit" name="saveClientAdd" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="editAddrInv">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit client addresses.</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<div class="col-lg-12 text-center">
											<div class="row justify-content-center">
												<label for="name">Invoice address: <br>
											</div>
											<div class="row justify-content-center">
												<label for="name">Address:<br>
												<input class="form-control form-100" type="text" name="invAddr" placeholder="Address" value="<?php echo $client["invAddr"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">City: *<br>
												<input class="form-control form-100" type="text" name="invCity" placeholder="City" value="<?php echo $client["invCity"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">Zip:<br>
												<input class="form-control form-100" type="text" name="invZip" placeholder="Zip" value="<?php echo $client["invZip"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="example1">Country: *<br>
												<select class="form-control select2" id="countriesInv" name="invCountry">    
													<?php listCountries(); ?>
												</select></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">E-mail:<br>
												<input class="form-control form-100" type="email" name="invExtraMA" placeholder="<EMAIL>" value="<?php echo $client["invExtraMA"]; ?>"></label>
											</div>
											<div class="row justify-content-center">
												<label for="name">IBAN:<br>
												<input class="form-control form-100" type="text" name="invIBAN" placeholder="IBAN" value="<?php echo $client["invIBAN"]; ?>"></label>
											</div>
										</div>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input class="hidden" type="number" name="invIdEd" value="<?php echo $id; ?>">
									<input type="submit" name="saveClientAddInv" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="delete">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Are you sure you want to delete this client?</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-footer">
									<input class="hidden" type="number" name="id" value="<?php echo $id; ?>">
									<input type="submit" name="delete" value="Yes, delete" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="replace">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Replace this client with one from list.</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<label>Select client: *<br>
											<select name="replacer" required class="select2-client form-control">
											</select>
										</label>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input type="submit" name="replaceNow" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="deleteR">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Are you sure you want to delete <span id="deleteIdSpanTittle"></span> from contacts?</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-footer">
									<input class="hidden" type="number" name="id" value="<?php echo $id; ?>">
									<input class="hidden" type="number" name="contactId" id="deleteId">
									<input type="submit" name="deleteContact" value="Yes, delete" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="addContact">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Adding new contact</h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<label>Gender: *<br>
											<select name="contactGender" class="select2 form-control" required>
												<option value="" selected disabled>Select</option>
												<option value="Male">Male</option>
												<option value="Female">Female</option>
											</select>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Name: *<br>
											<input type="text" placeholder="Name" class="form-control form-100" name="contactName" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Surname: *<br>
											<input type="text" placeholder="Surname" class="form-control form-100" name="contactSurname" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Adress e-mail: *<br>
											<input type="email" placeholder="Adress e-mail" class="form-control form-100" name="contactEmail" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Direct number: *<br>
											<input type="text" placeholder="Direct number" class="form-control form-100" name="contactPhone1" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Mobile number:<br>
											<input type="text" placeholder="Mobile number" class="form-control form-100" name="contactPhone2">
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Position:<br>
											<input type="text" placeholder="Position" class="form-control form-100" name="contactPosition">
										</label>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input class="hidden" type="number" name="id" value="<?php echo $id; ?>">
									<input type="submit" name="addContact" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<div class="modal fade" id="editR">
					<div class="modal-dialog">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Edit <span id="contactTitle"></span></h4>
								<button type="button" class="close" data-dismiss="modal">&times;</button>
							</div>
							<form method="POST">
								<div class="modal-body">
									<div class="row justify-content-center">
										<label>Gender: *<br>
											<select name="contactGenderEdit" id="contactGenderEdit" class="select2 form-control" required>
												<option value="" selected disabled>Select</option>
												<option value="Male">Male</option>
												<option value="Female">Female</option>
											</select>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Name: *<br>
											<input type="text" placeholder="Name" class="form-control form-100" name="contactNameEdit" id="contactNameEdit" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Surname: *<br>
											<input type="text" placeholder="Surname" class="form-control form-100" name="contactSurnameEdit" id="contactSurnameEdit" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Adress e-mail: *<br>
											<input type="email" placeholder="Adress e-mail" class="form-control form-100" name="contactEmailEdit" id="contactEmailEdit" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Direct number: *<br>
											<input type="text" placeholder="Direct number" class="form-control form-100" name="contactPhone1Edit" id="contactPhone1Edit" required>
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Mobile number:<br>
											<input type="text" placeholder="Mobile number" class="form-control form-100" name="contactPhone2Edit" id="contactPhone2Edit">
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Position:<br>
											<input type="text" placeholder="Position" class="form-control form-100" name="contactPositionEdit" id="contactPositionEdit">
										</label>
									</div>
									<div class="row justify-content-center">
										<label>Available for contact:&nbsp;<input type="checkbox" value="1" class="chbox mt5" name="avaliableEdit" id="avaliableEdit">
										</label>
									</div>
									<div class="row justify-content-center">* - Fields required.</div>
								</div>
								<div class="modal-footer">
									<input class="hidden" type="number" name="id" value="<?php echo $id; ?>">
									<input class="hidden" type="number" name="editId" id="editId">
									<input type="submit" name="editContact" value="Save" class="btn btn-primary form-100">
									<button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
								</div>
							</form>
						</div>
					</div>
				</div>
				<?php
					if(isset($_POST['saveClientAdd']))
					{
						$idEdit=strip_tags($_POST['invId']);
						$link=connect();
						$link->query('SET NAMES utf8');
						$link->query('SET CHARACTER_SET utf8_unicode_ci');
						$query=sprintf("UPDATE `clients` SET `adres`='%s', `city`='%s', `zip`='%s', `country`='%s', `region`='%s', `url`='%s', `email`='%s', `phone`='%s' WHERE id='%s'",
						mysqli_real_escape_string($link, strip_tags($_POST['adres'])),
						mysqli_real_escape_string($link, strip_tags($_POST['city'])),
						mysqli_real_escape_string($link, strip_tags($_POST['zip'])),
						mysqli_real_escape_string($link, strip_tags($_POST['country'])),
						mysqli_real_escape_string($link, strip_tags($_POST['region'])),
						mysqli_real_escape_string($link, strip_tags($_POST['url'])),
						mysqli_real_escape_string($link, strip_tags($_POST['email'])),
						mysqli_real_escape_string($link, strip_tags($_POST['phone'])),
						mysqli_real_escape_string($link, $idEdit));
						$link->query($query);
						//echo $query;
						header("Location: client-one-management.php?id=".$idEdit."&saveSuccess=true");
					}
					if(isset($_POST['saveClientAddInv']))
					{
						$idEdit=strip_tags($_POST['invIdEd']);
						$link=connect();
						$link->query('SET NAMES utf8');
						$link->query('SET CHARACTER_SET utf8_unicode_ci');
						$query=sprintf("UPDATE `clients` SET `invAddr`='%s' , `invExtraMA`='%s' , `invCity`='%s' , `invCountry`='%s' , `invZip`='%s' , `invIBAN`='%s' WHERE id='%s'",
						mysqli_real_escape_string($link, strip_tags($_POST['invAddr'])),
						mysqli_real_escape_string($link, strip_tags($_POST['invExtraMA'])),
						mysqli_real_escape_string($link, strip_tags($_POST['invCity'])),
						mysqli_real_escape_string($link, strip_tags($_POST['invCountry'])),
						mysqli_real_escape_string($link, strip_tags($_POST['invZip'])),
						mysqli_real_escape_string($link, strip_tags($_POST['invIBAN'])),
						mysqli_real_escape_string($link, $idEdit));
						$link->query($query);
						//echo $query;
						header("Location: client-one-management.php?id=".$idEdit."&saveSuccess=true");
					}
					if(isset($_POST['saveClient']))
					{
						if(!empty($_POST["clientLongName"])&&!empty($_POST["enterprise"]))
						{
							$idEdit=strip_tags($_POST['id']);
							$prevName=$client['previousName'];
							if($_POST["clientLongName"]!=$client['clientLongName'])
								if(strlen($prevName)!=0)
									$prevName.=", ".$client['clientLongName'];
								else
									$prevName.=$client['clientLongName'];
							$link=connect();
							$link->query('SET NAMES utf8');
							$link->query('SET CHARACTER_SET utf8_unicode_ci');
							$query=sprintf("UPDATE `clients` SET `clientLongName`='%s', `enterprise`='%s', `clientShortName`='%s', `rei`='%s', `subType`='%s', `market`='%s', `type`='%s', previousName='%s', vatNumber='%s' WHERE id='%s'",
							mysqli_real_escape_string($link, strip_tags($_POST['clientLongName'])),
							mysqli_real_escape_string($link, strip_tags($_POST['enterprise'])),
							mysqli_real_escape_string($link, strip_tags($_POST['clientShortName'])),
							mysqli_real_escape_string($link, strip_tags(implode($_POST['rei']))),
							mysqli_real_escape_string($link, strip_tags($_POST['subType'])),
							mysqli_real_escape_string($link, strip_tags(implode(",",$_POST['market']))),
							mysqli_real_escape_string($link, strip_tags($_POST['type'])),
							mysqli_real_escape_string($link, strip_tags($prevName)),
							mysqli_real_escape_string($link, strip_tags($_POST['vatNumber'])),
							mysqli_real_escape_string($link, $idEdit));
							$link->query($query);
							echo $query;
							if($_POST["clientLongName"]!=$client['clientLongName'])
							{
								$client=getClientInfo($id);
								if($client['rei']=="E")
								{
									$result=$link->query("SELECT * FROM offers WHERE finalClient='$id'");
									while($row=$result->fetch_object())
									{
										$off=$row->offerNo;
										$res=getClientName($row->client);
										$clientShortName=$client['clientShortName'];
										$link->query("UPDATE stats SET clientShortName='$res / $clientShortName' WHERE offerNo='$off'");
									}
								}
								else
								{
									$result=$link->query("SELECT * FROM offers WHERE client='$clientId'");
									while($row=$result->fetch_object())
									{
										$off=$row->offerNo;
										$end=getClientName($row->finalClient);
										$clientShortName=$replaceClient['clientShortName'];
										$link->query("UPDATE stats SET clientShortName='$clientShortName / $end' WHERE offerNo='$off'");
									}
								}
							}
								/*$clientId=$client['id'];
							switch($client['rei'])
							{
								case 'R':
									if($_POST['rei']!='R'&&$_POST['rei']=='E')
									{
										$result=$link->query("SELECT * FROM offers WHERE client='$idEdit'");
										while($row=$result->fetch_object())
										{
											$off=$row->offerNo;
											$clientShortName=$_POST['clientShortName'];
											$link->query("UPDATE stats SET clientShortName='/ $clientShortName' WHERE offerNo='$off'");
										}
										$link->query("UPDATE offers SET finalClient=client, endclientContactTechnican=technican, endclientContactPurchase=purchase, endclientInquiryNo=inquiryNo, client='', technican='', purchase='', inquiryNo='' WHERE client='$clientId'");
									}
									break;
								case 'E': 
									if($_POST['rei']!='E'&&($_POST['rei']=='R'||$_POST['rei']=='I'))
									{
										$result=$link->query("SELECT * FROM offers WHERE finalClient='$idEdit'");
										while($row=$result->fetch_object())
										{
											$off=$row->offerNo;
											$clientShortName=$_POST['clientShortName'];
											$link->query("UPDATE stats SET clientShortName='$clientShortName /' WHERE offerNo='$off'");
										}
										$link->query("UPDATE offers SET client=finalClient, technican=endclientContactTechnican, purchase=endclientContactPurchase, inquiryNo=endclientInquiryNo, finalClient='', endclientContactTechnican='', endclientContactPurchase='', endclientInquiryNo='' WHERE finalClient='$clientId'");
									}
									break;
								case 'I':
									if($_POST['rei']!='I'&&$_POST['rei']=='E')
									{
										$result=$link->query("SELECT * FROM offers WHERE client='$idEdit'");
										while($row=$result->fetch_object())
										{
											$off=$row->offerNo;
											$clientShortName=$_POST['clientShortName'];
											$link->query("UPDATE stats SET clientShortName='$clientShortName /' WHERE offerNo='$off'");
										}
										$link->query("UPDATE offers SET client=finalClient, technican=endclientContactTechnican, purchase=endclientContactPurchase, inquiryNo=endclientInquiryNo, finalClient='', endclientContactTechnican='', endclientContactPurchase='', endclientInquiryNo='' WHERE finalClient='$clientId'");
									}
									break;
							}*/
							countHitrates($client['id']);
							$link->close();
							header("Location: client-one-management.php?id=".$idEdit."&saveSuccess=true");
						}
						else
							header("Location: client-one-management.php?id=".$idEdit."&saveFail=true");
					}
					if(isset($_POST['addContactPerson']))
					{
						$link=connect();
						$clientid=$client['id'];
						$query=sprintf("INSERT INTO `clientsContacts`(`clientId`, `gender`, `name`, `surname`, `email`, `phone1`, `phone2`, `position`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')", 
							mysqli_real_escape_string($link,$clientid),
							mysqli_real_escape_string($link,strip_tags($_POST['contactGender'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactName'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactSurname'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactEmail'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone1'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone2'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPosition'])));
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$client['id']."&addContactPerson=true");
					}
					if(isset($_POST['saveFollow']))
					{
						$link=connect();
						$link->query($query);
						$cmnt=str_replace("'","`",$_POST['foLastCommenct']);
						$next=$_POST['foNext'];
						$clientId=$client['id'];
						$con=$_POST['foPerson'];
						$data=$_POST['foCurrent'];
						$conW=$_POST['foContactWith'];
						$link->query("INSERT INTO `contactsClients`(`clientId`, `contact`, `contactWith`, `nextContactDate`, `contactDate`, `note`) VALUES ('$clientId','$con','$conW','$next','$data','$cmnt')");
						header("Location: client-one-management.php?id=".$client['id']."&follow=1");
					}
		
					if(isset($_POST['noteSave']))
					{
						$link=connect();
						if(!empty($_POST['notesInput']))
						{
							$query=sprintf("INSERT INTO `commentsClient`(`clientId`, `name`, `surname`, `note`) VALUES ('%s','%s','%s','%s')",
								mysqli_real_escape_string($link, strip_tags($client['id'])),
								mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['imie'])),
								mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['nazwisko'])),
								mysqli_real_escape_string($link, strip_tags(str_replace("'","`",$_POST['notesInput'])))
							);
							$link->query($query);
						}
						$link->close();
						header("Location: client-one-management.php?id=".$client['id']."&noteSuccess=1");
					}
					if(isset($_POST['replaceNow']))
					{
						$link=connect();
						$clientId=$client['id'];
						echo $clientId;
						$replacer=$_POST['replacer'];
						$replaceClient=getClientInfo($replacer);
						$replacerName=$replaceClient['clientLongName'];
						if($replaceClient['rei']=="E")
						{
							$result=$link->query("SELECT * FROM offers WHERE finalClient='$clientId'");
							while($row=$result->fetch_object())
							{
								$off=$row->offerNo;
								$res=getClientName($row->client);
								$clientShortName=$replaceClient['clientShortName'];
								$link->query("UPDATE stats SET clientShortName='$res / $clientShortName' WHERE offerNo='$off'");
							}
							$link->query("UPDATE offers SET finalClient='$replacer', endclientContactTechnican='', endclientContactPurchase='' WHERE finalClient='$clientId'");
						}
						else
						{
							$result=$link->query("SELECT * FROM offers WHERE client='$clientId'");
							while($row=$result->fetch_object())
							{
								$off=$row->offerNo;
								$end=getClientName($row->finalClient);
								$clientShortName=$replaceClient['clientShortName'];
								$link->query("UPDATE stats SET clientShortName='$clientShortName / $end' WHERE offerNo='$off'");
							}
							$link->query("UPDATE offers SET client='$replacer', technican='', purchase='' WHERE client='$clientId'");
						}
						$link->query("UPDATE clients SET isDeleted=1 WHERE id='$clientId'");
						countHitrates($clientId);
						countHitrates($replacer);
						$idUserAction=$_SESSION['plasticonDigitalUser']['id'];
						$ip=$_SERVER['REMOTE_ADDR'];
						$link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Replace client','Replaced ".getClientName($clientId)." with ".getClientName($replacer)."','$ip')");
						header("Location: client-one-management.php?id=".$clientId."&saveSuccess=true");
					}
					if(isset($_POST['delete']))
					{
						$idEdit=strip_tags($_POST['id']);
						$link=connect();
						$query=sprintf("UPDATE clients SET isDeleted='1' WHERE id='%s'",
							mysqli_real_escape_string($link,$idEdit));
						$link->query($query);
						$idUserAction=$_SESSION['plasticonDigitalUser']['id'];
						$link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Delete client','Deleted ".getClientName($client['id'])." with id: ".$client['id']."','$ip')");
						$link->close();
						header("Location: client-management.php?deleteSuccess=true");
					}
					if(isset($_POST['deleteContact']))
					{
						$idEdit=strip_tags($_POST['id']);
						$conId=strip_tags($_POST['contactId']);
						$link=connect();
						$query=sprintf("DELETE FROM clientsContacts WHERE id='%s'",
							mysqli_real_escape_string($link,$conId));
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$idEdit."&deleteContact=true");
					}
					if(isset($_POST['addContact']))
					{
						$idEdit=strip_tags($_POST['id']);
						$link=connect();
						$query=sprintf("INSERT INTO `clientsContacts`(`clientId`, `gender`, `name`, `surname`, `email`, `phone1`, `phone2`, `position`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
							mysqli_real_escape_string($link,$idEdit),
							mysqli_real_escape_string($link,strip_tags($_POST['contactGender'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactName'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactSurname'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactEmail'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone1'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone2'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPosition'])));
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$idEdit."&addContact=true");
					}
					if(isset($_POST['editContact']))
					{
						$idEdit=strip_tags($_POST['id']);
						$idEditCon=strip_tags($_POST['editId']);
						$link=connect();
						$query=sprintf("UPDATE clientsContacts SET `name`='%s', `surname`='%s', `email`='%s', `phone1`='%s', `phone2`='%s', `position`='%s', `gender`='%s', `avaliable`='%s' WHERE `id`='%s'",
							mysqli_real_escape_string($link,strip_tags($_POST['contactNameEdit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactSurnameEdit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactEmailEdit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone1Edit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPhone2Edit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactPositionEdit'])),
							mysqli_real_escape_string($link,strip_tags($_POST['contactGenderEdit'])), 
							mysqli_real_escape_string($link,strip_tags($_POST['avaliableEdit'])),
							mysqli_real_escape_string($link,$idEditCon));
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$idEdit."&editContact=true");
					}
					if(isset($_POST['fuDeleteSubmit']))
					{
						$link=connect();
						$query=sprintf("DELETE FROM contactsClients WHERE id='%s'",
							mysqli_real_escape_string($link,strip_tags($_POST['fuDeleteId']))
						);
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$client['id']."&deletedFU=true");
					}
					if(isset($_POST['saveEditFollow']))
					{
						$link=connect();
						$query=sprintf("UPDATE contactsClients SET contact='%s', contactWith='%s', contactDate='%s', nextContactDate='%s', note='%s', status='%s' WHERE id='%s'",
							mysqli_real_escape_string($link,strip_tags($_POST['foEditPerson'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditContactWith'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditCurrent'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditNext'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditLastCommenct'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditStatus'])),
							mysqli_real_escape_string($link,strip_tags($_POST['foEditId']))
						);
						$link->query($query);
						$link->close();
						header("Location: client-one-management.php?id=".$client['id']."&foEdited=true");
					}
				?>
			</div>
		</div>
	</div>
<?php require('footer.php'); ?>
<script>
	<?php
	if(isset($_GET['openFollow']))
	{
		?>
			$('#followUp').modal("show");							
		<?php
	}
	?>
	var matrix="";
	var platforms="";
	function showPage(page)
	{
		$('.article-menu-item').each(function(){
			$(this).removeClass("article-menu-item-active");
		})
		$('.article-page-item').each(function(){
			$(this).removeClass("article-page-item-active");
		})
		$('#'+page).addClass('article-page-item-active');
		$('#'+page+"-menu").addClass('article-menu-item-active');
		/*$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "saveArtPage",
				page: page,
				user: <?php echo $_SESSION['plasticonDigitalUser']['id']; ?>
			}
		});*/
	}
	function saveMatrix()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "addMatrixRow",
				id: <?php echo $client['id']; ?>,
				company: $("[name='mxCompany']").val(),
				TL: $("[name='mxTL']").val(),
				KAM: $("[name='mxKAM']").val(),
				ASM: $("[name='mxASM']").val()
			},
			success: function(res) {
				if(res=="exists")
					alert("This company is already added to this client's responsibility matrix!")
				else
				{
					matrix.ajax.reload();
					$("#addMatrixRow").modal('hide');
				}
				
			}
		});
	}
	function savePlatform()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "addPlatformRow",
				id: <?php echo $client['id']; ?>,
				desc: $("[name='platformsDescAdd']").val(),
				login: $("[name='platformsLoginAdd']").val(),
				password: $("[name='platformsPasswordAdd']").val()
			},
			success: function(res) {
				platforms.ajax.reload();
				$("#addPlatformRow").modal('hide');
				
			}
		});
	}
	function matrixDelInfo(id)
	{
		$("[name='mxDelId']").val(id);
		$("#deleteMatrixRow").modal("show");
	}
	function platformsDelInfo(id)
	{
		$("[name='platformsDelId']").val(id);
		$("#deletePlatformRow").modal("show");
	}
	function saveDelMatrix()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "saveDelMatrix",
				id: $("[name='mxDelId']").val()
			},
			success: function(res) {
				matrix.ajax.reload();
				$("#deleteMatrixRow").modal('hide');
			}
		});
	}
	function saveDelPlatform()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "saveDelPlatform",
				id: $("[name='platformsDelId']").val()
			},
			success: function(res) {
				platforms.ajax.reload();
				$("#deletePlatformRow").modal('hide');
			}
		});
	}
	function matrixEditInfo(id)
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "getMatrixInfo",
				id: id
			},
			success: function(res) {
				data=JSON.parse(res);
				$("[name='mxEditId']").val(id);
				$("[name='mxCompanyE']").val(data.company).trigger('change');
				if(data.TL==0)
					$("[name='mxTLE']").val("").trigger('change');
				else
					$("[name='mxTLE']").val(data.TL).trigger('change');
				if(data.KAM==0)
					$("[name='mxKAME']").val("").trigger('change');
				else
					$("[name='mxKAME']").val(data.KAM).trigger('change');
				if(data.ASM==0)
					$("[name='mxASME']").val("").trigger('change');
				else
					$("[name='mxASME']").val(data.ASM).trigger('change');
				$("#editMatrixRow").modal("show");
			}
		});
	}
	function platformsEditInfo(id)
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "getPlatformInfo",
				id: id
			},
			success: function(res) {
				data=JSON.parse(res);
				$("[name='platformsEditId']").val(id);
				$("[name='platformsDesc']").val(data.desc);
				$("[name='platformsLogin']").val(data.login);
				$("[name='platformsPassword']").val(data.password);
				$("#editPlatformRow").modal("show");
			}
		});
	}
	function saveEditMatrix()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "saveEditMatrix",
				id: <?php echo $client['id']; ?>,
				company: $("[name='mxCompanyE']").val(),
				TL: $("[name='mxTLE']").val(),
				KAM: $("[name='mxKAME']").val(),
				ASM: $("[name='mxASME']").val(),
				editId: $("[name='mxEditId']").val()
			},
			success: function(res) {
				if(res=="exists")
					alert("This company is already added to this client's responsibility matrix! Changes were not saved.")
				else
				{
					matrix.ajax.reload();
					$("#editMatrixRow").modal('hide');
				}
				
			}
		});
	}
	function saveEditPlatform()
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php',
			data: {
				action: "saveEditPlatform",
				id: <?php echo $client['id']; ?>,
				desc: $("[name='platformsDesc']").val(),
				login: $("[name='platformsLogin']").val(),
				password: $("[name='platformsPassword']").val(),
				editId: $("[name='platformsEditId']").val()
			},
			success: function(res) {
				platforms.ajax.reload();
				$("#editPlatformRow").modal('hide');
			}
		});
	}
	function formatNote ( d, id, note ) {
		note=note.split("[-]");
		var rtn='';
			rtn+='<table class="control-table" style="border:0px;width:100%">'+
			'<tr class="suboffers-row" style="border:0;"><td class="text-left">Contact with '+note[1]+'</td></tr>'+
			'<tr class="suboffers-row"><td class="text-left">'+note[0]+'</td></tr></table>';
		return rtn;
	}	
	function setDeleteValues(offer, text)
	{
		$('#deleteIdSpanTittle').html(text);
		$('#deleteId').val(offer);
	}	
	function setEditValues(data)
	{
		data=data.split("[-]");
		$('#contactTitle').html(data[1]+" "+data[2]);
		$('#contactNameEdit').val(data[1]);
		$('#contactSurnameEdit').val(data[2]);
		$('#contactEmailEdit').val(data[3]);
		$('#contactPhone1Edit').val(data[4]);
		$('#contactPhone2Edit').val(data[5]);
		$('#contactPositionEdit').val(data[6]);
		$('#contactGenderEdit').val(data[8]).trigger('change');
		if(data[7]==1)
			$('#avaliableEdit').prop("checked", true);
		else
			$('#avaliableEdit').prop("checked", false);
		$('#editId').val(data[0]);
	}
	function readFU(id)
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php?action=readFU&id='+id,
			success: function(note) {
				$("#fuReadHTML").html(note);
				$("#followUpRead").modal("show");
			}
		});
	}
	function editFU(id)
	{
		$.ajax({
			type: 'get',
			url: 'assets/php/ajaxHandeler.php?action=editFU&id='+id,
			success: function(data) {
				data=JSON.parse(data);
				$('[name="foEditContactWith"]').val(data['contactWith']).trigger('change');
				$('[name="foEditCurrent"]').val(data['contactDate']);
				$('[name="foEditNext"]').val(data['nextContactDate']);
				$('[name="foEditStatus"]').val(data['status']).trigger('change');
				$('[name="foEditLastCommenct"]').html(data['note']);
				$('[name="foEditId"]').val(data['id']);
				$('[name="foEditPerson"]').val(data['contact']).trigger('change');
				$("#followUpEdit").modal("show");
			}
		});
	}
	function deleteFU(id)
	{
		$("#fuDeleteId").val(id);
		$("#fuDeleteIdTitle").html(id);
		$("#followUpDelete").modal("show");
	}
	$(document).ready(function() {
		showPage('general');
		platforms = $('#platforms').DataTable({
			"iDisplayLength": 10,
			stateSave: true,
			"stateDuration": 0,
			"ordering": false,
			"searching": false,
			"paging": false,
			"info": false,
			"processing": true,
			"serverSide": true,
			'ajax': {
				'url':'assets/php/platforms-processing.php',
				type: "POST",
				data: {
					id: <?php echo $client['id']; ?>
				},
			}, 
			'columns':[
				{ data: 'desc', name: 'desc' },
				{ data: 'login', name: 'login' },
				{ data: 'password', name: 'password' },
				{ data: 'actions', name: 'actions' },
			],
			"drawCallback":function(){
				$(".togglePassword").click(function(){
					var th=$(this);
					var elem=$(this).prev();
					if($(elem).attr("type")=="password")
					{
						$(th).html('<i class="fas fa-eye-slash pointer"></i>');
						$(elem).attr('type', 'text');
					}
					else
					{
						$(th).html('<i class="fas fa-eye pointer"></i>');
						$(elem).attr('type', 'password');
					}
				})
			}
		});
		matrix = $('#matrix').DataTable({
			"iDisplayLength": 10,
			stateSave: true,
			"stateDuration": 0,
			"ordering": false,
			"searching": false,
			"paging": false,
			"info": false,
			"processing": true,
			"serverSide": true,
			'ajax': {
				'url':'assets/php/matrix-processing.php',
				type: "POST",
				data: {
					id: <?php echo $client['id']; ?>
				},
			}, 
			'columns':[
				{ data: 'company', name: 'company' },
				{ data: 'inTL', name: 'inTL' },
				{ data: 'inKAM', name: 'inKAM' },
				{ data: 'inASM', name: 'inASM' },
				{ data: 'actions', name: 'actions' },
			],
		});
		function reloadComments(id)
		{
			$.ajax({
				type: 'get',
				url: 'assets/php/ajaxHandeler.php?action=getComments&id='+id+"&case=client",
				success: function(comments) {
					$(".comments-list-box").html(comments);
				}
			});
		}
		reloadComments(<?php echo $client['id']; ?>);
		var saveComment = function(data) {

			// Convert pings to human readable format
			$(Object.keys(data.pings)).each(function(index, userId) {
				var fullname = data.pings[userId];
				var pingText = '@' + fullname;
				data.content = data.content.replace(new RegExp('@' + userId, 'g'), pingText);
			});

			return data;
		}
		<?php if($_SESSION['plasticonDigitalUser']['crm']['externalCompany']==0) { ?>
		$('#commentsBox').comments({
			currentUserId: <?php echo $_SESSION['plasticonDigitalUser']['id']; ?>,
			roundProfilePictures: false,
			textareaRows: 1,
			enableAttachments: false,
			enableHashtags: false,
			enablePinging: true,
			scrollContainer: $(window),
			searchUsers: function(term, success, error) {
				$.ajax({
					type: 'get',
					url: 'assets/php/ajaxHandeler.php?action=getUsersArray&user=' + term,
					success: function(userArray) {
						var arr=JSON.parse(userArray);
						success(arr)
					},
					error: error
				});
			},
			postComment: function(data, success, error) {
				var users=[];
				for (var key in data.pings) {
					users.push(key);
				}
				users=users.join(";");
				$.ajax({
					type: 'get',
					url: 'assets/php/ajaxHandeler.php?action=insertComment&content='+encodeURIComponent(data.content)+'&userId='+users+'&creator='+<?php echo $_SESSION['plasticonDigitalUser']['id']; ?>+'&caseId='+<?php echo $client['id']; ?>+'&caseName=client',
					success: function(response) {
						console.log(response);
						success(saveComment(data));
						reloadComments(<?php echo $client['id']; ?>);
					},
					error: error
				});
			}
		});
		<?php } ?>
		function daysInMonth(year, month)
		{
			return new Date(year, month + 1, 0).getDate();
		}
		Date.prototype.addMonths = function (m) {
			var d = new Date(this);
			var years = Math.floor(m / 12);
			var months = m - (years * 12);
			if (years) d.setFullYear(d.getFullYear() + years);
			if (months) d.setMonth(d.getMonth() + months);
			var days=d.getDate()+1;
			var md=daysInMonth(d.getFullYear(), d.getMonth());
			if(md<days)
				d.setDate(md);
			return d;
		}
		$('.addMonths').click(function(){
			var amount=$(this).attr("amount");
			//var data=$('[name="foNext"]').val(); DODAJE DO AKTUALNEJ Z POLA
			var data='<?php echo date("Y-m-d"); ?>';
			console.log(data+" "+amount)
			data=data.split("-");
			data=new Date(data[0],data[1]-1,data[2]);
			data=data.addMonths(amount);
			var m = data.getUTCMonth() + 1; //months from 1-12
			var d = data.getUTCDate(); 
			var y = data.getUTCFullYear();
			console.log(data);
			if(m<=9)
				m=0+""+m;
			if(d<=9)
				d=0+""+d;
			$('[name="foNext"]').val(y+"-"+m+"-"+d);
			$('[name="acNext"]').val(y+"-"+m+"-"+d);
		})
		this.$slideOut = $('#slideOut');
		// Slideout show
		this.$slideOut.find('.slideOutTab').on('click', function() {
		  $("#slideOut").toggleClass('showSlideOut');
		});
		$('.select2').select2();
		$(".select2-client").select2({
			allowClear: true,
			placeholder: 'Select',
			ajax: {
				method: "GET",
				url: "assets/php/ajaxHandeler.php",
				dataType: 'json',
				delay: 250,
				data: function(params) {
					return {
						name: params.term,
						action: 'getClients',
						rei: '<?php echo $client["rei"] ?>'
					};
				},
				processResults: function(data, params) {
					var resData = [];
					data.forEach(function(value) {
						if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
							resData.push(value)
					})
					return {
						results: $.map(resData, function(item) {
							return {
								text: item.longName,
								id: item.id
							}
						})
					};
				},
				cache: true
			},
			minimumInputLength: 3
		})
		var table = $('#clietsContacts').DataTable({
			"iDisplayLength": 10,
			stateSave: true,
			"stateDuration": 0,
			"columnDefs": [
				{"orderable": false, "targets": 7}
			],
			"order": [[ 0, "desc" ]]
		});
		var table2 = $('#clientsOffers').DataTable({
			"iDisplayLength": 50,
			stateSave: true,
			"stateDuration": 0,
			"columnDefs": [
				{"orderable": false, "targets": 0},
				{ "targets": 0, "createdCell": function (td, cellData, rowData, row, col) {
					c=cellData.split("[-]");
						$(td).parent().addClass('pointer').attr("title","Click to open").attr("onclick","(location.href='offer.php?id="+c[1]+"')");
					$(td).html(c[0]);
				} },
				{ "targets": [7], "createdCell": function (td, cellData, rowData, row, col) {
					data=cellData.split("[-]");
					if(data[1]==""||data[1]==null)
					{
						if(data[2]=="Past")
						{
							$(td).css("background-color","rgba(255,0,0,0.5)").html(data[0]);
						}
						if(data[0]==null||data[0]==""||data[0]=="0"||data[0]=="0%"||data[0]=="0&euro;"||data[0]=='0000-00-00')
						{
							$(td).addClass('emptyBox');
						}
					}
					$(td).html(data[0]);
				} },
			],
			dom: "<'row'<'col-sm-3'l><'col-sm-3 filterClientOffers'><'col-sm-2'><'col-sm-1'><'col-sm-3'f>>" +
				"<'row'<'col-sm-12'tr>>" +
				"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			"order": [[ 1, "desc" ]],
			"processing": true,
			"serverSide": true,
			'ajax': {
				'url':'assets/php/clientOffersProcessing.php',
				type: "POST",
				"data": {
					"client": <?php echo $id; ?>,
					"status": function ( d ) { return  $('#filterCO').val()},
				}, 
			},
			'columns':[
				{ data: 'conDate', name: 'conDate', className: 'text-center tableS' },
				{ data: 'offerNo', name: 'offerNo' },
				{ data: 'SOC', name: 'SOC', className: 'text-right fixWidth' },
				{ data: 'OT', name: 'OT', className: 'fixWidth' },
				{ data: 'scope', name: 'scope' },
				{ data: 'InR', name: 'InR', className: 'fixWidth' },
				{ data: 'company', name: 'company', className: 'fixWidth' },
				{ data: 'requestedOrderDate', name: 'requestedOrderDate', className: 'datesWidth' }, 
				{ data: 'order', name: 'order', className: 'datesWidth' }, 
				{ data: 'OVE', name: 'OVE', className: "text-right" },
				{ data: 'gxg', name: 'gxg', className: "text-right fixWidth" },
				{ data: 'nextContactDate', name: 'nextContactDate', className: 'datesWidth' },
				{ data: 'lastComment', name: 'lastComment', className: 'tableSB ellipsis' }, 
			],
			"stateSaveParams": function (settings, data) {
				data.status = $('#filterCO').val();
			},
			"drawCallback":function(){
				$("td").mouseenter(function(){
					//if(!$(this).hasClass("w30"))
					$(this).attr("title", $(this).context.innerHTML.replace(/(<([^>]+)>)/gi, ""));
				})
			}
		});
		var conTable=$('#followups').DataTable({
			"iDisplayLength": 50,
			"order": [[ 5, "asc" ]],
			stateSave: true,
			"stateDuration": 0,
			"columnDefs": [
				{"orderable": false, "targets": 7},
			],
			"processing": true,
			"serverSide": true,
			'ajax': {
				'url':'assets/php/contactsClientProcessing.php',
				type: "POST",
				"data": {
					"clientId": "<?php echo $client['id']; ?>",
					"status": function ( d ) { return  $('#filterFO').val()},
				},
			},
			"stateSaveParams": function (settings, data) {
				data.status = $('#filterFO').val();
			},
			dom: 	"<'row'<'col-sm-3'l><'col-sm-3 filterFollowUps'><'col-sm-2'><'col-sm-1'><'col-sm-3'f>>" +
					"<'row'<'col-sm-12'tr>>" +
					"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			'columns':[
				{ data: 'status', name: 'status', className: 'tableS text-center' },
				{ data: 'id', name: 'id', className: 'tableS' },
				{ data: 'contact', name: 'contact', className: 'tableB' },
				{ data: 'contactWith', name: 'contactWith', className: 'tableB' },
				{ data: 'contactDate', name: 'contactDate', className: 'tableB' },
				{ data: 'nextContactDate', name: 'nextContactDate', className: 'tableB' },
				{ data: 'note', name: 'note' },
				{ data: 'actions', name: 'actions', className: 'tableB' },
			],
		});
		$('.filterFollowUps').html("<label>Status: <select class='form-control form-control-sm' style='width:80px;display:inline-block;' id='filterFO'><option value='all' selected>All</option><option value='Open'>Open</option><option value='Finished'>Closed</option></select></label>");
		$('.filterClientOffers').html("<label>Status: <select class='form-control form-control-sm' style='width:80px;display:inline-block;' id='filterCO'><option value='all' selected>All</option><option value='Open'>Open</option><option value='Finished'>Closed</option></select></label>");
		$('#filterFO').change(function(){
			conTable.ajax.reload();
		})
		$('#filterCO').change(function(){
			table2.ajax.reload();
		})
		if(conTable.state.loaded())
			$('#filterFO').val(conTable.state.loaded().status).trigger('change');
		if(table2.state.loaded())
			$('#filterCO').val(table2.state.loaded().status).trigger('change');
		$('#countries').val("<?php echo $client['country']; ?>").trigger("change");
		$('#countriesInv').val("<?php echo $client['invCountry']; ?>").trigger("change");
		$('#rei').val([
			<?php
				foreach(str_split($client['rei']) as $reiOne)
					echo '"'.$reiOne.'"'.",";
			?>
		]).trigger("change");
		$('#market').val([
			<?php
				foreach(explode(",",$client['market']) as $market)
					echo '"'.$market.'"'.",";
			?>
		]).trigger("change");
		$('#subType').val("<?php echo $client['subType']; ?>").trigger("change");
		$('[name="followUpClient"]').val("<?php echo $client['followUp']; ?>").trigger("change");
		table.draw();
	});
</script>