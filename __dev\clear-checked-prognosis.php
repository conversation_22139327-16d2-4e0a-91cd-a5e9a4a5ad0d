<?php

require(__DIR__ . '/../db-config.php');
// require(MAIN_DIR_GLOBAL . 'assets/functions.php');
require(MAIN_DIR_GLOBAL . 'crm/assets/php/better-functions.php');

ob_start();
session_name("plasticonapp");
session_start([
    "cookie_domain" => COOKIE_DOMAIN_CFG,
    "cookie_path" => "/"
]);

header('Content-Type: application/json');

/*
 * CHECK SESSION MAIN SUBKEYS:
 * plasticonDigitalUserKey
 * id
 */
if ($_SESSION['plasticonDigitalUser']['plasticonDigitalUserKey'] != '0QYasdas34233212as42q3244dsqeZIasddigitasdaser2wr4324rwsIC10ruotest' || !is_numeric($_SESSION['plasticonDigitalUser']['id']) && $_SESSION['plasticonDigitalUser']['developer'] == 1) {
    header("Location: login.php");

    echo json_encode([
        "status" => "error",
        "message" => "Invalid session. Please log in again.",
        "cookieDomain" => COOKIE_DOMAIN_CFG,
        "session" => $_SESSION,
    ]);
}

// Pobierz dane z body (JSON)
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Oczekujemy, że tablica numerów ofert będzie pod kluczem 'offerNos'
if (isset($data['offerNos']) && is_array($data['offerNos'])) {
    $array = $data['offerNos'];
} else {
    $array = [];
}

function connectOMS()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_OMS;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);

    try {
        $link->query('SET NAMES utf8');
        $link->query('SET CHARACTER_SET utf8_unicode_ci');
        $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
        $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
        if ($link->connect_error) {
            throw new Exception("Connection failed: " . $link->connect_error);
        }
        return $link;
    } catch (Exception $e) {
        consoleLog("Connection error: " . $e->getMessage(), "__clear-checked-prognosis");
        return null;
    }
}

function findOfferIdByOfferNo($offerNo)
{
    try {
        $link = connectOMS();

        $query = "SELECT id FROM salesarticles WHERE offerNo = '$offerNo'";

        $result = $link->query($query);

        if (!$result) {
            consoleLog("Query failed: " . $link->error . " for offer number: " . $offerNo, "__clear-checked-prognosis");

            return null;
        }

        if ($link->error) {
            consoleLog("Query error: " . $link->error . " for offer number: " . $offerNo, "__clear-checked-prognosis");
            return null;
        }

        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();

            return $row['id'];
        } else {
            consoleLog("No offer found for offer number: " . $offerNo, "__clear-checked-prognosis");
            return null;
        }
    } catch (Exception $e) {
        consoleLog("Connection error: " . $e->getMessage(), "__clear-checked-prognosis");
        return null;
    } finally {
        if (isset($link) && $link instanceof mysqli) {
            $link->close();
        }
    }
}

function findPrognosisByArticleId($id)
{
    try {
        $link = connectOMS();

        if ($link === null) {
            consoleLog("Failed to connect to the database.", "__clear-checked-prognosis");
            return null;
        }

        $query = "SELECT * FROM prognosis WHERE articleId = '$id' LIMIT 1";

        $result = $link->query($query);

        if (!$result) {
            consoleLog("Query failed: " . $link->error . " for offer ID: " . $id, "__clear-checked-prognosis");
            return null;
        }

        if ($result && $result->num_rows > 0) {
            return $result->fetch_assoc();
        } else {
            return null;
        }
    } catch (Exception $e) {
        consoleLog("Error finding prognosis by offer ID: " . $id, "__clear-checked-prognosis");
        return null;
    } finally {
        if (isset($link) && $link instanceof mysqli) {
            $link->close();
        }
    }
}

function removeFromCheckedPrognosisByArticleId($id)
{
    try {
        $link = connectOMS();

        if ($link === null) {
            consoleLog("Failed to connect to the database.", "__clear-checked-prognosis");
            return;
        }

        $query = "DELETE FROM cron_first_prognosis_salesarticles_check WHERE id_article = '$id'";

        if (!$link->query($query)) {
            consoleLog("Query failed: " . $link->error . " for article ID: " . $id, "__clear-checked-prognosis");
        } else {
            consoleLog("Successfully removed checked prognosis for article ID: " . $id, "__clear-checked-prognosis");
        }
    } catch (Exception $e) {
        consoleLog("Error removing checked prognosis by article ID: " . $id, "__clear-checked-prognosis");
    } finally {
        if (isset($link) && $link instanceof mysqli) {
            $link->close();
        }
    }
}

function checkedPrognosisByOfferNo($array)
{
    $articleIds = [];
    $removedIds = [];

    foreach ($array as $offerNo) {
        $articleId = findOfferIdByOfferNo($offerNo);

        if ($articleId === null) {
            consoleLog("Offer ID not found for offer number: " . $offerNo, "__clear-checked-prognosis");
            continue;
        }

        if ($articleId !== null) {
            $prognosis = findPrognosisByArticleId($articleId);

            if ($prognosis === null) {
                $articleIds[] = $articleId;
            } else {
                continue;
            }
        }
    }

    foreach ($articleIds as $id) {
        removeFromCheckedPrognosisByArticleId($id);
        $removedIds[] = $id;
    }


    echo json_encode([
        "status" => "success",
        "message" => "Checked prognosis removed successfully for the provided offer numbers.",
        "removedIds" => $removedIds,
        "cookieDomain" => COOKIE_DOMAIN_CFG,
        "sessionId" => $_SESSION,
    ]);
}

checkedPrognosisByOfferNo($array);
