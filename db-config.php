<?php
// Tu ma być PUSTE! Łukasz do Ciebie mówię/piszę
$devName = ''; // artur, marcin, lukasz, konrad, '', 'PRODUCTION', 'DEVELOPMENT', Tomasz

$adminEmail = "<EMAIL>";
$port_panel = '3306';

/*
 * CHECK SERVER VARIABLE - $_SERVER['SERVER_ADMIN']
 */
$serverAdmin = filter_input(INPUT_SERVER, "SERVER_ADMIN");
switch ($serverAdmin):
    case "t.b<PERSON><PERSON><PERSON><PERSON>@plasticoncomposites.com":
        $devName = $serverAdmin;
        break;
    case "<EMAIL>":
        $devName = $serverAdmin;
        break;
    case "k.lewan<PERSON><PERSON>@plasticoncomposites.com":
        $devName = $serverAdmin;
        break;
    case '<EMAIL>':
        $devName = 'DEVELOPMENT';
        break;
    default:
        $devName = 'PRODUCTION';
        break;
endswitch;
/*
 * END CHECK SERVER VARIABLE `SERVER_ADMIN`
 */

if ($devName == "PRODUCTION") {
    $user = 'plasticondb';
    $password = 'KDFS@fdns23%msd0';
    $port = '3306';
    $intranetLink = 'https://intranet.plasticon.app';
    $omsLink = 'https://oms.plasticon.app';
    $crmLink = 'https://crm.plasticon.app';
    $gceLink = 'https://gce.plasticon.app';
    $wtcLink = 'https://wtc.plasticon.app';
    $wpcLink = 'https://wpc.plasticon.app';
    $hmsLink = 'https://hms.plasticon.app';
    $rmsLink = 'https://rms.plasticon.app';
    $mmsLink = 'https://mms.plasticon.app';
    $pmsLink = 'https://pms.plasticon.app';
    $marketingLink = 'https://intranet.plasticon.app/marketing.php';
    $schedulerLink = "https://scheduler.plasticon.app";
    $plasticOnLink = 'https://plastic-on.com';
    $helpdeskLink = 'https://helpdesk.plasticon.com.pl/login.php?language=English';
    $instaTankLink = 'https://photo.plasticon.app';
    $hostPath = 'https://intranet.plasticon.app';
    $cookieDomain = '.plasticon.app';
    $emailItTeam = '<EMAIL>';

    // PANEL
    $host_panel = "sql.plasticon.com.pl";
    $user_panel = "01023895_panel";
    $password_panel = "UXN6PVsZ5GUQtdoEupgCaF4FW";
    $name_panel = "01023895_panel";
    $port_panel = '3306';

    $emailAvailability = true;
    $tabMailObject = ["files" => [
        '<EMAIL>',
        '<EMAIL>'
    ]];

    $env = "PROD";
}

if ($devName == "DEVELOPMENT") {
    $user = 'root';
    $password = 'qsomskJl/Xvp7o8U';
    $port = '3306';
    $intranetLink = 'http://************:8082';
    $omsLink = 'http://************:8084';
    $crmLink = 'http://************:8083';
    $gceLink = 'http://************:8093';
    $wtcLink = 'http://************:8087';
    $wpcLink = 'http://************:8086';
    $hmsLink = 'http://************:8089';
    $rmsLink = 'http://************:8088';
    $mmsLink = 'http://************:8085';
    $pmsLink = '';
    $marketingLink = 'http://************:8082/marketing.php';
    $schedulerLink = 'http://************:8094';
    $plasticOnLink = '';
    $helpdeskLink = 'https://helpdesk.plasticon.com.pl/';
    $instaTankLink = 'http://************:8095';
    $hostPath = 'http://************:8082';
    $cookieDomain = '************';
    $emailItTeam = '<EMAIL>';

    // PANEL
    $host_panel = "sql.plasticon.com.pl";
    $user_panel = "01023895_paneldev";
    $password_panel = "Dhwq9NYAIPx95LVcS7X5oyqFtg";
    $name_panel = "01023895_paneldev";
    $port_panel = '3306';

    $emailAvailability = false;
    $tabMailObject = ["files" => ['<EMAIL>']];

    $env = "DEV";
}

// Use include guard to prevent multiple inclusions
if (!defined('INCLUDED_DB_CONFIG')) {
    define('INCLUDED_DB_CONFIG', true);

    // Example for DEV_NAME
    if (!defined('DEV_NAME')) {
        define('DEV_NAME', $devName);
    }

    // Artur's local machine config
    if (DEV_NAME === 'artur') {
        $user = 'root';
        $password = 'krzemowa';
        $port = '4455';
        $protocol = "http";
        $domain = "localhost";

        $intranetLink = "$protocol://$domain/plastinet";
        $omsLink = "$protocol://$domain/plastinet/oms";
        $crmLink = "$protocol://$domain/plastinet/crm";
        $gceLink = "$protocol://$domain/plastinet/gce";
        $wtcLink = "$protocol://$domain/plastinet/wtc";
        $wpcLink = "$protocol://$domain/plastinet/wpc";
        $hmsLink = "$protocol://$domain/plastinet/hms";
        $rmsLink = "$protocol://$domain/plastinet/rms";
        $mmsLink = "$protocol://$domain/plastinet/mms";
        $pmsLink = "$protocol://$domain/plastinet/pms";
        $marketingLink = "$protocol://$domain/plastinet/marketing";
        $plasticOnLink = "$protocol://$domain/plasticoncomposites/";
        $helpdeskLink = 'https://helpdesk.plasticon.com.pl/';
        $instaTankLink = "";
        $hostPath = "$protocol://$domain/plastinet/";
        $cookieDomain = "localhost";
    }

    // Marcin's local machine config
    if (DEV_NAME === 'marcin') {
        $user = '';
        $password = '';
        $port = '3306';
        $intranetLink = '';
        $omsLink = '';
        $crmLink = '';
        $hostPath = '';
    }

    // Lukasz's local machine config
    if (DEV_NAME === '<EMAIL>') {
        $protocol = "http";
        $domain = "localhost";

        $user = 'root';
        $password = 'Luk@s#1024';
        $port = '3306';
        $intranetLink = "http://plastinet.plasticonapp.local";
        $omsLink = "http://oms.plasticonapp.local";
        $crmLink = "http://crm.plasticonapp.local";
        $gceLink = "$protocol://$domain/plastinet/gce";
        $wtcLink = "$protocol://$domain/plastinet/wtc";
        $wpcLink = "$protocol://$domain/plastinet/wpc";
        $hmsLink = "$protocol://$domain/plastinet/hms";
        $rmsLink = "$protocol://$domain/plastinet/rms";
        $mmsLink = "$protocol://$domain/plastinet/mms";
        $pmsLink = "$protocol://$domain/plastinet/pms";
        $plasticOnLink = "$protocol://$domain/plasticoncomposites/";
        $helpdeskLink = 'https://helpdesk.plasticon.com.pl/';
        $instaTankLink = "";
        $hostPath = "http://plastinet.plasticonapp.local";
        $cookieDomain = ".plasticonapp.local";

        $env = "LOCAL";
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        error_reporting(0);
        $emailAvailability = false;
    }

    // Konrad's local machine config
    if (DEV_NAME === '<EMAIL>') {
        $protocol = "http";
        $host = "localhost";
        $domain = "plastinet";

        $user = 'konrad';
        $password = 'password';
        $port = '3306';
        $intranetLink = "$protocol://$host/$domain";
        $omsLink = "$protocol://$host/$domain/oms";
        $crmLink = "$protocol://$host/$domain/crm";
        $gceLink = "$protocol://$host/$domain/gce";
        $wtcLink = "$protocol://$host/$domain/wtc";
        $wpcLink = "$protocol://$host/$domain/wpc";
        $hmsLink = "$protocol://$host/$domain/hms";
        $rmsLink = "$protocol://$host/plasticonrms";
        $mmsLink = "$protocol://$host/$domain/mms";
        $pmsLink = "$protocol://$host/$domain/pms";
        $marketingLink = "$protocol://$domain/plastinet/marketing.php";
        $schedulerLink = "$protocol://$host/scheduler";
        $plasticOnLink = "$protocol://$host/$domain/plasticoncomposites/";
        $helpdeskLink = 'https://helpdesk.plasticon.com.pl/';
        $instaTankLink = "";
        $hostPath = "$protocol://$host/$domain/";
        $cookieDomain = "localhost";
        $emailItTeam = '<EMAIL>';

        $host_panel = "localhost";
        $user_panel = "konrad";
        $password_panel = "password";
        $name_panel = "panel";

        $emailAvailability = false;
        $tabMailObject = ["files" => ['<EMAIL>']];

        $env = "LOCAL";
        // error_reporting(E_ALL);
    }
    if (DEV_NAME === '<EMAIL>') {
        $user = 'root';
        $password = 'krzemowa';
        $port = '4455';
        $intranetLink = "http://plastinet.plasticonapp.local";
        $omsLink = "http://oms.plasticonapp.local";
        $crmLink = "http://crm.plasticonapp.local";
        $gceLink = "HTTP://gce.local";
        $gceLink = 'https://gce.plasticon.app';
        $wtcLink = "HTTP://wtc.local";
        $wpcLink = "HTTP://wpc.local";
        $hmsLink = "HTTP://hms.local";
        $rmsLink = "HTTP://rms.local";
        $mmsLink = "HTTP://plasticonmms.local";
        $pmsLink = "HTTP://plasticonpms.local";
        $marketingLink = "http://plastinet.plasticonapp.local/marketing.php";
        $schedulerLink = "http://scheduler.local";
        $plasticOnLink = "HTTP://plastic-on.local/";
        $helpdeskLink = 'https://helpdesk.plasticon.com.pl/';
        $instaTankLink = "HTTP://photo.local";
        $hostPath = "http://plastinet.plasticonapp.local";
        $cookieDomain = ".plasticonapp.local";
        $emailItTeam = '<EMAIL>';

        $host_panel = "localhost";
        $user_panel = "root";
        $password_panel = "krzemowa";
        $name_panel = "01023895_panel";
        $port_panel = '4455';

        $emailAvailability = false;
        $tabMailObject = ["files" => ['<EMAIL>']];

        $env = "LOCAL";
        // error_reporting(E_ALL);
    }
    if (!defined('DB_PORT')) {
        define('DB_PORT', $port);
    }
    // Example for DB_HOST
    if (!defined('DB_HOST')) {
        define('DB_HOST', 'localhost');
    }

    // Example for DB_USER
    if (!defined('DB_USER')) {
        define('DB_USER', $user);
    }

    // Example for DB_PW
    if (!defined('DB_PW')) {
        define('DB_PW', $password);
    }

    // Example for DB_OMS
    if (!defined('DB_OMS')) {
        define('DB_OMS', 'oms');
    }

    // Example for DB_USERS
    if (!defined('DB_USERS')) {
        define('DB_USERS', 'users');
    }

    // Example for DB_CRM
    if (!defined('DB_CRM')) {
        define('DB_CRM', 'offers');
    }

    // Example for DB_NEWS
    if (!defined('DB_NEWS')) {
        define('DB_NEWS', 'news');
    }

    // Example for DB_IMAGES
    if (!defined('DB_IMAGES')) {
        define('DB_IMAGES', 'images');
    }

    // Example for DB_MMS
    if (!defined('DB_MMS')) {
        define('DB_MMS', 'mms');
    }

    // Example for DB_MILESTONES
    if (!defined('DB_MILESTONES')) {
        define('DB_MILESTONES', 'milestones');
    }

    if (!defined('DB_GENERALDATA')) {
        define('DB_GENERALDATA', 'generaldata');
    }

    // Example for DB_MARKETING
    if (!defined('DB_MARKETING')) {
        define('DB_MARKETING', 'marketing');
    }

    // Example for DB_PRC
    if (!defined("DB_PRC")) {
        define("DB_PRC", "prc");
    }

    // Example for DB_CHANGELOG
    if (!defined("DB_CHANGELOG")) {
        define("DB_CHANGELOG", "changelog");
    }

    // Example for ENV
    if (!defined('ENV')) {
        define('ENV', $env);
    }

    // Example for COOKIE_DOMAIN_CFG
    if (!defined('COOKIE_DOMAIN_CFG')) {
        define('COOKIE_DOMAIN_CFG', $cookieDomain);
    }

    // Example for INTRANET_LINK
    if (!defined('INTRANET_LINK')) {
        define('INTRANET_LINK', $intranetLink);
    }

    // Example for OMS_LINK
    if (!defined('OMS_LINK')) {
        define('OMS_LINK', $omsLink);
    }

    // Example for CRM_LINK
    if (!defined('CRM_LINK')) {
        define('CRM_LINK', $crmLink);
    }

    if (!defined('GCE_LINK')) {
        define('GCE_LINK', $gceLink);
    }

    if (!defined('WTC_LINK')) {
        define('WTC_LINK', $wtcLink);
    }

    if (!defined('WPC_LINK')) {
        define('WPC_LINK', $wpcLink);
    }

    if (!defined('HMS_LINK')) {
        define('HMS_LINK', $hmsLink);
    }

    if (!defined('RMS_LINK')) {
        define('RMS_LINK', $rmsLink);
    }

    if (!defined('PLASTIC_ON_LINK')) {
        define('PLASTIC_ON_LINK', $plasticOnLink);
    }

    if (!defined('HELPDESK_LINK')) {
        define('HELPDESK_LINK', $helpdeskLink);
    }

    if (!defined('RMS_LINK')) {
        define('RMS_LINK', $rmsLink);
    }

    if (!defined('PMS_LINK')) {
        define('PMS_LINK', $pmsLink);
    }

    if (!defined('INSTA_TANK_LINK')) {
        define('INSTA_TANK_LINK', $instaTankLink);
    }

    if (!defined('MMS_LINK')) {
        define('MMS_LINK', $mmsLink);
    }

    if (!defined('SCHEDULER_LINK')) {
        define('SCHEDULER_LINK', $schedulerLink);
    }

    // Example for HOST_PATH
    if (!defined('HOST_PATH')) {
        define('HOST_PATH', $hostPath);
    }

    // PANEL 
    if (!defined('DB_HOST_PANEL')) {
        define("DB_HOST_PANEL", $host_panel);
    }

    if (!defined('DB_USER_PANEL')) {
        define("DB_USER_PANEL", $user_panel);
    }

    if (!defined('DB_PASSWORD_PANEL')) {
        define("DB_PASSWORD_PANEL", $password_panel);
    }

    if (!defined('DB_NAME_PANEL')) {
        define("DB_NAME_PANEL", $name_panel);
    }
    if (!defined('DB_PORT_PANEL')) {
        define("DB_PORT_PANEL", $port_panel);
    }
}

// Additional constants without include guard
if (!defined('ADMIN_EMAIL')) {
    define('ADMIN_EMAIL', $adminEmail);
}

if (!defined('INTRANET_LINK')) {
    define("INTRANET_LINK", $intranetLink);
}

if (!defined('OMS_LINK')) {
    define("OMS_LINK", $omsLink);
}

if (!defined('CRM_LINK')) {
    define("CRM_LINK", $crmLink);
}

if (!defined('GCE_LINK')) {
    define('GCE_LINK', $gceLink);
}

if (!defined('WTC_LINK')) {
    define('WTC_LINK', $wtcLink);
}

if (!defined('WPC_LINK')) {
    define('WPC_LINK', $wpcLink);
}

if (!defined('HMS_LINK')) {
    define('HMS_LINK', $hmsLink);
}

if (!defined('RMS_LINK')) {
    define('RMS_LINK', $rmsLink);
}

if (!defined('PLASTIC_ON_LINK')) {
    define('PLASTIC_ON_LINK', $plasticOnLink);
}

if (!defined('HELPDESK_LINK')) {
    define('HELPDESK_LINK', $helpdeskLink);
}

if (!defined('RMS_LINK')) {
    define('RMS_LINK', $rmsLink);
}

if (!defined('PMS_LINK')) {
    define('PMS_LINK', $pmsLink);
}

if (!defined('MMS_LINK')) {
    define('MMS_LINK', $mmsLink);
}

if (!defined('MARKETING_LINK')) {
    define('MARKETING_LINK', $marketingLink);
}

if (!defined('SCHEDULER_LINK')) {
    define('SCHEDULER_LINK', $schedulerLink);
}

if (!defined('INSTA_TANK_LINK')) {
    define('INSTA_TANK_LINK', $instaTankLink);
}

if (!defined('HOST_PATH')) {
    define("HOST_PATH", $hostPath);
}

if (!defined('EMAIL_IT_TEAM')) {
    define("EMAIL_IT_TEAM", $emailItTeam);
}

if (!defined('EMAIL_AVAILABILITY')) {
    define("EMAIL_AVAILABILITY", $emailAvailability);
}

if (!defined('MAIL_TAB_OBJECT')) {
    define("MAIL_TAB_OBJECT", $tabMailObject);
}

if (!defined('MAIN_DIR_GLOBAL')) {
    define("MAIN_DIR_GLOBAL", __DIR__ . "/");
}

if (!defined('MAIN_DIR_OMS')) {
    define("MAIN_DIR_OMS", __DIR__ . "/oms/");
}

if (!defined('MAIN_DIR_MARKETING')) {
    define("MAIN_DIR_MARKETING", __DIR__ . "/marketing/");
}

if (!defined('INSTATANK_ICO')) {
    define("INSTATANK_ICO", '');
}

if (!defined('SESSION_KEY')) {
    define("SESSION_KEY", "0QYasdas34233212as42q3244dsqeZIasddigitasdaser2wr4324rwsIC10ruotest");
}
