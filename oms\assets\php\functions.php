<?php
//CONFIG
require(__DIR__ . "/../../../db-config.php");
require(__DIR__ . "/../../../_variables.php");
require(__DIR__  . "/../../../vendor/autoload.php");
require(__DIR__ . "/better-functions.php");
// NOBO
// require_once("nobo-functions.php");

function connect()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_OMS;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectCRM()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_CRM;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectUsers()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_USERS;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectMilestones()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_MILESTONES;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    if ($link->connect_errno) {
        echo "Failed to connect to MySQL: " . $link->connect_error;
        exit();
    }
    return $link;
}

function connectGeneraldata()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_GENERALDATA;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function dateFormat($date)
{
    $dateFormat = $_SESSION['plasticonDigitalUser']['dateFormat'];
    if ($dateFormat == "")
        $dateFormat = "Y-m-d";
    if ($date == "---")
        return $date;
    if (!empty($date) && $date != "0000-00-00" && strlen($date) > 10) {
        $date = date_create($date);
        return date_format($date, $dateFormat . " H:i:s");
    } else if (!empty($date) && $date != "0000-00-00") {
        $date = date_create($date);
        return date_format($date, $dateFormat);
    } else
        return "";
}

function connectExactPP()
{
    $serverName = '***********';
    $user = 'sa_full';
    $pw = 'ufWuG7ELx#1';
    // $pw = '';
    $db = '200';
    $connectionInfo = array("Database" => $db, "UID" => $user, "PWD" => $pw, "CharacterSet" => "UTF-8", "TrustServerCertificate" => "yes", "Encrypt" => "no");

    try {
        $conn = sqlsrv_connect($serverName, $connectionInfo);

        if ($conn === false) {
            $errors = sqlsrv_errors();
            $errorMessage = "Connection failed:\n";

            foreach ($errors as $error) {
                $errorMessage .= "SQLSTATE: " . $error['SQLSTATE'] . "\n";
                $errorMessage .= "Code: " . $error['code'] . "\n";
                $errorMessage .= "Message: " . $error['message'] . "\n";
            }

            // Log error
            error_log($errorMessage);

            // Send email notification
            $subject = "ExactPP Database Connection Error";
            sendEmail($subject, $errorMessage);

            throw new Exception("Failed to connect to ExactPP database. Please contact system administrator.");
        }

        return $conn;
    } catch (Exception $e) {
        // Log the exception
        error_log("ExactPP Connection Error: " . $e->getMessage());

        echo "  <div class='d-flex justify-content-center flex-column'>
                    <h1 class='text-center p-5'>{$e->getMessage()}</h1>
                    <a class='text-center btn btn-sm btn-danger w-25 m-auto' href='../../'>
                        Back to Plastinet
                    </a>
                </div>";

        // You can choose to either throw the exception again or return false
        throw $e;
        // return false;
    }
}

function cleanStr($txt)
{
    $transliterationTable = array('á' => 'a', 'Á' => 'A', 'à' => 'a', 'À' => 'A', 'ă' => 'a', 'Ă' => 'A', 'â' => 'a', 'Â' => 'A', 'å' => 'a', 'Å' => 'A', 'ã' => 'a', 'Ã' => 'A', 'ą' => 'a', 'Ą' => 'A', 'ā' => 'a', 'Ā' => 'A', 'ä' => 'ae', 'Ä' => 'Ae', 'æ' => 'ae', 'Æ' => 'AE', 'ḃ' => 'b', 'Ḃ' => 'B', 'ć' => 'c', 'Ć' => 'C', 'ĉ' => 'c', 'Ĉ' => 'C', 'č' => 'c', 'Č' => 'C', 'ċ' => 'c', 'Ċ' => 'C', 'ç' => 'c', 'Ç' => 'C', 'ď' => 'd', 'Ď' => 'D', 'ḋ' => 'd', 'Ḋ' => 'D', 'đ' => 'd', 'Đ' => 'D', 'ð' => 'dh', 'Ð' => 'Dh', 'é' => 'e', 'É' => 'E', 'è' => 'e', 'È' => 'E', 'ĕ' => 'e', 'Ĕ' => 'E', 'ê' => 'e', 'Ê' => 'E', 'ě' => 'e', 'Ě' => 'E', 'ë' => 'e', 'Ë' => 'E', 'ė' => 'e', 'Ė' => 'E', 'ę' => 'e', 'Ę' => 'E', 'ē' => 'e', 'Ē' => 'E', 'ḟ' => 'f', 'Ḟ' => 'F', 'ƒ' => 'f', 'Ƒ' => 'F', 'ğ' => 'g', 'Ğ' => 'G', 'ĝ' => 'g', 'Ĝ' => 'G', 'ġ' => 'g', 'Ġ' => 'G', 'ģ' => 'g', 'Ģ' => 'G', 'ĥ' => 'h', 'Ĥ' => 'H', 'ħ' => 'h', 'Ħ' => 'H', 'í' => 'i', 'Í' => 'I', 'ì' => 'i', 'Ì' => 'I', 'î' => 'i', 'Î' => 'I', 'ï' => 'i', 'Ï' => 'I', 'ĩ' => 'i', 'Ĩ' => 'I', 'į' => 'i', 'Į' => 'I', 'ī' => 'i', 'Ī' => 'I', 'ĵ' => 'j', 'Ĵ' => 'J', 'ķ' => 'k', 'Ķ' => 'K', 'ĺ' => 'l', 'Ĺ' => 'L', 'ľ' => 'l', 'Ľ' => 'L', 'ļ' => 'l', 'Ļ' => 'L', 'ł' => 'l', 'Ł' => 'L', 'ṁ' => 'm', 'Ṁ' => 'M', 'ń' => 'n', 'Ń' => 'N', 'ň' => 'n', 'Ň' => 'N', 'ñ' => 'n', 'Ñ' => 'N', 'ņ' => 'n', 'Ņ' => 'N', 'ó' => 'o', 'Ó' => 'O', 'ò' => 'o', 'Ò' => 'O', 'ô' => 'o', 'Ô' => 'O', 'ő' => 'o', 'Ő' => 'O', 'õ' => 'o', 'Õ' => 'O', 'ø' => 'oe', 'Ø' => 'OE', 'ō' => 'o', 'Ō' => 'O', 'ơ' => 'o', 'Ơ' => 'O', 'ö' => 'oe', 'Ö' => 'OE', 'ṗ' => 'p', 'Ṗ' => 'P', 'ŕ' => 'r', 'Ŕ' => 'R', 'ř' => 'r', 'Ř' => 'R', 'ŗ' => 'r', 'Ŗ' => 'R', 'ś' => 's', 'Ś' => 'S', 'ŝ' => 's', 'Ŝ' => 'S', 'š' => 's', 'Š' => 'S', 'ṡ' => 's', 'Ṡ' => 'S', 'ş' => 's', 'Ş' => 'S', 'ș' => 's', 'Ș' => 'S', 'ß' => 'SS', 'ť' => 't', 'Ť' => 'T', 'ṫ' => 't', 'Ṫ' => 'T', 'ţ' => 't', 'Ţ' => 'T', 'ț' => 't', 'Ț' => 'T', 'ŧ' => 't', 'Ŧ' => 'T', 'ú' => 'u', 'Ú' => 'U', 'ù' => 'u', 'Ù' => 'U', 'ŭ' => 'u', 'Ŭ' => 'U', 'û' => 'u', 'Û' => 'U', 'ů' => 'u', 'Ů' => 'U', 'ű' => 'u', 'Ű' => 'U', 'ũ' => 'u', 'Ũ' => 'U', 'ų' => 'u', 'Ų' => 'U', 'ū' => 'u', 'Ū' => 'U', 'ư' => 'u', 'Ư' => 'U', 'ü' => 'ue', 'Ü' => 'UE', 'ẃ' => 'w', 'Ẃ' => 'W', 'ẁ' => 'w', 'Ẁ' => 'W', 'ŵ' => 'w', 'Ŵ' => 'W', 'ẅ' => 'w', 'Ẅ' => 'W', 'ý' => 'y', 'Ý' => 'Y', 'ỳ' => 'y', 'Ỳ' => 'Y', 'ŷ' => 'y', 'Ŷ' => 'Y', 'ÿ' => 'y', 'Ÿ' => 'Y', 'ź' => 'z', 'Ź' => 'Z', 'ž' => 'z', 'Ž' => 'Z', 'ż' => 'z', 'Ż' => 'Z', 'þ' => 'th', 'Þ' => 'Th', 'µ' => 'u', 'а' => 'a', 'А' => 'a', 'б' => 'b', 'Б' => 'b', 'в' => 'v', 'В' => 'v', 'г' => 'g', 'Г' => 'g', 'д' => 'd', 'Д' => 'd', 'е' => 'e', 'Е' => 'E', 'ё' => 'e', 'Ё' => 'E', 'ж' => 'zh', 'Ж' => 'zh', 'з' => 'z', 'З' => 'z', 'и' => 'i', 'И' => 'i', 'й' => 'j', 'Й' => 'j', 'к' => 'k', 'К' => 'k', 'л' => 'l', 'Л' => 'l', 'м' => 'm', 'М' => 'm', 'н' => 'n', 'Н' => 'n', 'о' => 'o', 'О' => 'o', 'п' => 'p', 'П' => 'p', 'р' => 'r', 'Р' => 'r', 'с' => 's', 'С' => 's', 'т' => 't', 'Т' => 't', 'у' => 'u', 'У' => 'u', 'ф' => 'f', 'Ф' => 'f', 'х' => 'h', 'Х' => 'h', 'ц' => 'c', 'Ц' => 'c', 'ч' => 'ch', 'Ч' => 'ch', 'ш' => 'sh', 'Ш' => 'sh', 'щ' => 'sch', 'Щ' => 'sch', 'ъ' => '', 'Ъ' => '', 'ы' => 'y', 'Ы' => 'y', 'ь' => '', 'Ь' => '', 'э' => 'e', 'Э' => 'e', 'ю' => 'ju', 'Ю' => 'ju', 'я' => 'ja', 'Я' => 'ja', '>' => 'Larger', '<' => 'Smaller', '.' => ' ', '"' => ' ', "'" => ' ');
    return str_replace(array_keys($transliterationTable), array_values($transliterationTable), $txt);
}

function sumOrderValue($offer)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM salesArticles WHERE offerNo='%s'",
        mysqli_real_escape_string($link, strip_tags($offer))
    ));
    while ($row = $result->fetch_object()) {
        $link->query(sprintf(
            "UPDATE salesArticles SET orderValue=(SELECT ROUND(SUM(orderValue),2) FROM components WHERE salesArticleId='%s') WHERE id='%s'",
            mysqli_real_escape_string($link, strip_tags($row->id)),
            mysqli_real_escape_string($link, strip_tags($row->id))
        ));
    }
    $link->query(sprintf(
        "UPDATE projects SET orderValue=(SELECT ROUND(SUM(orderValue),2) FROM salesArticles WHERE offerNo='%s') WHERE offerNo='%s'",
        mysqli_real_escape_string($link, strip_tags($offer)),
        mysqli_real_escape_string($link, strip_tags($offer))
    ));
    $link->close();
}

function connectEGZ()
{
    $host = DB_HOST_PANEL;
    $db_user = DB_USER_PANEL;
    $db_password = DB_PASSWORD_PANEL;
    $db_name = DB_NAME_PANEL;
    $db_port = DB_PORT_PANEL;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);

    if (!$link || $link->connect_error || mysqli_connect_errno()) {
        $message = isset($link->connect_error) ? $link->connect_error : "Brak połączenia z baza danych";
        $subject = "Błąd w connectEGZ";
        $message = "Występuje błąd podczas proby polaczenia z baza danych: $message";

        sendEmail($subject, $message);

        return false;
    } elseif ($link && !mysqli_connect_errno()) {
        $link->query('SET NAMES utf8');
        // $link->query('SET CHARACTER_SET utf8_unicode_ci');

        return $link;
    } else {
        return false;
    }
}

//FUNCTIONS

function bez_pl($string)
{
    $polskie = array('ę', 'Ę', 'ó', 'Ó', 'Ą', 'ą', 'Ś', 's', 'ł', 'Ł', 'ż', 'Ż', 'Ź', 'ź', 'ć', 'Ć', 'ń', 'Ń', 'ś');
    $miedzyn = array('e', 'e', 'o', 'o', 'a', 'a', 's', 's', 'l', 'l', 'z', 'z', 'z', 'z', 'c', 'c', 'n', 'n', 's');
    $string = str_replace($polskie, $miedzyn, $string);
    return $string;
}

function ifEmailExists($mail)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE email='$mail'");
    $link->close();
    if ($result->num_rows != 0)
        return true;
    return false;
}

function alertDanger($t)
{
    echo "<div class='w-100'><div class='row alert-danger justify-content-center'><div style='width:97%;' class='text-center'><strong>$t</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>";
}

function alertSuccess($t)
{
    echo "<div><br><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>$t</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>";
}

function inicialy($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    if (!empty($row->imie))
        return strtoupper(substr(($row->imie), 0, 1) . substr(($row->nazwisko), 0, 1));
    return "";
}

function iniciclyText($text)
{
    $output = [];
    $encoding = 'UTF-8';

    foreach (explode(", ", $text) as $txt) {
        $pomStr = "";
        foreach (explode(" ", $txt) as $t)
            $pomStr .= mb_substr($t, 0, 1, $encoding);
        array_push($output, $pomStr);
    }
    return implode(", ", $output);
}



function getUserAvatar($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    $av = $row->avatar;
    if (empty($av))
        return "assets/images/avatars/admin.png";
    else
        return INTRANET_LINK . "/assets/upload/" . $av;
}

function imageResize($imageResourceId, $width, $height)
{
    $targetWidth = 60;
    $targetHeight = 60;
    $targetLayer = imagecreatetruecolor($targetWidth, $targetHeight);
    imagecopyresampled($targetLayer, $imageResourceId, 0, 0, 0, 0, $targetWidth, $targetHeight, $width, $height);
    return $targetLayer;
}

function checkPw($pw, $mail)
{
    $link = connectUsers();
    $query = sprintf(
        "SELECT * FROM users WHERE password='%s' AND email='%s'",
        mysqli_real_escape_string($link, strip_tags($pw)),
        mysqli_real_escape_string($link, strip_tags($mail))
    );
    $result = $link->query($query);
    if ($result->num_rows == 1)
        return true;
    else
        return false;
}

function dniPomiedzy($d1, $d2)
{
    $d1 = strtotime($d1);
    $d2 = strtotime($d2);
    $diff = $d2 - $d1;
    return round($diff / (60 * 60 * 24));
}

function getUserInfo($id)
{
    $link = connectUsers();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM users u JOIN oms o ON u.id=o.userId WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_array();
    return $row;
}

function saveArticleLog($val1, $val2, $desc) {}

function saveLog($article_id, $desc)
{
    $user = getUserInfo($_SESSION['plasticonDigitalUser']['id']);
    $link = connect();
    $name = $user['email'];
    if (!empty($user['imie']) && !empty($user['nazwisko'])) {
        $name = $user['imie'] . " " . $user['nazwisko'];
    }
    $link->query(sprintf(
        "INSERT INTO `salesarticles_logs`(`author_id`, `author_name`, `author_date`, `article_id`, `logdesc`) VALUES ('%s','%s',NOW(),'%s', '%s')",
        mysqli_real_escape_string($link, $user['id']),
        mysqli_real_escape_string($link, $name),
        mysqli_real_escape_string($link, $article_id),
        mysqli_real_escape_string($link, $desc)
    ));
}

function getLog($article_id)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM `salesarticles_logs` WHERE article_id='%s' ORDER BY author_date DESC",
        mysqli_real_escape_string($link, $article_id)
    ));
    $link->close();
    $output = [];
    while ($row = $result->fetch_object()) {
        $output[$row->id] = $row;
    }

    return $output;
}

function translateCompanies($cmp)
{
    switch ($cmp) {
        case 'PTN':
            return 'Plasticon the Netherlands';
            break;
        case 'PP':
            return 'Plasticon Poland';
            break;
        case 'PG':
            return 'Plasticon Germany';
            break;
        case 'TP':
            return 'Thermopol';
            break;
        case 'PT':
            return 'Plasto-Tec';
            break;
        default:
            return '';
            break;
    }
}

function selectUsers()
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE isActive='1'");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $name = $row->imie;
        $surname = $row->nazwisko;
        echo "<option value='" . $id . "'>$name $surname</option>";
    }
    $link->close();
}

function selectUsersSales()
{
    $link = connectUsers();

    $sql = "SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listID='1'";

    $result = $link->query($sql);

    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $name = $row->imie;
        $surname = $row->nazwisko;

        echo "<option value='" . $id . "'>$name $surname</option>";
    }
    $link->close();
}

function selectUsersPerm($perm)
{
    $link = connectUsers();
    $sql = "SELECT u.* FROM users u JOIN oms o ON u.id=o.userId WHERE isActive='1' AND o.$perm='1'";

    $result = $link->query($sql);

    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $name = $row->imie;
        $surname = $row->nazwisko;

        echo "<option value='" . $id . "'>$name $surname</option>";
    }
    $link->close();
}

function getSubcomponentInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM subcomponents WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_array();
    return $row;
}

function selectUsersNamesPerm($perm)
{
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM users u JOIN oms o ON u.id=o.userId WHERE isActive='1' AND o.$perm='1'");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $name = $row->imie;
        $surname = $row->nazwisko;
        echo "<option value='" . $name . " " . $surname . "'>$name $surname</option>";
    }
    $link->close();
}

function translateMonths($m)
{
    switch ($m) {
        case '1':
            return 'January';
            break;
        case '2':
            return 'February';
            break;
        case '3':
            return 'March';
            break;
        case '4':
            return 'April';
            break;
        case '5':
            return 'May';
            break;
        case '6':
            return 'June';
            break;
        case '7':
            return 'July';
            break;
        case '8':
            return 'August';
            break;
        case '9':
            return 'September';
            break;
        case '10':
            return 'October';
            break;
        case '11':
            return 'November';
            break;
        case '12':
            return 'December';
            break;
    }
}

function randomPassword()
{
    $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$';
    $pass = array();
    $alphaLength = strlen($alphabet) - 1;
    for ($i = 0; $i < 8; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass);
}

function getUserOnlineStatus($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    if (date("Y-m-d H:i:s") > date('Y-m-d H:i:s', strtotime('+10 minutes', strtotime($row->lastActivity))))
        return 'offline';
    return 'online';
}

function getUserLastActivity($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    if (($row->lastActivity) != "0000-00-00 00:00:00")
        return $row->lastActivity;
    return 'This user has no activity.';
}

function formatSizeUnits($bytes)
{
    if ($bytes >= 1073741824) {
        $bytes = number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        $bytes = number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        $bytes = number_format($bytes / 1024, 2) . ' KB';
    } elseif ($bytes > 1) {
        $bytes = $bytes . ' bytes';
    } elseif ($bytes == 1) {
        $bytes = $bytes . ' byte';
    } else {
        $bytes = '0 bytes';
    }
    return $bytes;
}

function getFolderSize($directory)
{
    // Check if directory exists
    if (!is_dir($directory)) {
        // consoleLog("Error: Directory does not exist or is not a directory: " . $directory);
        return 0;
    }

    $totalSize = 0;
    $directoryArray = scandir($directory);

    if ($directoryArray === false) {
        // consoleLog("Error: Failed to open directory: " . $directory);
        return 0;
    }

    foreach ($directoryArray as $key => $fileName) {
        if ($fileName != ".." && $fileName != ".") {
            $filePath = $directory . "/" . $fileName;
            if (is_dir($filePath)) {
                $totalSize += getFolderSize($filePath);
            } else if (is_file($filePath)) {
                $totalSize += filesize($filePath);
            }
        }
    }
    return $totalSize;
}

function getLastMilestoneUpdate($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM milestones WHERE articleId='$id' ORDER BY id DESC");
    $row = $result->fetch_object();
    return $row->id;
}

function getProjectId($offer)
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects WHERE offerNo='$offer'");
    $row = $result->fetch_object();
    if ($row === null) {
        return -1;
    }
    return $row->id;
}

function getCRMofferId(?int $offer): int
{
    if ($offer === null) {
        return 0;
    }
    $link = connectCRM();
    $result = $link->query("SELECT `id` FROM offers WHERE offerNo='$offer'");
    $row = $result->fetch_object();
    if ($row !== null) {
        return intval($row->id, 10);
    }
    return 0;
}

function getMilestoneInfo($article)
{
    $link = connectMilestones();

    $result = $link->query(sprintf(
        "SELECT n.id as nameId, n.short, n.name, d.*, CONCAT(u.imie,' ',u.nazwisko) as r_full FROM dates d JOIN names n ON d.m_id=n.id LEFT JOIN users.users u ON u.id=d.r_id WHERE a_id='%s' ORDER BY m_id ASC",
        mysqli_real_escape_string($link, $article)
    ));
    $link->close();

    $output = [];

    while ($row = $result->fetch_object()) {
        $output[$row->short] = $row;
    }

    return $output;
}

function getMilestoneInfoNew($articleId)
{
    $output = [];
    $link = connectMilestones();
    $result = $link->query(sprintf(
        "SELECT n.id as nameId, n.short, n.name, d.*, CONCAT(u.imie,' ',u.nazwisko) as r_full FROM dates d JOIN names n ON d.m_id=n.id LEFT JOIN users.users u ON u.id=d.r_id WHERE a_id='%s' ORDER BY n.sequence ASC, d.id asc",
        mysqli_real_escape_string($link, $articleId)
    ));

    if ($result === false) {
        return $output;
    }

    $link->close();

    while ($row = $result->fetch_object()) {
        array_push($output, $row);
    }

    return $output;
}

function getArticleMaxMilestoneRevision($articleId, $milestoneId = 2)
{
    $link = connectMilestones();
    $result = $link->query(sprintf(
        "SELECT MAX(revision) as maxId FROM dates WHERE a_id='%s' AND m_id='%s'",
        mysqli_real_escape_string($link, strip_tags($articleId)),
        mysqli_real_escape_string($link, strip_tags($milestoneId))
    ));

    if (!$result) {
        // Obsługa błędu zapytania
        error_log('Błąd zapytania: ' . $link->error);
        $link->close();
        return 0;
    }

    $row = $result->fetch_object();
    $link->close();

    return $row ? $row->maxId : 0;
}

function createMilestones($article)
{
    $article = getSalesArticleInfo($article);
    $milestones = getMilestoneInfo($article['id']);
    $link = connectMilestones();

    if ($article['staticCalculation'] == 1) {
        if (!isset($milestones['sc'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 1),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['sc'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['sc']->id)
            ));
        }
    }

    if ($article['detailDrawing'] == 1) {
        if (!isset($milestones['dd'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 2),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['dd'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['dd']->id)
            ));
        }
    }

    if ($article['approvalOfEngineering'] == 1) {
        if (!isset($milestones['aoe'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 3),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['aoe'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['aoe']->id)
            ));
        }
    }

    if ($article['qualityInspectionPlan'] == 1) {
        if (!isset($milestones['qip'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 4),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['qip'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['qip']->id)
            ));
        }
    }

    if ($article['workPreparation'] == 1) {
        if (!isset($milestones['wp'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 5),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['wp'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['wp']->id)
            ));
        }
    }

    if ($article['documentationPlanning'] == 1) {
        if (!isset($milestones['dp'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 6),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['dp'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['dp']->id)
            ));
        }
    }

    if ($article['fabrication'] == 1) {
        if (!isset($milestones['f'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 7),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['f'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['f']->id)
            ));
        }
    }

    if ($article['qualityControl'] == 1) {
        if (!isset($milestones['qc'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 8),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['qc'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['qc']->id)
            ));
        }
    }

    if ($article['transport'] == 1) {
        if (!isset($milestones['t'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 9),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['t'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['t']->id)
            ));
        }
    }

    // Packing
    if ($article['packing'] == 1) {
        if (!isset($milestones['p'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 12),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['p'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['p']->id)
            ));
        }
    }
    // packing

    if ($article['serviceInstalation'] == 1) {
        if (!isset($milestones['si'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 10),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['si'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['si']->id)
            ));
        }
    }

    if ($article['documentation'] == 1) {
        if (!isset($milestones['d'])) {
            $link->query(sprintf(
                "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`) VALUES ('%s','%s','%s')",
                mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['id']),
                mysqli_real_escape_string($link, 11),
                mysqli_real_escape_string($link, $article['id'])
            ));
        }
    } else {
        if (isset($milestones['d'])) {
            $link->query(sprintf(
                "DELETE FROM dates WHERE id='%s'",
                mysqli_real_escape_string($link, $milestones['d']->id)
            ));
        }
    }
}

function getProjectInfo($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects WHERE id='$id'");
    $row = $result->fetch_array();
    return $row;
}

function getProjectInfoByOffer($offer)
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects WHERE offerNo='$offer'");
    $row = $result->fetch_array();
    return $row;
}

function getComponentInfo($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM components WHERE id='$id'");
    $row = $result->fetch_array();
    return $row;
}

function getSalesArticleInfo($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM salesArticles WHERE id='$id'");
    $row = $result->fetch_array();
    //if($row['exchangeRate']==0)
    //$row['exchangeRate']=1;
    return $row;
}

function getPayCondInfo($id)
{
    $link = connectCRM();
    $result = $link->query("SELECT * FROM paymentconditions WHERE id='$id'");
    $row = $result->fetch_array();
    return $row;
}

function getClientName($id)
{
    $link = connectCRM();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $row = $result->fetch_object();

    return $row->clientLongName;
}

function getClientNameCopiedFnFromCRM($id)
{
    $link = connectCRM();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row) {
        if ($row->clientLongName != "")
            return $row->clientLongName;
        else
            return $row->clientShortName;
    }
    return false;
}

function getClientLocation($id)
{
    $link = connectCRM();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $row = $result->fetch_object();
    return $row->city;
}

function getUserNameAndSurname($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    return ($row->imie) . " " . ($row->nazwisko);
}

function getUserName($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->imie;
    return false;
}

function getUserSurname($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->nazwisko;
    return false;
}

function weeksDifference($d1, $d2)
{
    $date1 = new DateTime($d1);
    $date2 = new DateTime($d2);
    $difference_in_weeks = $date1->diff($date2)->days / 7;
    return round($difference_in_weeks);
}

function daysDifference($d1, $d2)
{
    $date1 = new DateTime($d1);
    $date2 = new DateTime($d2);
    return $date1->diff($date2)->days;
}

function clientsFilter()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(client) as id, clientName FROM orders ORDER BY clientName DESC");
    $output = "";
    while ($row = $result->fetch_object())
        $output .= "<option value='" . ($row->id) . "'>" . ($row->id) . " | " . ($row->clientName) . "</option>";
    return $output;
}

function getOrderIdFromSON($son)
{
    $link = connect();
    $result = $link->query("SELECT * FROM orders WHERE salesOrderNumber='$son'");
    $row = $result->fetch_object();
    return $row->id;
}

function listInspections($id)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM inspections WHERE articleId='%s' ORDER by id ASC",
        mysqli_real_escape_string($link, strip_tags($id))
    ));
    $output = "";
    $i = 1;
    while ($row = $result->fetch_object())
        $output .= "<tr><td>" . ($i++) . "</td><td>" . ($row->userFull) . "</td><td>" . ($row->inspectionDate) . "</td><td>" . ($row->note) . "</td><td>EDYCJA</td></td>";
    return $output;
}

function getStatusCircle($status)
{
    switch ($status) {
        case 'Not started':
            return "<div class='circle-blue m0 pointer' title='Not started'></div>";
            break;
        case 'Started':
            return "<div class='circle-yellow m0 pointer' title='Started'></div>";
            break;
        case 'Finished':
            return "<i class='fas fa-check text-success ml3px' title='Finished'></i>";
            break;
    }
}

function deleteDir($dirPath)
{
    if (!is_dir($dirPath)) {
        throw new InvalidArgumentException("$dirPath must be a directory");
    }
    if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
        $dirPath .= '/';
    }
    $files = glob($dirPath . '*', GLOB_MARK);
    foreach ($files as $file) {
        if (is_dir($file)) {
            deleteDir($file);
        } else {
            unlink($file);
        }
    }
    rmdir($dirPath);
}

function listSalesArticles($offer)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM salesArticles WHERE offerNo='%s'",
        mysqli_real_escape_string($link, strip_tags($offer))
    ));
    $link->close();
    $output = "";
    while ($row = $result->fetch_object())
        $output .= "<option value='" . ($row->id) . "'>" . ($row->name) . "</option>";
    return $output;
}

function listAdvStages($id)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM advancementFreeStages WHERE orderId='%s' ORDER BY id ASC",
        mysqli_real_escape_string($link, strip_tags($id))
    ));
    $output = "";
    while ($row = $result->fetch_object()) {
        $start = "---";
        if ($row->start != "0000-00-00")
            $start = $row->start;
        $end = "---";
        if ($row->end != "0000-00-00")
            $end = $row->end;
        if ($start == "---") {
            $se = '<button class="btn btn-xsm btn-primary btn-omt" onclick="strtAdvStageFree(' . "'" . ($row->id) . "'" . ')" title="Start"><i class="fas fa-play"></i></button>';
            $edit = '<button class="btn btn-xsm btn-primary btn-omt" title="Edit" onclick="editAdvStagesFree(' . ($row->id) . ', ' . "'0'" . ')"><i class="fas fa-edit"></i></button>';
        } else {
            if ($end == "---") {
                $se = '<button class="btn btn-xsm btn-primary btn-omt" onclick="endAdvStageFree(' . "'" . ($row->id) . "'" . ')" title="End"><i class="fas fa-step-forward"></i></button>';
                $edit = '<button class="btn btn-xsm btn-primary btn-omt" title="Edit" onclick="editAdvStagesFree(' . ($row->id) . ', ' . "'1'" . ')"><i class="fas fa-edit"></i></button>';
            } else
                $edit = '<button class="btn btn-xsm btn-primary btn-omt" title="Edit" onclick="editAdvStagesFree(' . ($row->id) . ', ' . "'2'" . ')"><i class="fas fa-edit"></i></button>';
        }
        $output .= '<tr id="' . ($row->id) . 'adv">
					<td>
						<span id="' . ($row->id) . 'span">' . $se . ' ' . $edit . '</span>
						<button class="btn btn-xsm btn-primary btn-omt" title="Delete" onclick="delAdvStage(' . "'" . ($row->id) . "'" . ')"><i class="fas fa-trash"></i></button>
					</td>
					<td id="' . ($row->id) . 'St">' . getStatusCircle($row->status) . '</td>
					<td id="' . ($row->id) . 'N">' . ($row->name) . '</td>
					<td id="' . ($row->id) . 'S">' . ($start) . '</td>
					<td id="' . ($row->id) . 'E">' . ($end) . '</td>
				</tr>';
    }
    $link->close();
    return $output;
}

function listGItrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->generalInfoSR != "0000-00-00")
            $startReal = $row->generalInfoSR;
        $endReal = "---";
        if ($row->generalInfoER != "0000-00-00")
            $endReal = $row->generalInfoER;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow giExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->generalInfoS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->generalInfoW) . ' (' . ($row->generalInfoD) . ')</strong></td>
						<td><strong>' . ($row->generalInfoE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>---</strong></td>
						<td><strong>---</strong></td>
						<td><strong>---</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow giExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->generalInfoS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->generalInfoW) . ' (' . ($row->generalInfoD) . ')</td>
						<td>' . ($row->generalInfoE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>---</td>
						<td>---</td>
						<td>---</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listGADtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $start = "---";
        if ($row->generalArrangementS != "0000-00-00")
            $start = $row->generalArrangementS;
        $startReal = "---";
        if ($row->generalArrangementSR != "0000-00-00")
            $startReal = $row->generalArrangementSR;
        $end = "---";
        if ($row->generalArrangementE != "0000-00-00")
            $start = $row->generalArrangementE;
        $endReal = "---";
        if ($row->generalArrangementER != "0000-00-00")
            $endReal = $row->generalArrangementER;
        $cont = "---";
        if ($row->generalArrangementC != "0000-00-00")
            $cont = $row->generalArrangementC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow gaExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($start) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->generalArrangementW) . ' (' . ($row->generalArrangementD) . ')</strong></td>
						<td><strong>' . ($end) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->generalArrangementRfull) . '</strong></td>
						<td><strong>' . ($row->generalArrangementN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow gaExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($start) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->generalArrangementW) . ' (' . ($row->generalArrangementD) . ')</td>
						<td>' . ($end) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->generalArrangementRfull) . '</td>
						<td>' . ($row->generalArrangementN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listSCtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->staticCalculationSR != "0000-00-00")
            $startReal = $row->staticCalculationSR;
        $endReal = "---";
        if ($row->staticCalculationER != "0000-00-00")
            $endReal = $row->staticCalculationER;
        $cont = "---";
        if ($row->staticCalculationC != "0000-00-00")
            $cont = $row->staticCalculationC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow scExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->staticCalculationS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->staticCalculationW) . ' (' . ($row->staticCalculationD) . ')</strong></td>
						<td><strong>' . ($row->staticCalculationE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->staticCalculationRfull) . '</strong></td>
						<td><strong>' . ($row->staticCalculationN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow scExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->staticCalculationS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->staticCalculationW) . ' (' . ($row->staticCalculationD) . ')</td>
						<td>' . ($row->staticCalculationE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->staticCalculationRfull) . '</td>
						<td>' . ($row->staticCalculationN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listDDtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->detailDrawingsSR != "0000-00-00")
            $startReal = $row->detailDrawingsSR;
        $endReal = "---";
        if ($row->detailDrawingsER != "0000-00-00")
            $endReal = $row->detailDrawingsER;
        $cont = "---";
        if ($row->detailDrawingsC != "0000-00-00")
            $cont = $row->detailDrawingsC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow ddExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->detailDrawingsS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->detailDrawingsW) . ' (' . ($row->detailDrawingsD) . ')</strong></td>
						<td><strong>' . ($row->detailDrawingsE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($row->detailDrawingsRfull) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->detailDrawingsN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow ddExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->detailDrawingsS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->detailDrawingsW) . ' (' . ($row->detailDrawingsD) . ')</td>
						<td>' . ($row->detailDrawingsE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->detailDrawingsRfull) . '</td>
						<td>' . ($row->detailDrawingsN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listQIPtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->qualityInspectionPlanSR != "0000-00-00")
            $startReal = $row->qualityInspectionPlanSR;
        $endReal = "---";
        if ($row->qualityInspectionPlanER != "0000-00-00")
            $endReal = $row->qualityInspectionPlanER;
        $cont = "---";
        if ($row->qualityInspectionPlanC != "0000-00-00")
            $cont = $row->qualityInspectionPlanC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow qipExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->qualityInspectionPlanS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->qualityInspectionPlanW) . ' (' . ($row->qualityInspectionPlanD) . ')</strong></td>
						<td><strong>' . ($row->qualityInspectionPlanE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($row->qualityInspectionPlanRfull) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->qualityInspectionPlanN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow qipExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->qualityInspectionPlanS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->qualityInspectionPlanW) . ' (' . ($row->qualityInspectionPlanD) . ')</td>
						<td>' . ($row->qualityInspectionPlanE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->qualityInspectionPlanRfull) . '</td>
						<td>' . ($row->qualityInspectionPlanN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listAOEtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->approvalOfEngineeringSR != "0000-00-00")
            $startReal = $row->approvalOfEngineeringSR;
        $endReal = "---";
        if ($row->approvalOfEngineeringER != "0000-00-00")
            $endReal = $row->approvalOfEngineeringER;
        $cont = "---";
        if ($row->approvalOfEngineeringC != "0000-00-00")
            $cont = $row->approvalOfEngineeringC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow aoeExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->approvalOfEngineeringS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->approvalOfEngineeringW) . ' (' . ($row->approvalOfEngineeringD) . ')</strong></td>
						<td><strong>' . ($row->approvalOfEngineeringE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->approvalOfEngineeringRfull) . '</strong></td>
						<td><strong>' . ($row->approvalOfEngineeringN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow aoeExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->approvalOfEngineeringS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->approvalOfEngineeringW) . ' (' . ($row->approvalOfEngineeringD) . ')</td>
						<td>' . ($row->approvalOfEngineeringE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->approvalOfEngineeringRfull) . '</td>
						<td>' . ($row->approvalOfEngineeringN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listWPtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->workPreparationSR != "0000-00-00")
            $startReal = $row->workPreparationSR;
        $endReal = "---";
        if ($row->workPreparationER != "0000-00-00")
            $endReal = $row->workPreparationER;
        $cont = "---";
        if ($row->workPreparationC != "0000-00-00")
            $cont = $row->workPreparationC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow wpExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->workPreparationS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->workPreparationW) . ' (' . ($row->workPreparationD) . ')</strong></td>
						<td><strong>' . ($row->workPreparationE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->workPreparationRfull) . '</strong></td>
						<td><strong>' . ($row->workPreparationN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow wpExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->workPreparationS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->workPreparationW) . ' (' . ($row->workPreparationD) . ')</td>
						<td>' . ($row->workPreparationE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->workPreparationRfull) . '</td>
						<td>' . ($row->workPreparationN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listFtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->fabricationSR != "0000-00-00")
            $startReal = $row->fabricationSR;
        $endReal = "---";
        if ($row->fabricationER != "0000-00-00")
            $endReal = $row->fabricationER;
        $cont = "---";
        if ($row->fabricationC != "0000-00-00")
            $cont = $row->fabricationC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow fExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->fabricationS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->fabricationW) . ' (' . ($row->fabricationD) . ')</strong></td>
						<td><strong>' . ($row->fabricationE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->fabricationRfull) . '</strong></td>
						<td><strong>' . ($row->fabricationN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow fExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->fabricationS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->fabricationW) . ' (' . ($row->fabricationD) . ')</td>
						<td>' . ($row->fabricationE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->fabricationRfull) . '</td>
						<td>' . ($row->fabricationN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listQCtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->qualityControlSR != "0000-00-00")
            $startReal = $row->qualityControlSR;
        $endReal = "---";
        if ($row->qualityControlER != "0000-00-00")
            $endReal = $row->qualityControlER;
        $cont = "---";
        if ($row->qualityControlC != "0000-00-00")
            $cont = $row->qualityControlC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow qcExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->qualityControlS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->qualityControlW) . ' (' . ($row->qualityControlD) . ')</strong></td>
						<td><strong>' . ($row->qualityControlE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->qualityControlRfull) . '</strong></td>
						<td><strong>' . ($row->qualityControlN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow qcExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->qualityControlS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->qualityControlW) . ' (' . ($row->qualityControlD) . ')</td>
						<td>' . ($row->qualityControlE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->qualityControlRfull) . '</td>
						<td>' . ($row->qualityControlN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listDtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->documentationSR != "0000-00-00")
            $startReal = $row->documentationSR;
        $endReal = "---";
        if ($row->documentationER != "0000-00-00")
            $endReal = $row->documentationER;
        $cont = "---";
        if ($row->documentationC != "0000-00-00")
            $cont = $row->documentationC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow dExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->documentationS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->documentationW) . ' (' . ($row->documentationD) . ')</strong></td>
						<td><strong>' . ($row->documentationE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->documentationRfull) . '</strong></td>
						<td><strong>' . ($row->documentationN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow dExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->documentationS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->documentationW) . ' (' . ($row->documentationD) . ')</td>
						<td>' . ($row->documentationE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->documentationRfull) . '</td>
						<td>' . ($row->documentationN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function listTtrs($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM workflow WHERE orderId='$id' AND id!=(SELECT MAX(id) FROM workflow WHERE orderId='$id') ORDER BY id DESC");
    $output = "";
    $loop = 0;
    while ($row = $result->fetch_object()) {
        $startReal = "---";
        if ($row->transportSR != "0000-00-00")
            $startReal = $row->transportSR;
        $endReal = "---";
        if ($row->transportER != "0000-00-00")
            $endReal = $row->transportER;
        $cont = "---";
        if ($row->transportC != "0000-00-00")
            $cont = $row->transportC;
        if ($loop == $result->num_rows - 1) {
            $output .= '<tr class="hidden expandRow tExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td><strong>' . ($row->status) . '</strong></td>
						<td><strong>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</strong></td>
						<td><strong>' . ($row->transportS) . '</strong></td>
						<td class="gb-grey"><strong>' . ($startReal) . '</strong></td>
						<td><strong>' . ($row->transportW) . ' (' . ($row->transportD) . ')</strong></td>
						<td><strong>' . ($row->transportE) . '</strong></td>
						<td class="gb-grey"><strong>' . ($endReal) . '</strong></td>
						<td><strong>' . ($cont) . '</strong></td>
						<td><strong>' . ($row->transportRfull) . '</strong></td>
						<td><strong>' . ($row->transportN) . '</strong></td>
					</tr>';
        } else {
            $output .= '<tr class="hidden expandRow tExTr expandAll" style="background-color: rgba(0,0,0,.04);">
						<td></td>
						<td></td>
						<td>' . ($row->status) . '</td>
						<td>' . ($row->updateTimestamp) . ' ' . ($row->updaterFull) . '</td>
						<td>' . ($row->transportS) . '</td>
						<td class="gb-grey">' . ($startReal) . '</td>
						<td>' . ($row->transportW) . ' (' . ($row->transportD) . ')</td>
						<td>' . ($row->transportE) . '</td>
						<td class="gb-grey">' . ($endReal) . '</td>
						<td>' . ($cont) . '</td>
						<td>' . ($row->transportRfull) . '</td>
						<td>' . ($row->transportN) . '</td>
					</tr>';
            $loop++;
        }
    }
    $link->close();
    return $output;
}

function orderStatus($id)
{
    $order = getOrderInfo($id);
    $status = "In progress";
    if ($order['generalArrangementC'] != '0000-00-00')
        if ($order['generalArrangementER'] != '0000-00-00') {
            if ($order['generalArrangementER'] > $order['generalArrangementC'])
                $status = "Delayed";
        } else
        if ($order['generalArrangementE'] > $order['generalArrangementC'])
            $status = "Delayed";

    if ($order['staticCalculationC'] != '0000-00-00')
        if ($order['staticCalculationER'] != '0000-00-00') {
            if ($order['staticCalculationER'] > $order['staticCalculationC'])
                $status = "Delayed";
        } else
        if ($order['staticCalculationE'] > $order['staticCalculationC'])
            $status = "Delayed";

    if ($order['detailDrawingsC'] != '0000-00-00')
        if ($order['detailDrawingsER'] != '0000-00-00') {
            if ($order['detailDrawingsER'] > $order['detailDrawingsC'])
                $status = "Delayed";
        } else
        if ($order['detailDrawingsE'] > $order['detailDrawingsC'])
            $status = "Delayed";

    if ($order['approvalOfEngineeringC'] != '0000-00-00')
        if ($order['approvalOfEngineeringER'] != '0000-00-00') {
            if ($order['approvalOfEngineeringER'] > $order['approvalOfEngineeringC'])
                $status = "Delayed";
        } else
        if ($order['approvalOfEngineeringE'] > $order['approvalOfEngineeringC'])
            $status = "Delayed";

    if ($order['workPreparationC'] != '0000-00-00')
        if ($order['workPreparationER'] != '0000-00-00') {
            if ($order['workPreparationER'] > $order['workPreparationC'])
                $status = "Delayed";
        } else
        if ($order['workPreparationE'] > $order['workPreparationC'])
            $status = "Delayed";

    if ($order['fabricationC'] != '0000-00-00')
        if ($order['fabricationER'] != '0000-00-00') {
            if ($order['fabricationER'] > $order['fabricationC'])
                $status = "Delayed";
        } else
        if ($order['fabricationE'] > $order['fabricationC'])
            $status = "Delayed";

    if ($order['qualityControlC'] != '0000-00-00')
        if ($order['qualityControlER'] != '0000-00-00') {
            if ($order['qualityControlER'] > $order['qualityControlC'])
                $status = "Delayed";
        } else
        if ($order['qualityControlE'] > $order['qualityControlC'])
            $status = "Delayed";

    if ($order['documentationC'] != '0000-00-00')
        if ($order['documentationER'] != '0000-00-00') {
            if ($order['documentationER'] > $order['documentationC'])
                $status = "Delayed";
        } else
        if ($order['documentationE'] > $order['documentationC'])
            $status = "Delayed";

    if ($order['transportC'] != '0000-00-00')
        if ($order['transportER'] != '0000-00-00') {
            if ($order['transportER'] > $order['transportC'])
                $status = "Delayed";
        } else
        if ($order['transportE'] > $order['transportC'])
            $status = "Delayed";
    $link = connect();
    $link->query(sprintf(
        "UPDATE orders SET status='%s' WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($status)),
        mysqli_real_escape_string($link, strip_tags($id))
    ));
    $link->close();
    return $status;
}

function listNotes($id)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT * FROM notes WHERE orderId='%s' ORDER BY id DESC",
        mysqli_real_escape_string($link, strip_tags($id))
    ));
    $link->close();
    $output = "";
    while ($row = $result->fetch_object())
        $output .= '<tr><td>' . ($row->id) . '</td><td>' . ($row->userFull) . '</td><td>' . ($row->timestamp) . '</td><td>' . str_replace("\n", "<br>", $row->note) . '</td></td>';
    return $output;
}

function listComments($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM comments WHERE articleId='$id'");
    $output = "";
    while ($row = $result->fetch_object()) {
        $output .= "<div class='row' style='margin:0;'><strong>" . substr(($row->timestamp), 0, 10) . " | " . ($row->name) . " " . ($row->surname) . "</strong></div>";
        $output .= "<div class='row' style='margin:0;'>" . ($row->note) . "</div><hr>";
    }
    echo $output;
    $link->close();
}

function cleanData($str)
{
    $str = strip_tags($str);
    $str = bez_pl($str);
    $str = iconv('UTF-8', 'ISO-8859-1//TRANSLIT//IGNORE', $str);
    return $str;
}

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Worksheet\AutoFilter;

function downloadAll($cols)
{
    // Inicjalizuj obiekt PhpSpreadsheet
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();

    // Pobierz kolumny z sesji
    $cols = $_SESSION['plasticonDigitalUser']['oms']['artCols'];

    // Znajdź indeks kolumny 'WPS'
    $wpsIndex = array_search('WPS', $cols);

    // Dodaj 'WPSR' po 'WPS', jeśli istnieje
    if ($wpsIndex !== false) {
        array_splice($cols, $wpsIndex + 1, 0, 'WPSR');
    }

    // Znajdź indeks kolumny 'FE' ponownie, ponieważ tablica się zmieniła
    $feIndex = array_search('FE', $cols);

    // Dodaj 'FER' po 'FE', jeśli istnieje
    if ($feIndex !== false) {
        array_splice($cols, $feIndex + 1, 0, 'FER');
    }

    $header = [];
    $colsQueryDef = '';
    $definitions = [
        'status' => "s.status,",
        'offerNo' => "s.offerNo,",
        'SON' => "s.SON,",
        'PON' => "s.PON,",
        'projectManagerFull' => "s.projectManagerFull as PM,",
        'supportingManagerFull' => "s.supportingManagerFull as SM,",
        'name' => "s.name as Name,",
        'hours' => "s.hours,",
        'rbhWgTechnologii' => "ROUND(s.rbhWgTechnologii) as `Tech. hours`,",
        'realHours' => "CONCAT(s.PON,'[-]realHours') as realHours,",
        'weight' => "s.weight,",
        'clientName' => "p.clientName,",
        'SC' => "p.SC,",
        'PC' => "s.PC,",
        'diameter' => "s.diameter,",
        'material' => "CONCAT(s.linerMaterial, s.staticMaterial) as material,",
        'OD' => "s.orderDate AS OD,",
        'SCS' => "sc.start_plan as SCS,",
        'SCSR' => "sc.start_real as SCSR,",
        'SCE' => "sc.end_plan as SCE,",
        'SCER' => "sc.end_real as SCER,",
        'DDS' => "dd.start_plan as DDS,",
        'DDSR' => "dd.start_real as DDSR,",
        'DDE' => "dd.end_plan as DDE,",
        'DDER' => "dd.end_real as DDER,",
        'AOES' => "aoe.start_plan as AOES,",
        'AOESR' => "aoe.start_real as AOESR,",
        'AOEE' => "aoe.end_plan as AOEE,",
        'AOEER' => "aoe.end_real as AOEER,",
        'WPS' => "wp.start_plan as WPS,",
        'WPSR' => "wp.start_real as WPSR,",
        'WPE' => "wp.end_plan as WPE,",
        'WPER' => "wp.end_real as WPER,",
        'DFPSR' => "dp.start_real as DFPSR,",
        'DPE' => "dp.end_plan as DPE,",
        'FS' => "f.start_plan as FS,",
        'FSR' => "f.start_real as FSR,",
        'FE' => "f.end_plan as FE,",
        'FER' => "f.end_real as FER,",
        'QCE' => "qc.end_plan as QCE,",
        'LDRS' => "qc.contract_date_real as LDRS,",
        'TS' => "t.start_plan as TS,",
        'TE' => "t.end_plan as TE,",
        'TER' => "t.end_real as TER,",
        'TC' => "t.contract_date as TC,",
        'TCR' => "t.contract_date_real as TCR,",
        'frozen' => "REPLACE(REPLACE(s.frozen, 0, 'No'), 1, 'Yes') as 'Fabrication period frozen',",
        'isCritical' => "REPLACE(REPLACE(s.isCritical, 0, 'No'), 1, 'Yes') as Critical,",
        'penalties' => "REPLACE(REPLACE(p.penalties, 0, 'No'), 1, 'Yes') as Penalties,",
        'lessonsLearned' => "REPLACE(REPLACE(s.lessonsLearned, 0, 'No'), 1, 'Yes') as 'Discuss on meeting',",
        'nobo' => "REPLACE(REPLACE(s.nobo, 0, 'No'), 1, 'Yes') as NOBO,",
        'ped' => "REPLACE(REPLACE(s.ped, 0, 'No'), 1, 'Yes') as PED,",
        'note' => "n.note,"
    ];

    foreach ($cols as $col) {
        if (!$definitions[$col]) {
            continue;
        }

        $colsQueryDef .= $definitions[$col] ?? '';
        $header[] = $col;
    }

    $egz = connectEGZ();

    if ($egz) {
        $resultEGZ = $egz->query("SELECT DISTINCT(zlecenie) as zlecenie, ROUND(SUM(ilosc_godzin)) as ile FROM egz_registry WHERE (brygada='DE' OR brygada='D1' OR brygada='D2' OR brygada='KO' OR brygada='KT' OR brygada='K' OR brygada='M1' OR brygada='M2' OR brygada='M3' OR brygada='S' OR brygada='TA' OR brygada='N' OR brygada='RT' OR brygada='R') GROUP BY zlecenie");

        $hours = [];

        while ($rowEGZ = $resultEGZ->fetch_object()) {
            $hours[$rowEGZ->zlecenie] = round($rowEGZ->ile);
        }

        $egz->close();
    }

    $colsQueryDef = rtrim($colsQueryDef, ",");
    $searchQuery = $_SESSION['articlesSearchQuerry'];

    $selectMilestonesDatesByArticleAndMilestone = function ($articleId, $milestoneIdsArray) {
        $sql = "";

        foreach ($milestoneIdsArray as $milestoneId => $milestoneName) {
            $revStatusCondition = $milestoneId == 2 ? "AND rev_status = 0" : "";

            $sql .= "LEFT JOIN (
                        SELECT *
                        FROM (
                            SELECT *,
                                   ROW_NUMBER() OVER (PARTITION BY a_id ORDER BY revision ASC) AS rn
                            FROM milestones.dates
                            WHERE m_id = $milestoneId $revStatusCondition
                        ) ranked_$milestoneName
                        WHERE rn = 1
                    ) $milestoneName ON ($articleId = $milestoneName.a_id) ";
        }

        return $sql;
    };

    $milestonesIdNamesArray = [
        1 => 'sc',
        2 => 'dd',
        3 => 'aoe',
        5 => 'wp',
        6 => 'dp',
        7 => 'f',
        8 => 'qc',
        9 => 't'
    ];

    $query = "SELECT $colsQueryDef, s.costGroup FROM salesArticles s 
    JOIN projects p ON s.offerNo=p.offerNo 
    LEFT JOIN (SELECT id, articleId, note FROM notes 
    WHERE id IN 
    (SELECT MAX(id) FROM notes GROUP BY articleId)) n ON (n.articleId = s.id) 
   {$selectMilestonesDatesByArticleAndMilestone('s.id',$milestonesIdNamesArray)}
    WHERE 1 $searchQuery ORDER BY s.PON DESC";

    $link = connect();
    $result = $link->query($query);

    $filename = "articles_" . date('Ymd') . ".xlsx";

    $sheet->fromArray($header, null, 'B1');

    // Ustaw kolor tła nagłówków
    $headerStyle = [
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'color' => ['rgb' => 'FFFF00'], // Kolor żółty dla nagłówków
        ],
        'font' => [
            'bold' => true,
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER,
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];
    $sheet->getStyle('A1:' . $sheet->getHighestColumn() . '1')->applyFromArray($headerStyle);

    // Dodaj filtrowanie i sortowanie
    $highestColumn = $sheet->getHighestColumn();
    $filterRange = 'A1:' . $highestColumn . '1';
    $sheet->setAutoFilter($filterRange);

    // Ustaw szerokość kolumn
    $sheet->getColumnDimension('A')->setWidth(5); // Zmniejszona szerokość kolumny LP

    $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestColumn());
    for ($col = 2; $col <= $highestColumnIndex; $col++) {
        $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col);
        $headerTitle = $sheet->getCell($columnLetter . '1')->getValue(); // Pobierz tytuł nagłówka
        // Ustaw szerokość kolumny 'note' na 200px (około 55-60 znaków)
        if ($headerTitle == 'note') {
            $sheet->getColumnDimension($columnLetter)->setWidth(60); // Odpowiada około 200px
        } else if ($headerTitle == 'name' || $headerTitle == 'client Name' || $headerTitle == 'material') {
            $sheet->getColumnDimension($columnLetter)->setWidth(25); // Odpowiada około 200px
        } else if ($headerTitle == 'real Hours' || $headerTitle == 'hours' || $headerTitle == 'tech. hours') {
            $sheet->getColumnDimension($columnLetter)->setWidth(10); // Odpowiada około 200px
        } else {
            $sheet->getColumnDimension($columnLetter)->setWidth(10); // Zwiększona szerokość pozostałych kolumn
        }
    }

    // Wyśrodkowanie tekstu we wszystkich komórkach
    $textAlignStyle = [
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER,
        ],
    ];
    $sheet->getStyle('A1:' . $sheet->getHighestColumn() . ($sheet->getHighestRow()))->applyFromArray($textAlignStyle);

    $rowIndex = 2;
    while ($row = $result->fetch_assoc()) {

        consoleLog($row);

        $pon = $row['PON'] ?? '';
        $rh = $hours[$pon] ?? '';
        $row = array_map(function ($value) use ($pon, $rh) {
            return str_replace($pon . '[-]realHours', $rh, $value);
        }, $row);

        // Usuń 'costGroup' z danych, ale zachowaj jego wartość
        $costGroup = $row['costGroup'];
        unset($row['costGroup']);

        // Wstaw numerację porządkową w pierwszej kolumnie
        $rowWithIndex = array_merge([$rowIndex - 1], $row);
        $sheet->fromArray($rowWithIndex, null, 'A' . $rowIndex);

        // Formatowanie warunkowe dla komórek z datami
        $dateColumns = ['DDS', 'DDE', 'AOES', 'AOEE', 'WPS', 'WPE', 'FS', 'FE'];
        $revisionColumns = ['DDSR', 'DDER', 'AOESR', 'AOEER', 'WPSR', 'WPER', 'FSR', 'FER'];

        foreach ($dateColumns as $index => $column) {
            $columnIndex = array_search($column, array_keys($row));
            if ($columnIndex !== false) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex + 2) . $rowIndex;
                $cellValue = $row[$column];
                $revisionValue = $row[$revisionColumns[$index]];

                $sheet->setCellValue($cellCoordinate, $cellValue);

                // Sprawdzanie warunków kolorowania
                if (($row['hours'] > 150 && ($costGroup == '*103' || $costGroup == '*104')) ||
                    $costGroup == '*101' || $costGroup == '*102'
                ) {

                    // Sprawdzanie czy data jest przeterminowana i nie ma daty rewizji
                    if (
                        $cellValue < date("Y-m-d") &&
                        $revisionValue == '0000-00-00' &&
                        $cellValue != "0000-00-00"
                    ) {

                        $sheet->getStyle($cellCoordinate)
                            ->getFont()
                            ->getColor()
                            ->setRGB('FF0000'); // Czerwony kolor
                    }
                }
            }
        }

        // Formatowanie warunkowe dla materiału (kolumna 'material')
        $materialColumnIndex = array_search('material', array_keys($row));
        if ($materialColumnIndex !== false) {
            $materialCellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($materialColumnIndex + 2) . $rowIndex;
            $materialValue = $row['material'];

            // Sprawdź, czy materiał nie zaczyna się od "CBL"
            if (!empty($materialValue) && strpos($materialValue, 'CBL') !== 0) {
                $sheet->getStyle($materialCellCoordinate)->getFont()->getColor()->setRGB('FFA500'); // Pomarańczowy kolor
            }
        }

        $rowIndex++;
    }

    // Pobierz najwyższy wiersz i kolumnę w arkuszu
    $highestRow = $sheet->getHighestRow();
    $highestColumn = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestColumn());

    // Iteruj przez wszystkie komórki w arkuszu
    for ($row = 1; $row <= $highestRow; $row++) {
        for ($col = 1; $col <= $highestColumn; $col++) {
            // Uzyskaj adres komórki
            $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col) . $row;
            // Pobierz wartość komórki
            $cellValue = $sheet->getCell($cellCoordinate)->getValue();

            // Sprawdź, czy wartość to '0000-00-00' i podmień na 'pusto'
            if ($cellValue === '0000-00-00') {
                $sheet->setCellValue($cellCoordinate, '');
            }
        }
    }

    // Zastosuj obramowania do wszystkich komórek
    $borderStyle = [
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN,
                'color' => ['argb' => 'FF000000'],
            ],
        ],
    ];
    $sheet->getStyle('A1:' . $sheet->getHighestColumn() . ($rowIndex - 1))->applyFromArray($borderStyle);

    $writer = new Xlsx($spreadsheet);

    // Zapisz plik do przeglądarki
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    $writer->save('php://output');

    exit;
}

function getEGZhours($orderNo)
{
    $link = null;
    $counter = 0;

    try {
        $link = connectEGZ();

        if ($link) {
            $result = $link->query("SELECT ROUND(SUM(ilosc_godzin)) as ile FROM egz_registry WHERE (brygada='DE' OR brygada='D1' OR brygada='D2' OR brygada='KO' OR brygada='KT' OR brygada='K' OR brygada='M1' OR brygada='M2' OR brygada='M3' OR brygada='S' OR brygada='TA' OR brygada='N' OR brygada='RT' OR brygada='R') AND zlecenie='$orderNo'");

            if ($result) {
                $row = $result->fetch_object();

                if ($row && $row->ile != "" && $row->ile != null) {
                    $counter = $row->ile;
                }
            }
        }
    } catch (Exception $e) {
        // Logowanie błędu
        error_log("Error in getEGZhours: " . $e->getMessage());

        // Opcjonalnie: wysłanie powiadomienia email
        // sendEmail("Error in getEGZhours", $e->getMessage());
    } finally {
        // Zamknięcie połączenia, jeśli zostało otwarte
        if ($link) {
            $link->close();
        }
    }

    return $counter;
}

function getEgzHoursByGroup($orderNo, $group)
{
    $link = null;
    $counter = 0;

    try {
        $link = connectEGZ();

        if ($link) {
            $result = $link->query("SELECT ROUND(SUM(ilosc_godzin)) as ile FROM egz_registry WHERE grupa_pracownicza LIKE '$group%' AND zlecenie='$orderNo'");

            if ($result) {
                $row = $result->fetch_object();

                if ($row && $row->ile != "" && $row->ile != null) {
                    $counter = $row->ile;
                }
            }
        }
    } catch (Exception $e) {
        // Logowanie błędu
        error_log("Error in getEgzHoursByGroup: " . $e->getMessage());

        // Opcjonalnie: wysłanie powiadomienia email
        // sendEmail("Error in getEgzHoursByGroup", $e->getMessage());
    } finally {
        // Zamknięcie połączenia, jeśli zostało otwarte
        if ($link) {
            $link->close();
        }
    }

    return $counter;
}

function getOfferInfo($offer)
{
    $link = connectCRM();

    $result = $link->query("SELECT * FROM offers WHERE offerNo='$offer'");
    $link->close();
    return $result->fetch_object();
}

function getOfferInfoCopiedFnFromCRM($offerNo)
{
    $link = connectCRM();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM offers WHERE offerNo='%s'",
        mysqli_real_escape_string($link, strip_tags($offerNo))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    if ($row)
        $clientInfo = getClientInfoCopiedFnFromCRM($row->client);
    $clientLongName = isset($clientInfo['clientLongName']) ? $clientInfo['clientLongName'] : "";
    $clientInfoCity = isset($clientInfo['city']) ? $clientInfo['city'] : "";
    $cmp = 0;
    $offer = array(
        'id' => "",
        'offerNo' => "",
        'oldOfferNo' => "",
        'OT' => "",
        'inquiryNo' => "",
        'client' => "",
        'clientLongName' => "",
        'clientCity' => "",
        'endClient' => "",
        'finalClient' => "",
        'endClientName' => "",
        'endclientContactPurchase' => "",
        'endclientContactTechnican' => "",
        'endclientInquiryNo' => "",
        'orderLocation' => "",
        'productionLocation' => "",
        'plantLocationCountry' => "",
        'plantLocationCity' => "",
        'deliveryDate' => "",
        'scope' => "",
        'oID' => "",
        'V' => "",
        'DN' => "",
        'm3' => "",
        'kg' => "",
        'inquiry' => "",
        'request' => "",
        'offer' => "",
        'order' => "",
        'requestedOrderDate' => "",
        'OVE' => "",
        'OV' => "",
        'GO' => "",
        'GET' => "",
        'GxG' => "",
        'OVEgg' => "",
        'AX' => "",
        'reason' => "",
        'WHP' => "",
        'WHS' => "",
        'costPrice' => "",
        'BT' => "",
        'orderValue' => "",
        'projectName' => "",
        'company' => "",
        'pm' => "",
        'inpm' => "",
        'clientOrderNo' => "",
        'note' => "",
        'InR' => "",
        'InID' => "",
        'SOC' => "",
        'step' => "",
        'orderNo' => "",
        'CMp' => "",
        'segment' => "",
        'projectId' => "",
        'productionOrderNo' => "",
        'productionPM' => "",
        'productionValue' => "",
        'nextContactDate' => "",
        'technican' => "",
        'purchase' => "",
        'F' => "",
        'InF' => "",
        'productionReservation' => "",
        'incoterms' => "",
        'competitor' => "",
        'requestedDeliveryDate' => "",
        'ORVCM' => "",
        'prodValCM' => "",
        'orderCompany' => "",
        'calcPersons' => "",
        'serviceStart' => "",
        'serviceEnd' => "",
        'mailRev' => ""
    );

    if ($row) {
        if ($row->costPrice != 0)
            $cmp = round(((($row->OVE) - ($row->costPrice)) * 100) / ($row->costPrice), 2);
        $offer = array(
            'id' => ($row->id),
            'offerNo' => ($row->offerNo),
            'oldOfferNo' => ($row->oldOfferNo),
            'OT' => ($row->OT),
            'inquiryNo' => ($row->inquiryNo),
            'client' => ($row->client),
            'clientLongName' => $clientLongName,
            'clientCity' => $clientInfoCity,
            'endClient' => ($row->finalClient),
            'finalClient' => ($row->finalClient),
            'endClientName' => getClientNameCopiedFnFromCRM($row->finalClient),
            'endclientContactPurchase' => ($row->endclientContactPurchase),
            'endclientContactTechnican' => ($row->endclientContactTechnican),
            'endclientInquiryNo' => ($row->endclientInquiryNo),
            'orderLocation' => ($row->orderLocation),
            'productionLocation' => ($row->productionLocation),
            'plantLocationCountry' => ($row->plantLocationCountry),
            'plantLocationCity' => ($row->plantLocationCity),
            'deliveryDate' => ($row->deliveryDate),
            'scope' => ($row->scope),
            'oID' => ($row->oID),
            'V' => ($row->V),
            'inquiry' => ($row->inquiry),
            'request' => ($row->request),
            'offer' => ($row->offer),
            'order' => ($row->order),
            'requestedOrderDate' => ($row->requestedOrderDate),
            'OVE' => ($row->OVE),
            'OV' => ($row->OV),
            'GO' => ($row->GO),
            'GET' => ($row->GET),
            'GxG' => ($row->GO) / 100 * ($row->GET),
            'OVEgg' => (($row->OVE) / 100) * (($row->GO) / 100) * ($row->GET),
            'AX' => ($row->AX),
            'reason' => ($row->reason),
            'WHP' => ($row->WHP),
            'WHS' => ($row->WHS),
            'costPrice' => ($row->costPrice),
            'BT' => ($row->BT),
            'orderValue' => ($row->orderValue),
            'projectName' => ($row->projectName),
            'company' => ($row->company),
            'pm' => ($row->PM),
            'inpm' => ($row->inPM),
            'clientOrderNo' => ($row->clientOrderNo),
            'note' => ($row->note),
            'InR' => ($row->InR),
            'InID' => ($row->InID),
            'SOC' => ($row->SOC),
            'step' => ($row->step),
            'orderNo' => ($row->orderNo),
            'CMp' => ($row->CMp),
            'segment' => ($row->segment),
            'projectId' => ($row->projectId),
            'productionOrderNo' => ($row->productionOrderNo),
            'productionPM' => ($row->productionPM),
            'productionValue' => ($row->productionValue),
            'nextContactDate' => ($row->nextContactDate),
            'technican' => ($row->technican),
            'purchase' => ($row->purchase),
            'F' => ($row->F),
            'InF' => ($row->InF),
            'productionReservation' => ($row->productionReservation),
            'incoterms' => ($row->incoterms),
            'competitor' => ($row->competitor),
            'requestedDeliveryDate' => ($row->requestedDeliveryDate),
            'ORVCM' => ($row->ORVCM),
            'prodValCM' => ($row->prodValCM),
            'orderCompany' => ($row->orderCompany),
            'calcPersons' => ($row->calcPersons),
            'serviceStart' => ($row->serviceStart),
            'serviceEnd' => ($row->serviceEnd),
            'mailRev' => ($row->mailRev)
        );
    }
    return $offer;
}

function setOrderStatus($offerNo)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT DISTINCT(status) FROM salesarticles WHERE offerNo='%s'",
        mysqli_real_escape_string($link, $offerNo)
    ));
    if ($result->num_rows == 1) {
        $row = $result->fetch_object();
        $link->query(sprintf(
            "UPDATE projects SET status='%s' WHERE offerNo='%s'",
            mysqli_real_escape_string($link, $row->status),
            mysqli_real_escape_string($link, $offerNo)
        ));
    } else if ($result->num_rows == 2 || $result->num_rows == 3) {
        $status = "";
        $statuses = [];
        while ($row = $result->fetch_object())
            array_push($statuses, $row->status);
        if (in_array("Delayed", $statuses))
            $status = "Delayed";
        else if (in_array("In progress", $statuses))
            $status = "In progress";
        $link->query(sprintf(
            "UPDATE projects SET status='%s' WHERE offerNo='%s'",
            mysqli_real_escape_string($link, $status),
            mysqli_real_escape_string($link, $offerNo)
        ));
    }
    $link->close();
}

function getCompanies()
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM companies");
    $link->close();
    $output = "";
    while ($row = $result->fetch_object())
        $output .= "<option value='" . $row->shortcut . "'>" . $row->name . "</option>";
    return $output;
}

function getAllPms()
{
    $users = connectUsers();
    $result = $users->query("SELECT * FROM users u JOIN oms o ON u.id=o.userId WHERE o.pm=1 ORDER BY id ASC");
    $users->close();
    $output = "";
    while ($row = $result->fetch_object())
        $output .= "<option value='" . $row->id . "'>" . $row->imie . " " . $row->nazwisko . "</option>";
    return $output;
}

function getStatsFilters($id)
{
    $users = connectUsers();
    $result = $users->query(sprintf(
        "SELECT * FROM omsstatsfilters WHERE userId='%s'",
        mysqli_real_escape_string($users, strip_tags($id))
    ));
    $users->close();
    if ($result->num_rows != 0)
        return $result->fetch_object();
    else
        return 0;
}

function kategoria($b)
{
    switch ($b) {
        case 'DE':
            return 'BP';
            break;
        case 'D1':
            return 'BP';
            break;
        case 'D2':
            return 'BP';
            break;
        case 'SPR':
            return 'PP';
            break;
        case 'KO':
            return 'BP';
            break;
        case 'KRA':
            return 'PP';
            break;
        case 'KT':
            return 'BP';
            break;
        case 'K':
            return 'BP';
            break;
        case 'MAG':
            return 'PP';
            break;
        case 'M1':
            return 'BP';
            break;
        case 'M2':
            return 'BP';
            break;
        case 'M3':
            return 'BP';
            break;
        case 'M4':
            return 'BP';
            break;
        case 'MR':
            return 'BP';
            break;
        case 'SRW':
            return 'PP';
            break;
        case 'STA':
            return 'PP';
            break;
        case 'S':
            return 'BP';
            break;
        case 'TA':
            return 'BP';
            break;
        case 'NAR':
            return 'PP';
            break;
        case 'N':
            return 'BP';
            break;
        case 'PLO':
            return 'PP';
            break;
        case 'RT':
            return 'BP';
            break;
        case 'R':
            return 'BP';
            break;
        case 'NSZZ':
            return 'PP';
            break;
        case 'STO':
            return 'PP';
            break;
        case 'SZ':
            return 'PP';
            break;
        case 'TOK':
            return 'PP';
            break;
        case 'TRA':
            return 'PP';
            break;
        case 'ZAR':
            return 'PP';
            break;
    }
}

function costGroup($zlec)
{
    if ($zlec == 10000)
        return '*108';
    if ($zlec == 22222)
        return '*201';
    if ($zlec == 66666)
        return '*108';
    if ($zlec == 88880)
        return '*108';
    if ($zlec == 88888)
        return '*205';
    if ($zlec == 33333)
        return '*305';
    if ($zlec == '00156')
        return '*103';
    if ($zlec == "200000/1")
        return '*200';
    if ($zlec == "K200001")
        return '*100';
    $spec = substr($zlec, 0, 2);
    if ($spec == '00')
        return '*102';
    if (strlen($zlec) == 6) {
        $zlec = intval(substr($zlec, 2, 4));
        if ($zlec >= 3000 && $zlec <= 3300)
            return '*103';
        if ($zlec >= 3301 && $zlec <= 3700)
            return '*104';
        if ($zlec >= 3701 && $zlec <= 3999)
            return '*103';
        if ($zlec >= 7001 && $zlec <= 7300)
            return '*103';
        if ($zlec >= 7301 && $zlec <= 7700)
            return '*104';
        if ($zlec >= 7701 && $zlec <= 7999)
            return '*103';
        if ($zlec >= 0 && $zlec <= 300)
            return '*101';
        if ($zlec >= 301 && $zlec <= 700)
            return '*102';
        if ($zlec >= 701 && $zlec <= 999)
            return '*101';
        if ($zlec >= 6001 && $zlec <= 6300)
            return '*106';
        if ($zlec >= 6301 && $zlec <= 6700)
            return '*106';
        if ($zlec >= 6701 && $zlec <= 6999)
            return '*106';
        if ($zlec >= 9000 && $zlec <= 9200)
            return '*105';
        if ($zlec >= 9201 && $zlec <= 9300)
            return '*105';
        if ($zlec >= 9301 && $zlec <= 9500)
            return '*105';
        if ($zlec >= 9601 && $zlec <= 9700)
            return '*109';
        if ($zlec >= 9701 && $zlec <= 9800)
            return '*109';
        if ($zlec >= 9801 && $zlec <= 9900)
            return '*110';
        if ($zlec >= 9901 && $zlec <= 9999)
            return '*110';
        return 'blad';
    } else {
        return 'blad';
    }
}

function miesiacZZ($m)
{
    switch ($m) {
        case "01":
            return "Styczeń";
            break;
        case "02":
            return "Luty";
            break;
        case "03":
            return "Marzec";
            break;
        case "04":
            return "Kwiecień";
            break;
        case "05":
            return "Maj";
            break;
        case "06":
            return "Czerwiec";
            break;
        case "07":
            return "Lipiec";
            break;
        case "08":
            return "Sierpień";
            break;
        case "09":
            return "Wrzesień";
            break;
        case "10":
            return "Październik";
            break;
        case "11":
            return "Listopad";
            break;
        case "12":
            return "Grudzień";
            break;
    }
}

function getPrognosisAmount($id)
{
    $link = connect();

    $result = $link->query(sprintf(
        "SELECT * FROM prognosis p JOIN prognosisvalues pv ON (pv.prognosisId = p.id) WHERE p.articleId='%s' AND p.id=(SELECT MAX(id) FROM prognosis WHERE articleId='%s')",
        mysqli_real_escape_string($link, strip_tags($id)),
        mysqli_real_escape_string($link, strip_tags($id))
    ));

    $link->close();

    return $result->num_rows;
}

function getLatestPrognosis($id, $pon, $exchangeRate)
{
    $link = connect();

    $result = $link->query(sprintf(
        "SELECT * FROM prognosis p JOIN prognosisvalues pv ON (pv.prognosisId = p.id) WHERE p.articleId='%s' AND p.id=(SELECT MAX(id) FROM prognosis WHERE articleId='%s')",
        mysqli_real_escape_string($link, strip_tags($id)),
        mysqli_real_escape_string($link, strip_tags($id))
    ));

    $link->close();

    $prognosis = [];

    if ($result->num_rows != 0) {
        while ($row = $result->fetch_object()) {
            $prognosis[$row->cost] = [$row->hours, $row->price_euro, $row->price_accounting_euro];
        }

        return ["results" => $prognosis, "status" => false];
    } else {
        $link = connectExactPP();
        $result = sqlsrv_query($link, "exec dbo.PLASTICONP_GetProjectDataMIKA $pon");

        if ($result) {
            $budget = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
            sqlsrv_next_result($result);

            while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
                $key = $row['AKZ'];

                if (in_array($key, GROUPED_LCS_ARRAY)) {
                    $prognosis[$key][0] = $budget['bdg_pr'];
                } else if ($row['AKZ'] == "LC01") {
                    $prognosis[$row['AKZ']][0] = $budget['bdg_pm'];
                } else if ($row['AKZ'] == "LC02") {
                    $prognosis[$row['AKZ']][0] = $budget['bdg_en'];
                } else {
                    $prognosis[$row['AKZ']][0] = $budget['bdg_pr'];
                }

                $prognosis[$row['AKZ']][1] += $row['bdg'];
            }

            foreach ($prognosis as $key => $value) {
                $prognosis[$key][1] = $value[1] / $exchangeRate;
            }

            return ["results" => $prognosis, "status" => true];
        }
    }

    return false;
}

function translateAkz($akz)
{
    switch ($akz) {
        case 'TPC02':
            return "TP02";
            break;
        case 'TPC07':
            return "TP07";
            break;
        case 'TPC10':
            return "TP10";
            break;
        default:
            return $akz;
    }
}

function translateAkzBck($akz)
{
    switch ($akz) {
        case 'TP02':
            return "TPC02";
            break;
        case 'TP07':
            return "TPC07";
            break;
        case 'TP10':
            return "TPC10";
            break;
        default:
            return $akz;
    }
}

function getLatestPrognosisSum($id)
{
    $oms = connect();

    $res = $oms->query(sprintf(
        "SELECT SUM(pv.price_euro) as prc FROM prognosis p LEFT JOIN prognosisvalues pv ON (pv.prognosisId = p.id) WHERE p.articleId='%s' AND p.id=(SELECT MAX(id) FROM prognosis WHERE articleId='%s')",
        mysqli_real_escape_string($oms, strip_tags($id)),
        mysqli_real_escape_string($oms, strip_tags($id))
    ));

    $oms->close();
    $prow = $res->fetch_object();

    return $prow->prc;
}

function getLatestCostsSum($id, $pon)
{
    $act = 0;

    $link = connectExactPP();
    $result = sqlsrv_query($link, "exec dbo.PLASTICONP_GetProjectDataMIKA " . $pon);

    if ($result) {
        $budget = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
        sqlsrv_next_result($result);
        while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
            if (substr($row['AKZ'], 0, 1) == "L" || substr($row['AKZ'], 0, 1) == "M" || substr($row['AKZ'], 0, 1) == "T")
                $act += $row['act'];
        }
        sqlsrv_close($link);
    }

    return $act;
}

function getLatestCostsSumGroup($pon)
{
    $link = connectExactPP();
    $result = sqlsrv_query($link, "exec dbo.PLASTICONP_GetProjectsGroupDataMIKA " . $pon);
    $budget = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC);
    sqlsrv_next_result($result);
    $act = 0;
    while ($row = sqlsrv_fetch_array($result, SQLSRV_FETCH_ASSOC)) {
        if (substr($row['AKZ'], 0, 1) == "L" || substr($row['AKZ'], 0, 1) == "M" || substr($row['AKZ'], 0, 1) == "T")
            $act += $row['act'];
    }
    sqlsrv_close($link);
    return $act;
}

function getLatestValueSum($projectid)
{
    $conn_exact = new PDO("sqlsrv:Server=***********;Database=200;TrustServerCertificate=yes;Encrypt=no;", "sa_full", "ufWuG7ELx#1");

    $query = $conn_exact->prepare("exec [dbo].[PLASTICONP_GetProjectData] :projectid");
    $query->execute(array(':projectid' => $projectid));

    $r1 = $query->fetchAll(PDO::FETCH_ASSOC);

    $query->nextRowset();
    $r2 = $query->fetchAll(PDO::FETCH_ASSOC);

    $query->nextRowset();
    $r3 = $query->fetchAll(PDO::FETCH_ASSOC);
    return $r3[0]['Wartosc_ZS_PLN'];
}

function getLastSalesArticleNote($id)
{
    $link = connect();
    $result = $link->query(sprintf(
        "SELECT note FROM notes WHERE id IN (SELECT MAX(id) FROM notes WHERE articleId='%s')",
        mysqli_real_escape_string($link, $id)
    ));
    $link->close();
    if ($result->num_rows != 0) {
        $row = $result->fetch_object();
        return $row->note;
    }
    return "";
}

function getEgzRealHours($z)
{
    $link = connectEGZ();

    $sumaBPlhw = 0;
    $godziny_lhw = 0;
    $godziny_lhwIn = 0;

    if ($link) {
        $result_bryg = $link->query("SELECT * FROM brygady");
        $i = 2;
        $od = "0000-00-00";
        $od_w = "---";
        $do = "2100-12-31";
        $do_w = "---";
        $sumaPPlho = 0;
        $sumaPPlhw = 0;
        $sumaBPlho = 0;
        $sumaBPlhw = 0;

        $suma_sum = 0;
        $suma_lhw = 0;
        $suma_lho = 0;

        while ($brygada = $result_bryg->fetch_object()) {
            $br = $brygada->brygada;
            $id_bryg = $brygada->symbol;
            $result_lhw = $link->query("SELECT SUM(ilosc_godzin) as ile FROM egz_registry WHERE ((data BETWEEN '$od' AND '$do') OR (data='$od' OR data='$do')) AND brygada='$id_bryg' AND kod_godzin='LHW' AND zlecenie='$z'");
            $result_lho = $link->query("SELECT SUM(ilosc_godzin) as ile FROM egz_registry WHERE ((data BETWEEN '$od' AND '$do') OR (data='$od' OR data='$do')) AND brygada='$id_bryg' AND kod_godzin='LHO' AND zlecenie='$z'");
            $row_lhw = $result_lhw->fetch_object();
            $row_lho = $result_lho->fetch_object();
            $godziny_lhw = $row_lhw->ile;
            $godziny_lho = $row_lho->ile;

            if (empty($godziny_lhw)) {
                $godziny_lhw = 0;
            }

            if (empty($godziny_lho)) {
                $godziny_lho = 0;
            }

            $suma = $godziny_lho + $godziny_lhw;
            $suma_sum += $suma;
            $suma_lhw += $godziny_lhw;
            $suma_lho += $godziny_lho;
            $kategoria = kategoria($id_bryg);

            if ($kategoria == "PP") {
                $sumaPPlho += $godziny_lho;
                $sumaPPlhw += $godziny_lhw;
            }
            if ($kategoria == "BP") {
                $sumaBPlho += $godziny_lho;
                $sumaBPlhw += $godziny_lhw;
            }
            $i++;
        }
        $result_lhw = $link->query("SELECT SUM(ilosc_godzin) as ile FROM egz_registry WHERE ((data BETWEEN '$od' AND '$do') OR (data='$od' OR data='$do')) AND grupa_pracownicza='PM' AND kod_godzin='LHW' AND zlecenie='$z'");
        $row_lhw = $result_lhw->fetch_object();
        $godziny_lhw = $row_lhw->ile;
        if (empty($godziny_lhw))
            $godziny_lhw = 0;
        $result_lhw = $link->query("SELECT SUM(ilosc_godzin) as ile FROM egz_registry WHERE ((data BETWEEN '$od' AND '$do') OR (data='$od' OR data='$do')) AND (grupa_pracownicza='IN1' OR grupa_pracownicza='IN2' OR grupa_pracownicza='IN3' OR grupa_pracownicza='IN4') AND kod_godzin='LHW' AND zlecenie='$z'");
        $row_lhw = $result_lhw->fetch_object();
        $godziny_lhwIn = $row_lhw->ile;
        if (empty($godziny_lhwIn))
            $godziny_lhwIn = 0;
        if (empty($godziny_lho))
            $godziny_lho = 0;
        $link->close();
    }

    return [$sumaBPlhw, $godziny_lhw, $godziny_lhwIn];
}

function renderOptionsForSrFilterProjectsList($perm)
{
    $link = connectUsers();
    $result = $link->query("SELECT u.id, CONCAT(u.imie, ' ', u.nazwisko) AS full_name FROM users u JOIN oms o ON u.id=o.userId WHERE isActive='1' AND o.$perm='1'");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $full_name = $row->full_name;
        echo "<option value='" . $id . "'>" . $full_name . "</option>";
    }
    $link->close();
}

function detectIP()
{

    $ip = "";

    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
    } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    } else if (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }

    if (is_array($ip)) {
        $ip = "";
    }
    $ip = preg_replace('/[^a-zA-Z0-9,._=\+\()\-\:]/', "_", $ip);

    return $ip;
}

function saveSecLogOMS($offer_id, $desc)
{
    $link = connect();

    $idUserAction = 0;

    if (isset($_SESSION['plasticonDigitalUser']['id'])) {
        $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    }
    $ip = detectIP();

    $query = sprintf(
        "INSERT INTO seclogs (`userId`, `offer`, `details`, `ip`, `event_date`) VALUES ('%s','%s','%s','%s', NOW())",
        mysqli_real_escape_string($link, strip_tags($idUserAction)),
        mysqli_real_escape_string($link, strip_tags($offer_id)),
        mysqli_real_escape_string($link, strip_tags($desc)),
        mysqli_real_escape_string($link, strip_tags($ip))
    );

    $link->query($query);

    $link->close();
}

function getClientInfoCopiedFnFromCRM($id)
{
    $link = connectCRM();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM clients WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    $client = array(
        'id' => "",
        'clientLongName' => "",
        'enterprise' => "",
        'clientShortName' => "",
        'country' => "",
        'zip' => "",
        'city' => "",
        'adres' => "",
        'rei' => "",
        'market' => "",
        'type' => "",
        'kam' => "",
        'asm' => "",
        'de' => "",
        'pl' => "",
        'ep' => "",
        'noInq' => "",
        'volInq' => "",
        'noAxInqR' => "",
        'volAxInqR' => "",
        'noOrd' => "",
        'volOrd' => "",
        'cmOrd' => "",
        'tl' => "",
        'emailPurchase' => "",
        'emailTechnican' => "",
        'subType' => "",
        'hitrate' => "",
        'offers' => "",
        'orders' => "",
        'ordersValue' => "",
        'offersValue' => "",
        'region' => "",
        'url' => "",
        'email' => "",
        'phone' => "",
        'previousName' => "",
        'followUp' => "",
        'nextContactDate' => "",
        'vatNumber' => "",
        'invAddr' => "",
        'invExtraMA' => "",
        'invCity' => "",
        'invZip' => "",
        'invCountry' => "",
        'invIBAN' => "",
    );

    if ($row) {
        $client = array(
            'id' => ($row->id),
            'clientLongName' => ($row->clientLongName),
            'enterprise' => ($row->enterprise),
            'clientShortName' => ($row->clientShortName),
            'country' => ($row->country),
            'zip' => ($row->zip),
            'city' => ($row->city),
            'adres' => ($row->adres),
            'rei' => ($row->rei),
            'market' => ($row->market),
            'type' => ($row->type),
            'kam' => ($row->kam),
            'asm' => ($row->asm),
            'de' => ($row->de),
            'pl' => ($row->pl),
            'ep' => ($row->ep),
            'noInq' => ($row->noInq),
            'volInq' => ($row->volInq),
            'noAxInqR' => ($row->noAxInqR),
            'volAxInqR' => ($row->volAxInqR),
            'noOrd' => ($row->noOrd),
            'volOrd' => ($row->volOrd),
            'cmOrd' => ($row->cmOrd),
            'tl' => ($row->tl),
            'emailPurchase' => ($row->emailPurchase),
            'emailTechnican' => ($row->emailTechnican),
            'subType' => ($row->subType),
            'hitrate' => ($row->hitrate),
            'offers' => ($row->offers),
            'orders' => ($row->orders),
            'ordersValue' => ($row->ordersValue),
            'offersValue' => ($row->offersValue),
            'region' => ($row->region),
            'url' => ($row->url),
            'email' => ($row->email),
            'phone' => ($row->phone),
            'previousName' => ($row->previousName),
            'followUp' => ($row->followUp),
            'nextContactDate' => ($row->nextContactDate),
            'vatNumber' => ($row->vatNumber),
            'invAddr' => ($row->invAddr),
            'invExtraMA' => ($row->invExtraMA),
            'invCity' => ($row->invCity),
            'invZip' => ($row->invZip),
            'invCountry' => ($row->invCountry),
            'invIBAN' => ($row->invIBAN),
        );
        return $client;
    }
    return false;
}

// FUNCKJE OD ŁUKASZA

function getTableIdsToUpdateFilters()
{
    return ['OMS-articles'];
}

function getColumnsToAddInFiltersArray()
{
    $columns = [
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'OD',
            'index' => 17
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'SCSR',
            'index' => 19
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'DDSR',
            'index' => 23
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'AOESR',
            'index' => 27
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'DFPSR',
            'index' => 33
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'FSR',
            'index' => 36
        ]
    ];

    return $columns;
}

function increaseIndexOfFiltersColumns($columns, $index)
{
    // Increase indexes of all columns that are higher than index of the new column
    for ($i = count($columns); $i >= $index; $i--) {
        if (isset($columns[$i])) {
            $columns[$i + 1] = $columns[$i];
        }
    }

    return $columns;
}

function addNewColumnsToSavedFiltersArray($filters)
{
    if (empty($filters)) {
        return $filters;
    }

    $filters_columns = [];

    if (isset($filters['columns'])) {
        $filters_columns = $filters['columns'];
    }

    $columns_to_add = getColumnsToAddInFiltersArray();

    if (empty($columns_to_add)) {
        return $filters;
    }

    $new_filters_columns = $filters_columns;

    // Just in case sort new columns by index
    usort($columns_to_add, function ($a, $b) {
        return $a['index'] <=> $b['index'];
    });

    $existing_columns = [];

    foreach ($new_filters_columns as $key => $value) {
        if (isset($value['name'])) {
            $existing_columns[$value['name']] = $key;
        }
    }

    foreach ($columns_to_add as $column) {
        $index = $column['index'];
        $name = $column['name'];

        unset($column['index']);

        if (!isset($existing_columns[$name])) {
            $new_filters_columns = increaseIndexOfFiltersColumns($new_filters_columns, $index);
            $new_filters_columns[$index] = $column;
            $existing_columns[$name] = $index;
        }
    }

    // Sort just in case
    ksort($new_filters_columns);

    $filters['columns'] = $new_filters_columns;
    $filters['updated'] = true;

    return $filters;
}

function getMilestoneNameById($id)
{
    $link = connectMilestones();

    $result = $link->query(sprintf(
        "SELECT * FROM names WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    ));

    $link->close();

    $row = $result->fetch_array();

    return $row['name'];
}

function getArticleFolderLink($id)
{
    $article = getSalesArticleInfo($id);
    return $article['folder_link'];
}

function getSalesArticleOfferNo($article_id)
{
    $article = getSalesArticleInfo($article_id);
    return $article['offerNo'];
}

function checkIfSalesArticleFolderNameAsOfferNo($article_id)
{
    $article = getSalesArticleInfo($article_id);
    return ($article['folder_link'] == $article['offerNo']);
}

// retuns idthat should be used in sales article dir folder path. for old way it must be article id, else it must be offerNo
function getIdToSADirPath($article_id)
{
    $is_new_way = checkIfSalesArticleFolderNameAsOfferNo($article_id);

    if (!$is_new_way) {
        return $article_id;
    }

    $offer_no = getSalesArticleOfferNo($article_id);

    return $offer_no;
}

function getMilestonesCountByIds($link, $milestoneId, $articleId)
{
    $findMaxRevisionSql = "SELECT COUNT(*) as count FROM dates WHERE m_id='%s' AND a_id='%s'";
    $sanitizedMaxRevisionSql = sprintf(
        $findMaxRevisionSql,
        mysqli_real_escape_string($link, strip_tags($milestoneId)),
        mysqli_real_escape_string($link, strip_tags($articleId))
    );
    $maxRevisionResult = $link->query($sanitizedMaxRevisionSql);

    // Fetch the count row
    $maxRevisionRow = $maxRevisionResult->fetch_object();

    // Get the count
    return $maxRevisionRow->count;
}

function checkIfMilestoneExists($link, $milestoneId, $articleId)
{
    $sql = "SELECT * FROM dates WHERE m_id='%s' AND a_id='%s'";
    $sanitizedSql = sprintf(
        $sql,
        mysqli_real_escape_string($link, strip_tags($milestoneId)),
        mysqli_real_escape_string($link, strip_tags($articleId))
    );
    $result = $link->query($sanitizedSql);

    if ($result->num_rows > 0) {
        return true;
    } else {
        return false;
    }
}

function selectUsersPermWithSelected(?string $perm = "", ?string $id = ""): void
{
    if ($perm === null) {
        return;
    }
    if ($id === null) {
        $id = 0;
    }
    $link = connectUsers();
    $sql = "SELECT `u`.* FROM `users` `u` JOIN `oms` `o` ON `u`.`id`=`o`.`userId` WHERE `u`.`isActive`='1' AND `o`.`$perm`='1'";
    $result = $link->query($sql);
    while ($row = $result->fetch_object()) {
        $selected = "";
        if ($row->id === $id) {
            $selected = "selected";
        }
        echo "<option value='" . $row->id . "' $selected>" . $row->imie . " " . $row->nazwisko . "</option>";
    }
    $link->close();
}

function articlesGroupsMapSimplified()
{
    return [
        1 => 'Vessel',
        2 => 'Lining',
        3 => 'Special',
        4 => 'Piping',
        5 => 'Service',
        6 => 'Other'
    ];
}

function articlesSegmentsMap()
{
    return [
        1 => [
            'Tanks/Apparatus≤4000mm',
            'Storage tank GRP',
            'Storage tank GRP 3D',
            'Storage tank Dual',
            'Pressure vessel',
            'Scrubber GRP',
            'Scrubber Dual',
            'Rectangular vessels',
            'Tanks/Apparatus>4000mm GRP',
            'Tanks/Apparatus>4000mm Dual',
            'Tanks/Apparatus >4000mm hand lay-up and assembly on site GRP',
            'Tanks/Apparatus >4000mm hand lay-up and assembly on site Dual',
            'Fully fluorinated liner/GRP',
            'Pure TP'
        ],
        2 => [
            'Loose lining',
            'Sheet lining',
            'Fixpoint lining'
        ],
        3 => [
            'Wet ESP',
            'Ducts round',
            'Ducts rectangular',
            'Chimney & Stacks <=4000mm',
            'Chimney & Stacks >4000mm',
            // 'Other'
        ],
        4 => [
            'Process Piping 25-600mm GRP',
            'Process Piping 25-600mm Dual',
            'Pipelines 601mm-1500mm GRP',
            'Pipelines 601mm-1500mm Dual'
        ],
        5 => [
            'Installation',
            'Daily service',
            'Revamping',
            'Shutdowns & Turnarounds',
            'Framework Contracts'
        ],
        6 => [
            'Spare Parts',
            'External Trade Components',
            'Change Order'
        ]
    ];
}

function addMilestoneChangeMailBeginning($project, $article)
{
    $content = "CRM - " . $project['offerNo'] . " | " . $project['SC'] . " - " . $article['SON'] . " | " . $article['PC'] . " - " . $article['PON'];

    $content .= "<br><br>";

    $content .= "Scope: " . $article['name'] . "<br>";
    $content .= "SON: " . $article['SON'] . " - " .  $project['SC'] .  "<br>";
    $content .= "PON: " . $article['PON'] . " - " .  $project['PC'] . "<br>";
    $content .= "Client: " . $project['clientName'] . "<br>";

    return $content;
}
