<?php
include("functions.php");
$link=connect();
## Read value
$draw = $_POST['draw'];
$id=$_POST['id'];

## Fetch records
$empQuery = "SELECT * FROM clientsresponsibility WHERE clientId='$id'"; 
$empRecords = $link->query($empQuery);
$prevEmpRecords = $link->query($prevEmpQuery);
$data = array();
while ($row = $empRecords->fetch_object()) {
	$actions="<a href='#' onclick='matrixDelInfo(".$row->id.")'>Delete</a> | <a href='#' onclick='matrixEditInfo(".$row->id.")'>Edit</a>";
    $data[] = array(
    		"company"=>$row->company,
    		"inTL"=>$row->inTL,
    		"inKAM"=>$row->inKAM,
    		"inASM"=>$row->inASM,
    		"actions"=>$actions
    	);
}

## Response
$response = array(
    "draw" => intval($draw),
    "iTotalRecords" => 0,
    "iTotalDisplayRecords" => 0,
    "aaData" => $data
);

echo json_encode($response);
?>
