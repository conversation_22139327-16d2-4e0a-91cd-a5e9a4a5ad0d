<?php
require('header.php');
/*
 * ROOT OFFERS DIRECTORY
 */
define("ROOT_OFFERS_DIR", '../../offers/');
$id = (filter_input(INPUT_GET, "id") !== null) ? filter_input(INPUT_GET, "id") : '0';
if ($_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1 || $_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 1) {
    if (hasRightsForOffer($id, $_SESSION['plasticonDigitalUser']['id']) == FALSE) {
        header("Location: index.php");
        die();
    }
}
$offer = getOfferInfo($id);
$client = getClientInfo($offer['client']);
$endclient = getClientInfo(intval($offer['endClient']));
$nextCnt = getLastContact($offer['id']);
$project = getProjectInfo($offer['projectId']);
$offerNo = array_key_exists('offerNo', $offer) ? $offer['offerNo'] : "";
$salesArticles = getSalesArticleInfo($offerNo);
$_SESSION['title'] = 1;
saveSecLog($offer['offerNo'], "Access to offer");
if (empty($offer['folder_link'])) {
    if (!empty(glob(ROOT_OFFERS_DIR . $offerNo . "*"))) {
        $dirname = (glob(ROOT_OFFERS_DIR . $offerNo . "*")[0]);
        if (is_dir($dirname)) {
            $link = connect();
            $link->query(sprintf(
                "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                mysqli_real_escape_string($link, str_replace(ROOT_OFFERS_DIR, "", $dirname)),
                mysqli_real_escape_string($link, $offer['offerNo'])
            ));
            $link->close();
        }
    }
}
$user = getUserInfo($_SESSION['plasticonDigitalUser']['id']);
$tmpObjectOffer = (object) $offer;
/*
 * OFFERS LINKS
 */
$offersLinks = [
    'gce' => Utilities::getMediumGceOfferLink($tmpObjectOffer, $user),
    'wpc' => Utilities::getMediumWpcOfferLink($tmpObjectOffer, $user),
    'wtc' => Utilities::getMediumWtcOfferLink($tmpObjectOffer, $user),
    'oms' => Utilities::getMediumOmsOfferLink($salesArticles)
];
/*
 * LOAD Gce class
 */
$gce = new Gce($user);
/*
 * GCE Project risk level
 */
$projectRiskLevel = $gce->getProjectRiskLevel($offerNo);
/*
 * GCE Cash flow
 */
$cashFlow = $gce->getCashFlow($offerNo);
/*
 * GCE Client risk level object
 */
$gceClientRiskLevel = $gce->getProjectRiskLevelGroup('Client');
?>
<style>
    .dropdown-menu-OMS {
        padding-top: 10px !important;
        overflow: scroll;
        max-height: 600px;
    }

    .btn-xxs {
        padding: 0.15rem 0.3rem;
        font-size: 0.7rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    .select2-container {
        width: 200px !important;
        text-align: left;
    }

    .select2-container .select2-selection--single {
        height: 36px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 34px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
    }

    .select2-container--default .select2-selection--single {
        border: 1px solid #ced4da;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        font-size: 1rem;
        font-family: inherit;
        font-weight: 430;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__rendered {
        overflow-y: scroll;
        height: 100px;
    }

    .daterangepicker.dropdown-menu {
        z-index: 1060;
    }

    .daterangepicker select.yearselect {
        width: 100px;
    }

    td.details-control {
        background: url('https://datatables.net/examples/resources/details_open.png') no-repeat center center;
        cursor: pointer;
    }

    tr.shown td.details-control {
        background: url('https://datatables.net/examples/resources/details_close.png') no-repeat center center;
    }

    .control-table tr:hover {
        background-color: white !important;
    }

    td[colspan="4"]:hover {
        background-color: white !important;
    }

    td[colspan="3"]:hover {
        background-color: white !important;
    }

    .revClass {
        width: 30px;
        text-align: center;
    }

    .revIco {
        font-size: 26px;
        cursor: pointer;
    }

    .steps-container .step-icon {
        width: 80px;
        height: 80px;
        line-height: 60px;
    }

    .offerBox {
        height: auto !important;
        padding: 5px !important;
        width: 100% !important;
    }

    .dateTop {
        position: absolute;
        top: -20px;
        width: 80px;
        left: -5px;
    }

    .steps-container .step {
        margin-top: 24px !important;
    }

    #suboffers tbody tr {
        height: 45px;
    }

    #contact tbody tr {
        height: 45px;
    }

    #suboffersOrdered tbody tr {
        height: 45px;
    }
</style>
<script>
    function validateDates(idNextContactInput, idContactInput) {
        const contactDate = $(idContactInput).val();
        const nextContactDate = $(idNextContactInput).val();
        const contactDateObj = new Date(contactDate);
        const nextContactDateObj = new Date(nextContactDate);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set time to midnight for comparison

        let hasError = false;

        // Clear previous errors
        $(idNextContactInput).removeClass('borderRed');
        $(idContactInput).removeClass('borderRed');
        $('#foNextError, #foCurrentError').remove();

        // Check if next contact date is in the past
        if (nextContactDateObj < today) {
            $(idNextContactInput).addClass('borderRed').after('<span id="foNextError" class="text-danger">Next contact date cannot be set in the past.</span>');
            hasError = true;
        }

        // Check if contact date is after next contact date
        if (contactDate && nextContactDate && contactDateObj > nextContactDateObj) {
            $(idContactInput).addClass('borderRed').after('<span id="foCurrentError" class="text-danger">Contact date cannot be after next contact date.</span>');
            hasError = true;
        }

        // Check if dates are equal
        if (contactDate && nextContactDate && contactDateObj.getTime() === nextContactDateObj.getTime()) {
            $(idNextContactInput).addClass('borderRed').after('<span id="foNextError" class="text-danger">Next contact date cannot be equal to contact date.</span>');
            hasError = true;
        }

        return !hasError; // Return true if no errors
    }

    // Run validation on input change
    $(document).on('input change', '#foNext, #foCurrent', function() {
        validateDates('#foNext', '#foCurrent');
    });

    // Run validation on save button click
    $(document).on('click', 'input[name=saveFollow]', function(e) {
        if (!validateDates('#foNext', '#foCurrent')) {
            e.preventDefault(); // Stop form submission if errors exist
        }
    });

    $(document).on('input change', '#acNext, #acCurrent', function() {
        validateDates('#acNext', '#acCurrent');
    });

    $(document).on('click', 'input[name=acSave]', function(e) {
        if (!validateDates('#acNext', '#acCurrent')) {
            e.preventDefault(); // Stop form submission if errors exist
        }
    });

    function redBorder() {
        $('[required]').each(function() {
            if ($(this).hasClass("select2")) {
                if ($(this).val() == '' || $(this).val() == null)
                    $(this).next().children().children().addClass("borderRed");
                else
                    $(this).next().children().children().removeClass("borderRed");
            } else
            if ($(this).val() == '' || $(this).val() == null)
                $(this).addClass("borderRed");
            else
                $(this).removeClass("borderRed");
        })
        setTimeout(function() {
            $('.borderRed').removeClass('borderRed');
        }, 5000);
    }

    function getCities(client, pole) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $(pole).html(this.responseText).trigger('change').prop('disabled', false);
            }
        };
        n = encodeURIComponent(client);
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientLocationsAddOffer&value=" + n, true);
        xmlhttp.send();
    }

    function inqFlag(id) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                if (this.responseText != "")
                    $('#PmInqFlag').html("<label><br><img class='flag' src='assets/images/" + this.responseText + ".png'></label>");
                else
                    $('#PmInqFlag').html("");
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=inqFlag&value=" + id, true);
        xmlhttp.send();
    }

    function setTechEditValues(comp) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                data = this.responseText.split("[-]");
                $('[name="techScope"]').val(data[0]);
                $('[name="techSegment"]').val(data[1]).trigger("change");
                $('[name="stat"]').val(data[2]).trigger("change");
                $('[name="prot"]').val(data[3]).trigger("change");
                $('[name="medium"]').val(data[4]);
                $('[name="DN"]').val(data[5]);
                $('[name="m3"]').val(data[6]);
                $('[name="kg"]').val(data[7]);
                $('[name="pressure"]').val(data[8]).trigger("change");
                $('[name="componentType"]').val(data[9]).trigger("change");
                $('[name="whp"]').val(data[10]);
                $('[name="whs"]').val(data[11]);
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=setTechEditValues&value=" + comp, true);
        xmlhttp.send();
    }

    function setDeleteValues(file, fileName) {
        $('#deleteFileSpan').html(fileName);
        $('#fileName').val(file);
    }

    function setDelVal(val) {
        $('#delThisComp').attr('data-id', val);
    }

    function getCitiesEx(client) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#clientLocationEx').html(this.responseText).trigger('change');
            }
        };
        n = encodeURIComponent(client);
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientLocations&value=" + n, true);
        xmlhttp.send();
    }

    function addFile() {
        var a = document.getElementById('fileInput');
        if (a.value == "") {
            fileText.innerHTML = "";
        } else {
            var theSplit = a.value.split('\\');
            fileText.innerHTML = theSplit[theSplit.length - 1];
        }
    }

    function addFileAdd() {
        var a = document.getElementById('fileInputAdd');
        if (a.value == "") {
            fileTextAdd.innerHTML = "";
        } else {
            var theSplit = a.value.split('\\');
            fileTextAdd.innerHTML = theSplit[theSplit.length - 1];
        }
    }

    function setDeleteValuesCon(deleteId) {
        $('#deleteId').val(deleteId);
    }

    function setEditValuesCon(data) {
        data = data.split("[-]");
        $('#contactDateEdit').val(data[1]);
        $('#nextContactDateEdit').val(data[2]);
        $('#noteEdit').val(data[3]);
        $('#contactWithEdit').val(data[4]).trigger('change');
        $('#editId').val(data[0]);
    }

    function setRevValues(off) {
        $('#revOfferNo').val(off);
        $('#revOfferNoTitle').html(off);
    }

    function setCpValues(cmpId) {
        $('#copyCmpId').val(cmpId);
    }

    function setScopeEdit(id, scope, person, segment, prod) {
        $('#cmpIdScope').val(id);
        $('#cmpScope').val(scope);
        $('#calcPersonSelect').val(person).trigger('change');
        $('#cmpSegment').val(segment).trigger('change');
        $('#cmpProduction').val(prod).trigger('change');
    }

    function setComercialValues(id, value, cm) {
        $('#addCOmercialOVID').val(id);
        $('#comercialOVvalue').val(value);
        $('#comercialCM').val(cm);
    }

    function getContacts(client, purch, tech, end) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                if (end == 0) {
                    $(purch).html(this.responseText).prop("disabled", false);
                    $(tech).html(this.responseText).prop("disabled", false);

                    $(purch).val("<?= $offer['purchase']; ?>");
                    if ($(purch).val()) {
                        $(purch).trigger('change');
                    }
                    $(tech).val("<?= $offer['technican']; ?>");
                    if ($(tech).val()) {
                        $(tech).trigger('change');
                    }
                    console.log(purch, $(purch).val(), <?= $offer['purchase']; ?>);
                    if (client) {
                        $('#addConBtn').attr("disabled", false);
                    }
                } else {
                    $(purch).html(this.responseText).prop("disabled", false);
                    $(tech).html(this.responseText).prop("disabled", false);
                    $(purch).val("<?= $offer['endclientContactPurchase']; ?>");
                    if ($(purch).val()) {
                        $(purch).trigger('change');
                    }
                    $(tech).val("<?= $offer['endclientContactTechnican']; ?>");
                    if ($(tech).val()) {
                        $(tech).trigger('change');
                    }
                    if (client) {
                        $('#addEndConBtn').attr("disabled", false);
                    }
                }
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientContacts&value=" + client, true);
        xmlhttp.send();
    }

    function offerFormData(offerDate, GO, GET, competitor, OVE, ProdRes) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=offerFormData&offerDate=" + offerDate + "&GO=" + GO + "&GET=" + GET + "&competitor=" + competitor + "&OVE=" + OVE + "&ProdRes=" + ProdRes, true);
        xmlhttp.send();
    }

    function ProductionReser(that, text, saveBtn) {
        if ($(that).val() == 1) {
            $(text).removeClass('hidden');
            $(saveBtn).prop('disabled', true);
        } else {

            $(text).addClass('hidden');
            $(saveBtn).prop('disabled', false);
        }
    }
</script>

<body class="widescreen adminbody-void">
    <?php
    $_SESSION['url'] = 'offer.php';
    require('menu.php');
    if (substr($_SESSION['backbutton'], 0, 25) != "client-one-management.php")
        $_SESSION['plasticonDigitalUser']['crm']['clientsBack'] = "offer.php?id=$id";
    (strpos($_SESSION['backbutton'], '?') && $offer['id'] != "") ? $backbtn = $_SESSION['backbutton'] . "&backId=" . $offer['id'] : $backbtn = $_SESSION['backbutton'] . "?backId=" . $offer['id'];
    if ($_SESSION['backbutton'] == "articles.php") {

        $backbtn = "index.php?backId=" . $offer['id'];
    }

    if (isset($_GET['source'])) {
        if ($_GET['source'] == 'search') {
            $backbtn = "javascript:window.open('','_self').close()";
        }
    }



    $buttonsAX = "";
    if ($offer['AX'] != '')
        $buttonsAX = "disabled title='This offer is already an order or is already lost!'";
    ?>
    <div class="content-page">
        <div class="content-offer">
            <div class="container-fluid">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                    <div class="card mb-3">
                        <div class="card-body" style="padding-top:5px;">

                            <?php
                            if (isset($_GET['contact_error'])) {

                                $err_val = (int) $_GET['contact_error'];

                                $err_msg = "";

                                if ($err_val == 1) {
                                    $err_msg = "Next contact date cannot be set in past.";
                                }

                                if ($err_val == 2) {
                                    $err_msg = "Contact date cannot be set later than next contact date.";
                                }

                                if ($err_val == 3) {
                                    $err_msg = "Contact date and next contact date cannot be the same.";
                                }


                                alertDanger($err_msg);
                            }
                            ?>

                            <div class="row justify-content-center">
                                <div class="col-lg-1 text-center">
                                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?>
                                        <button class="btn btn-success" style="height:35px;width:50px;margin-top:25px;" data-toggle="modal" data-target="#changeStage"><i style="font-size:20px;padding-top:1px;" class="fas fa-clock"></i></button>
                                    <?php } ?><br>
                                    <a href="<?= $backbtn; ?>"><button class="btn btn-danger mt-1 mb-2">Close</button></a>
                                    <!--
                                            GCE, WPC and WTC links
                                        -->
                                    <?php
                                    foreach ($offersLinks as $offerLink) {
                                        echo $offerLink;
                                    }
                                    ?>
                                    <!--
                                            END OF: GCE, WPC and WTC links
                                        -->
                                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                        <div class="row mt-2"></div>
                                    <?php
                                        $offerDirErr = "<div class='row justify-content-center text-center'>Folder for this offer doesn`t exists.</div><div class='row justify-content-center text-center'><form method='POST'><button title='Create folder' type='submit' name='createFolder' class='form-control btn btn-primary'><i class='fas fa-folder-plus'></i></button></form></div>";
                                        if (!empty(glob(ROOT_OFFERS_DIR . $offer['offerNo'] . "*"))) {
                                            if (is_dir(glob(ROOT_OFFERS_DIR . $offer['offerNo'] . "*")[0])) {
                                                echo "<div class='row justify-content-center text-center'><a href='folder.php?id=" . $offer['id'] . "' ><button title='Open folder' class='btn btn-primary'><i class='fas fa-folder-open'></i></button></a></div>";
                                            } else {
                                                echo $offerDirErr;
                                            }
                                        } else {
                                            echo $offerDirErr;
                                        }
                                    }
                                    ?>
                                </div>
                                <div class="col-lg-2 text-center col-section">
                                    <div class="row header-section">
                                        <div class="col-lg-12">
                                            <div class="textSmall" title="">&nbsp;</div>
                                            <div class="textSmall" title="Inquiry date"><strong><?php
                                                                                                if ($offer['inquiry'] != '0000-00-00')
                                                                                                    echo dateFormat($offer['inquiry']);
                                                                                                else
                                                                                                    echo "&nbsp;";
                                                                                                ?></strong></div>
                                        </div>
                                    </div>
                                    <div class="row step-section justify-content-center">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['datesInfo'] == 2 && $_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?>
                                            <button class="btn btn-success position-absolute btn-edit" data-toggle="modal" data-target="#editDatesInfo">Edit</button>
                                        <?php } ?>
                                        <div class="bar-right step2"></div>
                                        <div class="step-circle pointer step1" title="Inquiry" data-toggle="modal" data-target="#inquiryModal"><i class="fa fa-dove step-ico" aria-hidden="true"></i></div>
                                    </div>
                                    <div class="row people-section justify-content-center">
                                        <strong title="Responsible"><?= getNameAndSurname($offer['V']); ?></strong>
                                    </div>
                                    <div class="row content-section ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-top" data-toggle="modal" data-target="#editClientInfo">Edit</button><?php } ?>
                                        <?php
                                        if (empty($offer['client']) || $offer['client'] == 0)
                                            echo "<div class='col-lg-12 textSmall font-italic mt20 text-center'>No reseller</div>";
                                        else {
                                        ?>
                                            <div class="col-lg-12">
                                                <div class="row justify-content-center textSmall font-italic color-grey m0 mt20" title="Enterprise"><?= $client['enterprise']; ?></div>
                                                <div class="row justify-content-center m0">
                                                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['clientManagement'] == 1) { ?>
                                                        <a href="client-one-management.php?id=<?= $offer['client']; ?>" title="Click to see client info"><?= $client['clientLongName']; ?></a>
                                                    <?php } else { ?>
                                                        <?= $client['clientLongName']; ?>
                                                    <?php } ?>
                                                </div>
                                                <div class="row justify-content-center textSmall color-plasticon-blue m0"><?= $client['city']; ?></div>
                                                <div class="row justify-content-center m0 mt10">
                                                    <div class="col-lg-3 textBig" title="Client category">
                                                        <strong><?= ($client['type'] == "New") ? "N" : $client['type']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?php
                                                            $hitrate = $client['hitrate'];
                                                            if ($hitrate < 31)
                                                                $bg = "bg-danger";
                                                            if ($hitrate >= 31 && $hitrate <= 50)
                                                                $bg = "bg-warning";
                                                            if ($hitrate > 50)
                                                                $bg = "bg-success";
                                                            ?>
                                                            <div class="progress" style="width:100px;margin:auto;">
                                                                <div class="progress-bar progress-bar-animated progress-bar-striped <?= $bg; ?>" role="progressbar" style="width:<?= $hitrate; ?>%;color:black;" aria-valuenow="<?= $hitrate; ?>" aria-valuemin="0" aria-valuemax="100"><?= $hitrate; ?>%</div>
                                                            </div>
                                                        </div>
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?= $client['ordersValue'] . " | " . $client['offersValue'] . " k&euro;"; ?>
                                                        </div>
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?= $client['orders'] . " | " . $client['offers']; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row font-size-22 justify-content-center m0" title="<?= translateMarket($client['market']); ?>">
                                                            <?= $client['market']; ?>
                                                        </div>
                                                        <div class="row justify-content-center m0">
                                                            <img src="assets/images/flags/<?= getCountryShortcut($client['country']); ?>.png" class="flag" title="<?= $client['country']; ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center m0 mt5"><a href="<?= $gceClientRiskLevel->getLink() ?>" target="_blank" title="Go To Client risk level">Client risk level</a></div>
                                                <div class="row justify-content-center m0">
                                                    <div class="progress" style="height: 18px; width: 100px; position: relative;">
                                                        <div class="progress-bar <?= $gceClientRiskLevel->getColorLevel() ?>" role="progressbar" style="width: <?= $gceClientRiskLevel->getPercentInString() . "%" ?>;" aria-valuenow="<?= $gceClientRiskLevel->getPercentInString() ?>" aria-valuemin="0" aria-valuemax="100">
                                                        </div>
                                                        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; align-items: center; justify-content: center; color: black; font-size: 14px; font-weight: normal;">
                                                            <?= $gceClientRiskLevel->getPercentInString() . "%" ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center m0 mt10">
                                                    <div class="border-1px-black">Inquiry no. <strong><?= $offer['inquiryNo']; ?></strong></div>
                                                </div>
                                                <div class="row justify-content-center m0 mt10">Procurement:&nbsp;<strong><?= getContactNameAndSurname($offer['purchase']); ?></strong></div>
                                                <div class="row justify-content-center m0 mt5">Technical:&nbsp;<strong><?= getContactNameAndSurname($offer['technican']); ?></strong></div>
                                            </div>
                                        <?php } ?>
                                    </div>
                                    <div class="row content-section mt0 ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-bottom" data-toggle="modal" data-target="#editEndClientInfo">Edit</button><?php } ?>
                                        <?php
                                        if (empty($offer['endClient']) || $offer['endClient'] == 0)
                                            echo "<div class='col-lg-12 textSmall font-italic mt20 text-center'>No end client</div>";
                                        else {
                                        ?>
                                            <div class="col-lg-12">
                                                <div class="row justify-content-center textSmall font-italic color-grey m0 mt20" title="Enterprise"><?= $endclient['enterprise']; ?></div>
                                                <div class="row justify-content-center m0">
                                                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['clientManagement'] == 1) { ?>
                                                        <a href="client-one-management.php?id=<?= $offer['endClient']; ?>" title="Click to see client info"><?= $endclient['clientLongName']; ?></a>
                                                    <?php } else { ?>
                                                        <?= $endclient['clientLongName']; ?>
                                                    <?php } ?>
                                                </div>
                                                <div class="row justify-content-center textSmall color-plasticon-blue m0"><?= $endclient['city']; ?></div>
                                                <div class="row justify-content-center m0 mt10">
                                                    <div class="col-lg-3 textBig" title="Client category">
                                                        <strong><?= ($endclient['type'] == "New") ? "N" : $endclient['type']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-6">
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?php
                                                            $hitrate = $endclient['hitrate'];
                                                            if ($hitrate < 31)
                                                                $bg = "bg-danger";
                                                            if ($hitrate >= 31 && $hitrate <= 50)
                                                                $bg = "bg-warning";
                                                            if ($hitrate > 50)
                                                                $bg = "bg-success";
                                                            ?>
                                                            <div class="progress" style="width:100px;margin:auto;">
                                                                <div class="progress-bar progress-bar-animated progress-bar-striped <?= $bg; ?>" role="progressbar" style="width:<?= $hitrate; ?>%;color:black;" aria-valuenow="<?= $hitrate; ?>" aria-valuemin="0" aria-valuemax="100"><?= $hitrate; ?>%</div>
                                                            </div>
                                                        </div>
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?= $endclient['ordersValue'] . " | " . $endclient['offersValue'] . " k&euro;"; ?>
                                                        </div>
                                                        <div class="row justify-content-center m0 mt5">
                                                            <?= $endclient['orders'] . " | " . $endclient['offers']; ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row font-size-22 justify-content-center m0" title="<?php if (isset($endclient['market'])) echo translateMarket($endclient['market']); ?>">
                                                            <?php if (isset($endclient['market'])) echo $endclient['market']; ?>
                                                        </div>
                                                        <div class="row justify-content-center m0">
                                                            <img src="assets/images/flags/<?= getCountryShortcut($endclient['country']); ?>.png" class="flag" title="<?= $endClient['country']; ?>">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center m0 mt10">Client risk level</div>
                                                <div class="row justify-content-center m0">
                                                    <div class="progress" style="width:100px;margin:auto;">
                                                        <div class="progress-bar progress-bar-animated progress-bar-striped" role="progressbar" style="width:0%;color:black;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center m0 mt20">
                                                    <div class="border-1px-black">Inquiry no. <strong><?= $offer['endclientInquiryNo']; ?></strong></div>
                                                </div>
                                                <div class="row justify-content-center m0 mt20">Procurement:&nbsp;<strong><?= getContactNameAndSurname($offer['endclientContactPurchase']); ?></strong></div>
                                                <div class="row justify-content-center m0 mt5">Technical:&nbsp;<strong><?= getContactNameAndSurname($offer['endclientContactTechnican']); ?></strong></div>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="col-lg-2 text-center col-section">
                                    <div class="row header-section">
                                        <div class="col-lg-12">
                                            <div class="textSmall" title="Requested quote date"><?php
                                                                                                if ($offer['request'] != '0000-00-00')
                                                                                                    echo dateFormat($offer['request']);
                                                                                                else
                                                                                                    echo "&nbsp;";
                                                                                                ?></div>
                                            <div class="textSmall" title="Quote date"><strong><?php
                                                                                                if ($offer['offer'] != '0000-00-00')
                                                                                                    echo dateFormat($offer['offer']);
                                                                                                else
                                                                                                    echo "&nbsp;";
                                                                                                ?></strong></div>
                                        </div>
                                    </div>
                                    <div class="row step-section justify-content-center">
                                        <div class="bar-left step2"></div>
                                        <div class="bar-right step3"></div>
                                        <div class="step-circle step2 pointer" title="Offer" data-toggle="modal" data-target="#offerModal"><i class="fa fa-file-alt step-ico" aria-hidden="true"></i></div>
                                    </div>
                                    <div class="row people-section justify-content-center">
                                        <strong title="Inside sales"><?= getNameAndSurname($offer['oID']) . "&nbsp;"; ?></strong>
                                    </div>
                                    <div class="row content-section ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-top" data-toggle="modal" data-target="#editSalesInfo">Edit</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="row m0 mt30">
                                                <div class="col-lg-6 text-left pr-0">
                                                    Project risk level
                                                </div>
                                                <div class="col-lg-6 text-right pl-0 pr-1">
                                                    <?= $projectRiskLevel ?>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-4 text-left pr-0">
                                                    Cash flow
                                                </div>
                                                <div class="col-lg-8 text-right pl-0 pr-1">
                                                    <?= $cashFlow ?>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-5 text-left">
                                                    Competition
                                                </div>
                                                <div class="col-lg-7 text-right">
                                                    <strong>
                                                        <?php
                                                        $numItems = count(explode(";", $offer['competitor']));
                                                        $i = 0;
                                                        foreach (explode(";", $offer['competitor']) as $compe) {
                                                            echo getCompetitorName($compe);
                                                            if (++$i != $numItems)
                                                                echo "<br>";
                                                        }
                                                        ?>
                                                    </strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt20">
                                                <div class="col-lg-7 text-left">
                                                    <?= "GO <strong>" . $offer['GO'] . "</strong>% x GET <strong>" . $offer['GET'] . "</strong>%"; ?>
                                                </div>
                                                <div class="col-lg-3 text-right">
                                                    <strong><?= $offer['GxG']; ?></strong>
                                                </div>
                                                <div class="col-lg-2 text-left">
                                                    %
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-7 text-left">
                                                    Order Value Estimated
                                                </div>
                                                <div class="col-lg-3 text-right">
                                                    <strong><?= $offer['OVE']; ?></strong>
                                                </div>
                                                <div class="col-lg-2 text-left">
                                                    k&euro;
                                                </div>
                                            </div>
                                            <div class="row m0 mt20">
                                                <div class="col-lg-7 text-left">
                                                    Quote Value (QV)
                                                </div>
                                                <div class="col-lg-3 text-right">
                                                    <strong><?= $offer['OV']; ?></strong>
                                                </div>
                                                <div class="col-lg-2 text-left">
                                                    k&euro;
                                                </div>
                                            </div>
                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                <div class="row m0 mt5">
                                                    <div class="col-lg-7 text-left">
                                                        QV CM
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['CMp']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">
                                                        %
                                                    </div>
                                                </div>
                                                <div class="row m0 mt20">
                                                    <div class="col-lg-7 text-left" title="Production Hours Project">
                                                        PHP
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['WHP']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">

                                                    </div>
                                                </div>
                                                <div class="row m0 mt5">
                                                    <div class="col-lg-7 text-left" title="Production Hours Service">
                                                        PHS
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['WHS']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">

                                                    </div>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="row content-section mt0 ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-bottom" data-toggle="modal" data-target="#offerModal">Edit</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="table-responsive">
                                                <table id="suboffers" class="table table-hover display" style="margin:auto;width:95%;">
                                                    <thead>
                                                        <tr>
                                                            <th></th>
                                                            <th></th>
                                                            <th></th>
                                                            <th title="Quote value">k&euro;</th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 text-center col-section">
                                    <div class="row header-section">
                                        <div class="col-lg-12">
                                            <div class="textSmall" title="">&nbsp;</div>
                                            <div class="textSmall" title="Next contact date"><strong><?php
                                                                                                        if ($offer['nextContactDate'] != '0000-00-00')
                                                                                                            echo dateFormat($offer['nextContactDate']);
                                                                                                        else
                                                                                                            echo "&nbsp;";
                                                                                                        ?></strong></div>
                                        </div>
                                    </div>
                                    <div class="row step-section justify-content-center">
                                        <div class="bar-left step3"></div>
                                        <div class="bar-right step4"></div>
                                        <div class="step-circle step3 pointer" title="Follow up" data-toggle="modal" data-target="#followUpModal"><i class="fa fa-phone-volume step-ico" aria-hidden="true"></i></div>
                                    </div>
                                    <div class="row people-section justify-content-center">
                                        <strong title="Follow up"><?= getNameAndSurname($offer['F']) . "&nbsp;"; ?></strong>
                                    </div>
                                    <div class="row content-section ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-top" data-toggle="modal" data-target="#addContactModal">Add</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="row m0 mt30">
                                                <div class="col-lg-7 text-left"><?php if (isset($nextCnt['contact'])) echo getNameAndSurname($nextCnt['contact']); ?></div>
                                                <div class="col-lg-5 text-left"><strong><?php if (isset($nextCnt['date'])) echo $nextCnt['date']; ?></strong></div>
                                            </div>
                                            <div class="row m0 mt5 plr10 text-left">
                                                <?php if (!empty($nextCnt['contactWith'])) { ?>
                                                    Contact with
                                                <?php
                                                    if ($nextCnt['contactWith'] != 0)
                                                        echo getContactNameAndSurname($nextCnt['contactWith']);
                                                    else
                                                        echo "Other";
                                                }
                                                ?>

                                            </div>
                                            <div class="row m0 mt5 plr10 text-left">
                                                <?php if (isset($nextCnt['note'])) echo str_replace("\n", "<br>", $nextCnt['note']); ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row content-section mt0 ml-mr-5 position-relative">
                                        <a href='offerContacts.php?id=<?= $offer['id']; ?>'><button class="btn btn-success position-absolute btn-edit-bottom">Open</button></a>
                                        <div class="col-lg-12">
                                            <div class="table-responsive">
                                                <table id="contacts" class="table table-hover display" style="margin:auto;width:95%;">
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 text-center col-section">
                                    <div class="row header-section">
                                        <div class="col-lg-12">
                                            <div class="textSmall" title="Requested order date"><?php
                                                                                                if ($offer['requestedOrderDate'] != '0000-00-00')
                                                                                                    echo dateFormat($offer['requestedOrderDate']);
                                                                                                else
                                                                                                    echo "&nbsp;";
                                                                                                ?></div>
                                            <div class="textSmall" title="Order date"><strong><?php
                                                                                                if ($offer['order'] != '0000-00-00')
                                                                                                    echo dateFormat($offer['order']);
                                                                                                else
                                                                                                    echo "&nbsp;";
                                                                                                ?></strong></div>
                                        </div>
                                    </div>
                                    <div class="row step-section justify-content-center">
                                        <div class="bar-left step4"></div>
                                        <div class="bar-right step5"></div>
                                        <div class="step-circle step4 pointer" title="Conclusion" data-toggle="modal" data-target="#conclusionModal"><i class="fa fa-handshake step-ico" aria-hidden="true"></i></div>
                                    </div>
                                    <div class="row people-section justify-content-center">
                                        <strong title="Project manager"><?= getNameAndSurname($offer['pm']) . "&nbsp;"; ?></strong>
                                    </div>
                                    <div class="row content-section ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-top" data-toggle="modal" data-target="#editOrderInfo">Edit</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="row m0 mt30">
                                                <div class="col-lg-6 text-left" title="Sales order number">
                                                    SON
                                                </div>
                                                <div class="col-lg-6 text-right">
                                                    <strong><?= $offer['orderNo']; ?></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-6 text-left" title="Production order number">
                                                    PON
                                                </div>
                                                <div class="col-lg-6 text-right">
                                                    <strong><?= $offer['productionOrderNo']; ?></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-7 text-left">
                                                    Client order no
                                                </div>
                                                <div class="col-lg-5 text-right">
                                                    <strong><?= $offer['clientOrderNo']; ?></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt50">
                                                <div class="col-lg-7 text-left">
                                                    Order value (OV)
                                                </div>
                                                <div class="col-lg-3 text-right">
                                                    <strong><?= $offer['orderValue']; ?></strong>
                                                </div>
                                                <div class="col-lg-2 text-left">
                                                    k&euro;
                                                </div>
                                            </div>
                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                <div class="row m0 mt5">
                                                    <div class="col-lg-7 text-left">
                                                        OV CM
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['ORVCM']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">
                                                        %
                                                    </div>
                                                </div>
                                                <div class="row m0 mt20">
                                                    <div class="col-lg-7 text-left">
                                                        Production value
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['productionValue']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">
                                                        k&euro;
                                                    </div>
                                                </div>
                                                <div class="row m0 mt5">
                                                    <div class="col-lg-7 text-left">
                                                        Prod. val. CM
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong><?= $offer['prodValCM']; ?></strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">
                                                        %
                                                    </div>
                                                </div>
                                                <div class="row m0 mt20">
                                                    <div class="col-lg-7 text-left">
                                                        Group CM
                                                    </div>
                                                    <div class="col-lg-3 text-right">
                                                        <strong>
                                                            <?php
                                                            if ($offer['orderValue'] != 0) {
                                                                $orvcme = $offer['orderValue'] * $offer['ORVCM'] / 100;
                                                                $prodcme = $offer['productionValue'] * $offer['prodValCM'] / 100;
                                                                $cme = $orvcme + $prodcme;
                                                                $groupCM = $cme * 100 / $offer['orderValue'];
                                                                echo round($groupCM);
                                                            } else
                                                                echo 0;
                                                            ?>
                                                        </strong>
                                                    </div>
                                                    <div class="col-lg-2 text-left">
                                                        %
                                                    </div>
                                                </div>
                                            <?php } ?>
                                        </div>
                                    </div>
                                    <div class="row content-section mt0 ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <a href="offerOrder.php?id=<?= $offer['id']; ?>"><button class="btn btn-success position-absolute btn-edit-bottom">Edit</button></a><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="table-responsive">
                                                <table id="suboffersOrdered" class="table table-hover display" style="margin:auto;width:95%;">
                                                    <thead>
                                                        <tr>
                                                            <th></th>
                                                            <th></th>
                                                            <th></th>
                                                            <th title="Order value">k&euro;</th>
                                                        </tr>
                                                    </thead>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-2 text-center col-section">
                                    <div class="row header-section">
                                        <div class="col-lg-6">
                                            <div class="textSmall" style="text-align:left;" title="Requested delivery date"><?php
                                                                                                                            if ($offer['requestedDeliveryDate'] != '0000-00-00')
                                                                                                                                echo dateFormat($offer['requestedDeliveryDate']);
                                                                                                                            else
                                                                                                                                echo "&nbsp;";
                                                                                                                            ?></div>
                                            <div class="textSmall" style="text-align:left;" title="Delivery date"><strong><?php
                                                                                                                            if ($offer['deliveryDate'] != '0000-00-00')
                                                                                                                                echo dateFormat($offer['deliveryDate']);
                                                                                                                            else
                                                                                                                                echo "&nbsp;";
                                                                                                                            ?></strong></div>
                                        </div>
                                        <div class="col-lg-6">
                                            <div class="textSmall" style="text-align:right;" title="Service start"><?php
                                                                                                                    if ($offer['serviceStart'] != '0000-00-00')
                                                                                                                        echo dateFormat($offer['serviceStart']);
                                                                                                                    else
                                                                                                                        echo "&nbsp;";
                                                                                                                    ?></div>
                                            <div class="textSmall" style="text-align:right;" title="Service end"><strong><?php
                                                                                                                            if ($offer['serviceEnd'] != '0000-00-00')
                                                                                                                                echo dateFormat($offer['serviceEnd']);
                                                                                                                            else
                                                                                                                                echo "&nbsp;";
                                                                                                                            ?></strong></div>
                                        </div>
                                    </div>
                                    <div class="row step-section">
                                        <div class="bar-left step6" style="left:10px;"></div>
                                        <div class="bar-right step6" style="right:10px;"></div>
                                        <div class="step-circle step5" style="left:10px;" data-toggle="modal" data-target="#deliveryModal" title="Delivery"><i class="fa fa-truck-moving step-ico pointer" aria-hidden="true"></i></div>
                                        <div class="step-circle step6" style="right:10px;" data-toggle="modal" data-target="#serviceModal" title="Service"><i class="fas fa-tools step-ico pointer" aria-hidden="true"></i></div>
                                    </div>
                                    <div class="row people-section justify-content-center">
                                        &nbsp;
                                    </div>
                                    <div class="row content-section ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-top" data-toggle="modal" data-target="#editIncoterms">Edit</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="row m0 mt30">
                                                <div class="col-lg-8 text-left">
                                                    INCOTERMS
                                                </div>
                                                <div class="col-lg-4 text-right">
                                                    <strong><?= $offer['incoterms']; ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row content-section mt0 ml-mr-5 position-relative">
                                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['editButtons'] == 1) { ?> <button class="btn btn-success position-absolute btn-edit-bottom" data-toggle="modal" data-target="#editBottomRight">Edit</button><?php } ?>
                                        <div class="col-lg-12">
                                            <div class="row m0 mt30">
                                                <div class="col-lg-8 text-left">
                                                    Payment cond.
                                                </div>
                                                <div class="col-lg-4 text-right">
                                                    <strong></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-8 text-left">
                                                    Penalties
                                                </div>
                                                <div class="col-lg-4 text-right">
                                                    <strong></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-8 text-left">
                                                    Liablities
                                                </div>
                                                <div class="col-lg-4 text-right">
                                                    <strong></strong>
                                                </div>
                                            </div>
                                            <div class="row m0 mt5">
                                                <div class="col-lg-8 text-left">
                                                    Place of jur.
                                                </div>
                                                <div class="col-lg-4 text-right">
                                                    <strong><?= $offer['plantLocationCity']; ?></strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-1 text-center"></div>
                            </div>
                            <br>

                            <!-- // SlideOut -->
                            <div id="slideOut">
                                <!--   // Tab -->
                                <div class="slideOutTab pointer">
                                    <div>
                                        <p>Comments</p>
                                    </div>
                                </div>
                                <div class="modal-content-slide">
                                    <div class="comments-list-box" style="height:320px;overflow-y:scroll;overflow-x:hidden;">

                                    </div>
                                    <form method="POST">
                                        <div class="row mt20">
                                            <div id="commentsBox" style="width:96%;">

                                            </div>
                                        </div>
                                    </form>
                                    <div class="modal-footer-slide"> </div>
                                </div>
                            </div>
                            <!--<div class="row">
                                    <div id="accordion" style="margin:0px 15px;width:100%;">
                                            <div class="card">
                                                    <div class="card-header">
                                                            <a class="card-link" data-toggle="collapse" id="collapseOneTrigger" href="#collapseOne">
                                                                    Offer files
                                                            </a>
                                                    </div>
                                                    <div id="collapseOne" class="collapse" data-parent="#accordion">
                                                            <div class="card-body">
                                                                    <div class="row"><button class="btn btn-primary" data-toggle="modal" data-target="#addOfferFile">Add offer file</button></div>
<?php
$i = 1;
$files = glob(ROOT_OFFERS_DIR . "offer-" . $offer['offerNo'] . "*");
foreach ($files as $file) {
    $fileName = explode("/", $file);
    echo "<div class='row'>" . $i . ")&nbsp;<a href='" . $file . "' target='_blank'>" . end($fileName) . "</a><i class='fas fa-times' onclick='setDeleteValues(" . '"' . $file . '"' . "," . '"' . end($fileName) . '"' . ")' style='cursor:pointer;margin: 5px 0px 10px 10px;' data-toggle='modal' data-target='#deleteFile'></i></div>";
    $i++;
}
if ($i == 1)
    echo "<div class='row'>There are no files assigned to this offer.</div>";
?>
                                                            </div>
                                                    </div>
                                            </div>
                                    </div>
                            </div>-->

                            <div class="modal fade" id="editCommentModal">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Edit comment</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label>Comment:<br>
                                                        <textarea class="form-control form-100" name="editComment" required></textarea>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-primary save-edit-comment">Save</button>
                                                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="delCommentModal">
                                <div class="modal-dialog ui-draggable" style="margin-top:10px;">
                                    <div class="modal-content">
                                        <div class="modal-header ui-draggable-handle">
                                            <h4 class="modal-title">Do you want to delete this comment?</h4>
                                            <button type="button" class="close" data-dismiss="modal">×</button>
                                        </div>
                                        <div class="modal-footer">
                                            <input type="submit" data-dismiss="modal" value="Yes" class="btn btn-primary submit-del-comment form-100">
                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="conclusionModal">
                                    <div class="modal-dialog" style="min-width:50%;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Conclusion: <?= ($client['clientLongName'] == '' ? $endclient['clientLongName'] : $client['clientLongName']) . "/" . $offer['offerNo']; ?></h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3 text-center">
                                                            <label>Order date: *<br>
                                                                <input type="date" class="form-control form-100" name="OOOrderDate" value="<?= $offer['order']; ?>" <?php if (ifOneOrderStatus($offer['offerNo']) == true) echo 'required'; ?>>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <label for="name" style="width:200px;">Order company:<br>
                                                                <select class="form-control select2" name="OOOrderCompany">
                                                                    <option value="" selected>Select</option>
                                                                    <?= getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <label for="name" style="width:200px;">Project manager (PM):<br>
                                                                <select class="form-control select2" name="OOPM" id="OOPM">
                                                                    <?= listPMs($offer['pm']); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <label>Client order no:<br>
                                                                <input type="text" class="form-control form-100" name="OOClientOrderNo" value="<?= $offer['clientOrderNo']; ?>">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required only if one of components id order.</div>
                                                    <div class="row justify-content-center">
                                                        <div class="table-responsive">
                                                            <table id="conclusion" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Position</th>
                                                                        <th>Sales article</th>
                                                                        <th>SON</th>
                                                                        <th>Status</th>
                                                                        <th>Reason</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php conclusion($offer['offerNo']); ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <div class="row justify-content-center mx-0">
                                                        <button type="button" class="btn btn-primary form-100 m-1" data-dismiss="modal" data-toggle="modal" data-target="#lostOffer">Lost offer</button>
                                                        <a class="m-1" href="offerOrder.php?id=<?= $offer['id']; ?>"><button type="button" class="btn btn-primary form-100">Order info</button></a>
                                                        <!-- <a class="m-1" href="orderIntake.php?id=<?= $offer['id']; ?>"><button type="button" class="btn btn-primary form-100">Order intake</button></a> -->
                                                    </div>
                                                    <div class="row justify-content-center mx-0">
                                                        <input type="submit" onclick="redBorder()" name="saveOO" value="Save" class="btn btn-primary form-100 m-1">
                                                        <button type="button" class="btn btn-danger form-100 m-1" data-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deliveryModal">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Delivery: <?= ($client['clientLongName'] == '' ? $endclient['clientLongName'] : $client['clientLongName']) . "/" . $offer['offerNo']; ?></h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label>Delivery date: *<br>
                                                            <input type="date" class="form-control form-100" name="delDeliveryDate" value="<?= $offer['deliveryDate']; ?>" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label>Place of jur:<br>
                                                            <input type="text" class="form-control form-100" name="delPlantLocation" value="<?= $offer['plantLocationCity']; ?>">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Incoterms:<br>
                                                            <select name="delIncoterms" class="form-control select2">
                                                                <option selected value="">Select</option>
                                                                <option value="EXW">EXW</option>
                                                                <option value="DAP">DAP</option>
                                                                <option value="DDP">DDP</option>
                                                                <option value="CIP">CIP</option>
                                                                <option value="DAT">DAT</option>
                                                                <option value="FCA">FCA</option>
                                                                <option value="CPT">CPT</option>
                                                                <option value="FAS">FAS</option>
                                                                <option value="FOB">FOB</option>
                                                                <option value="CFR">CFR</option>
                                                                <option value="CIF">CIF</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="saveDel" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>


                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="serviceModal">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Service:</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label>Service start: *<br>
                                                            <input type="date" class="form-control form-100" name="serviceStart" value="<?= $offer['serviceStart']; ?>" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label>Service end: *<br>
                                                            <input type="date" class="form-control form-100" name="serviceEnd" value="<?= $offer['serviceEnd']; ?>" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="saveService" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="followUpModal">
                                <div class="modal-dialog" style="min-width:50%;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Follow up: <?= ($client['clientLongName'] == '' ? $endclient['clientLongName'] : $client['clientLongName']) . "/" . $offer['offerNo']; ?></h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">GO: *<br>
                                                                <input type="number" min="0" max="100" class="form-control" name="foGO" value="<?= $offer['GO']; ?>" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">GET: *<br>
                                                                <input type="number" min="0" max="100" class="form-control" name="foGET" value="<?= $offer['GET']; ?>" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label for="name" style="width:200px;">Requested order date: *<br>
                                                                <input type="date" class="form-control form-100" name="foOrderDate" id="foOrderDate" value="<?php
                                                                                                                                                            if ($offer['requestedOrderDate'] != '0000-00-00')
                                                                                                                                                                echo $offer['requestedOrderDate'];
                                                                                                                                                            else
                                                                                                                                                                echo date("Y-m-d");
                                                                                                                                                            ?>" required>
                                                            </label>
                                                        </div>
                                                        <div class="row justify-content-center" style="color:red;" id="datePast">
                                                            <?php if ($offer['requestedOrderDate'] < date('Y-m-d') && $offer['step'] != 4) echo "WARNING! Date is in the past!"; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Quote type (QT): *<br>
                                                                <select class="form-control form-100" name="foOT" required>
                                                                    <option value="" disabled selected>Select</option>
                                                                    <option value="A">A</option>
                                                                    <option value="B">B</option>
                                                                    <option value="E">E</option>
                                                                    <option value="Q">Q</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">OVE: *<br>
                                                                <input type="number" min="0" step="0.01" class="form-control" name="foOVE" value="<?= $offer['OVE']; ?>" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Requested delivery date: *<br>
                                                                <input type="date" class="form-control" name="foRequestedDeliveryDate" value="<?= $offer['requestedDeliveryDate']; ?>">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Production reservation:<br>
                                                                <select class="form-control form-100" name="foProdReserv" id="foProdReserv">
                                                                    <option value="0">No</option>
                                                                    <option value="1">Yes</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                    </div>
                                                    <div class="col-lg-4">
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                                <hr>
                                                <div class="row justify-content-center">
                                                    <h4>Contact</h4>
                                                </div>
                                                <hr>
                                                <div class="row justify-content-center">
                                                    <label><input type="checkbox" name="includeContact" checked value="1" id="includeContact"> Include contact with client.</label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <div class="col-lg-6 text-center borderRight">
                                                        <div class="row justify-content-center">
                                                            <label for="name">Contact With: **<br>
                                                                <select name="foContactWith" id="foContactWith" class="form-control select2 incCont" required>
                                                                    <option value='' selected disabled>Select contact</option>
                                                                    <?php listContactsPerson($offer['client']); ?>
                                                                    <?php listContactsPerson($offer['endClient']); ?>

                                                                    <option value='0' selected>Other</option>
                                                                </select><span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary incCont" data-dismiss="modal" data-toggle="modal" data-target="#addCon"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                            </label>
                                                        </div>
                                                        <div class="row justify-content-center">
                                                            <label for="name" style="width:200px;">Contact date: *<br>
                                                                <input type="date" class="form-control form-100 incCont" name="foCurrent" id="foCurrent" value="<?= date("Y-m-d"); ?>" required>
                                                            </label>
                                                        </div>
                                                        <div class="row justify-content-center">
                                                            <label for="name" style="width:200px;">Next contact date: **<br>
                                                                <input type="date" class="form-control form-100 incCont" name="foNext" id="foNext" value="<?= $offer['nextContactDate']; ?>" required>
                                                            </label>
                                                        </div>
                                                        <div class="row justify-content-center">
                                                            <button type="button" class="btn addMonths" amount="1">+1m</button>&nbsp;
                                                            <button type="button" class="btn addMonths" amount="3">+3m</button>&nbsp;
                                                            <button type="button" class="btn addMonths" amount="6">+6m</button>&nbsp;
                                                            <button type="button" class="btn addMonths" amount="12">+12m</button>
                                                        </div>
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Follow up: *<br>
                                                                <select name="fFollowUp" class="form-control select2 incCont" required>
                                                                    <?php listFollowUpUsers(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-6 text-center">
                                                        <div class="row justify-content-center">
                                                            <label style="width:80%;">Comment: **<br>
                                                                <textarea class="form-control form-100 incCont" style="height:270px;width:100%" name="foLastCommenct" id="foLastCommenct" required></textarea>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div><br>
                                                <div class="row justify-content-center">** - Fields required when "Include contact with client" is checked.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" onclick="redBorder()" name="saveFollow" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addContactModal">
                                    <div class="modal-dialog" style="min-width:50%;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Add contact</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 text-center borderRight">
                                                            <div class="row justify-content-center">
                                                                <label for="name">Contact With: *<br>
                                                                    <select name="acContactWith" id="acContactWith" class="form-control select2" required>
                                                                        <?php listContactsPerson($offer['client']); ?>
                                                                        <?php listContactsPerson($offer['endClient']); ?>
                                                                        <option value='0' selected>Other</option>
                                                                    </select><span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary incCont" data-dismiss="modal" data-toggle="modal" data-target="#addCon"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label for="name" style="width:200px;">Next contact date: *<br>
                                                                    <input type="date" class="form-control form-100" name="acNext" id="acNext" value="<?= $offer['nextContactDate']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label for="name" style="width:200px;">Contact date: *<br>
                                                                    <input type="date" class="form-control form-100" name="acCurrent" id="acCurrent" value="<?= date("Y-m-d"); ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <button type="button" class="btn addMonths" amount="1">+1m</button>&nbsp;
                                                                <button type="button" class="btn addMonths" amount="3">+3m</button>&nbsp;
                                                                <button type="button" class="btn addMonths" amount="6">+6m</button>&nbsp;
                                                                <button type="button" class="btn addMonths" amount="12">+12m</button>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label style="width:200px;">Follow up: *<br>
                                                                    <select name="fnFollowUp" class="form-control select2 incCont" required>
                                                                        <?php listFollowUpUsers(); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6 text-center">
                                                            <div class="row justify-content-center">
                                                                <label style="width:80%;">Comment: *<br>
                                                                    <textarea class="form-control form-100" style="height:270px;width:100%" name="acLastCommenct" id="acLastCommenct" required></textarea>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="acSave" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="offerModal">
                                    <div class="modal-dialog" style="min-width:60%;margin-top:50px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Offer: <?= ($client['clientLongName'] == '' ? $endclient['clientLongName'] : $client['clientLongName']) . "/" . $offer['offerNo']; ?></h4>
                                                <button type="button" class="close" data-dismiss="modal" onclick="location.href = 'offer.php?id=<?= $offer['id']; ?>';">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-lg-3 text-center">
                                                            <div class="row justify-content-center">
                                                                <label for="name" style="width:200px;">Quote date: *<br>
                                                                    <input type="date" name="offOfferDate" id="offOfferDate" class="form-control form-100" value="<?php
                                                                                                                                                                    if (isset($_SESSION['offerFormData']['offer']))
                                                                                                                                                                        echo $_SESSION['offerFormData']['offer'];
                                                                                                                                                                    else if ($offer['offer'] != '0000-00-00')
                                                                                                                                                                        echo $offer['offer'];
                                                                                                                                                                    else
                                                                                                                                                                        echo date('Y-m-d');
                                                                                                                                                                    ?>" required oninput="offerFormData($('#offOfferDate').val(),$('#offGO').val(),$('#offGET').val(),$('#offCompetitorsSelect').val(),$('#offOve').val(),$('#offProdRes').val())">
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label for="name">Order Value Est. (OVE) k&euro;: *<br>
                                                                    <input onfocusout="checkKiloEuro(this);" class="form-control form-100" step="0.1" type="number" name="offOve" id="offOve" placeholder="OVE" required value="<?php
                                                                                                                                                                                                                                if (isset($_SESSION['offerFormData']['OVE']))
                                                                                                                                                                                                                                    echo $_SESSION['offerFormData']['OVE'];
                                                                                                                                                                                                                                else
                                                                                                                                                                                                                                    echo $offer['OVE'];
                                                                                                                                                                                                                                ?>" oninput="offerFormData($('#offOfferDate').val(),$('#offGO').val(),$('#offGET').val(),$('#offCompetitorsSelect').val(),$('#offOve').val(),$('#offProdRes').val())">
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <div class="row justify-content-center">
                                                                <label for="name" style="width:200px;">GO:<br>
                                                                    <input type="number" min="0" max="100" name="offGO" id="offGO" placeholder="GO" value="<?php
                                                                                                                                                            if (isset($_SESSION['offerFormData']['GO']))
                                                                                                                                                                echo $_SESSION['offerFormData']['GO'];
                                                                                                                                                            else
                                                                                                                                                                echo $offer['GO'];
                                                                                                                                                            ?>" class="form-control form-100" oninput="offerFormData($('#offOfferDate').val(),$('#offGO').val(),$('#offGET').val(),$('#offCompetitorsSelect').val(),$('#offOve').val(),$('#offProdRes').val())">
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label for="name">Production reservation:<br>
                                                                    <select class="select2 form-control" name="offProdRes" id="offProdRes" <?php if (($offer['WHP'] == 0 && $offer['WHS'] == 0) || $offer['productionLocation'] == "") { ?>onchange="ProductionReser($('#offProdRes'), $('#prodReserText'), $('#offSaveBtn'));offerFormData($('#offOfferDate').val(), $('#offGO').val(), $('#offGET').val(), $('#offCompetitorsSelect').val(), $('#offOve').val(), $('#offProdRes').val())" <?php } else { ?> onchange="offerFormData($('#offOfferDate').val(), $('#offGO').val(), $('#offGET').val(), $('#offCompetitorsSelect').val(), $('#offOve').val(), $('#offProdRes').val())" <?php } ?>>
                                                                        <option value="0">No</option>
                                                                        <option value="1">Yes</option>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center text-danger hidden" id="prodReserText">
                                                                You need to fill production hours and production location first. To do so go to technical part of component.
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <label for="name" style="width:200px;">GET:<br>
                                                                <input type="number" min="0" max="100" name="offGET" id="offGET" placeholder="GET" value="<?php
                                                                                                                                                            if (isset($_SESSION['offerFormData']['GET']))
                                                                                                                                                                echo $_SESSION['offerFormData']['GET'];
                                                                                                                                                            else
                                                                                                                                                                echo $offer['GET'];
                                                                                                                                                            ?>" class="form-control form-100" oninput="offerFormData($('#offOfferDate').val(),$('#offGO').val(),$('#offGET').val(),$('#offCompetitorsSelect').val(),$('#offOve').val(),$('#offProdRes').val())">
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3 text-center">
                                                            <label for="name" style="width:200px;">Competitor:<br>
                                                                <select class="form-control select2" name="offCompetitor[]" id="offCompetitorsSelect" multiple onchange="offerFormData($('#offOfferDate').val(), $('#offGO').val(), $('#offGET').val(), $('#offCompetitorsSelect').val(), $('#offOve').val(), $('#offProdRes').val())">
                                                                    <?php listCompetitors(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                    <div class="row position-relative">
                                                        <button id="addComponent" type="button" class="btn btn-primary" title="Create sales article" offerNo="<?= $offer['offerNo']; ?>"><i style="font-size:16px;padding-top:3px;" class="fas fa-plus"></i></button>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="table-responsive">
                                                            <table id="components" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Count</th>
                                                                        <th>Position</th>
                                                                        <th>Sales article</th>
                                                                        <th>Segment</th>
                                                                        <th>Calc. person</th>
                                                                        <th>Prod. loc.</th>
                                                                        <th>Technical</th>
                                                                        <th>Commercial</th>
                                                                        <!--<th>Edit</th>-->
                                                                        <th>Delete</th>
                                                                        <th>Revision</th>
                                                                        <th>Copy</th>
                                                                    </tr>
                                                                </thead>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" name="saveOffer" class="btn btn-primary form-100" value="Save" id="offSaveBtn">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="inquiryModal">
                                    <div class="modal-dialog" style="margin-top:10px;min-width:80%;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit inquiry data.</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-lg-3">
                                                            <h4>General</h4>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col">
                                                            <label>Quote type (QT): *<br><select class="form-control form-100" name="inqOT" required>
                                                                    <option value="" disabled selected>Select</option>
                                                                    <option value="A">A</option>
                                                                    <option value="B">B</option>
                                                                    <option value="E">E</option>
                                                                    <option value="Q">Q</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label style="width:200px;">Project:<br>
                                                                <select id='inqProject' name="inqProject" class="form-control select2">
                                                                    <?php selectProjects(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label for="name">Description: *<br>
                                                                <input class="form-control form-100" type="text" name="inqScope" placeholder="Description (scope)" required value="<?= $offer['scope']; ?>">
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label style="width:200px;">Sales company: *<br>
                                                                <select id='inqCompany' name="inqCompany" class="form-control select2" required>
                                                                    <option value="" selected>Sales company</option>
                                                                    <?= getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label for="name">Internal number:<br>
                                                                <input class="form-control form-100" type="text" name="internalNumber" placeholder="Internal number" value="<?= $offer['oldOfferNo']; ?>"></label>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 borderRight">
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <h4>Reseller</h4>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Inquiry no:<br>
                                                                        <input type="text" name="inqInquiryNo" placeholder="Inquiry number" class="form-control form-100 client" value="<?= $offer['inquiryNo']; ?>">
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Company:<br>
                                                                        <select class="form-control select2-reseller client" id="inqClient" name="inqClient" required onchange="getCities($(this).val(), $('#inqClientLocation'));$('#addConBtn').prop('disabled', false);$('.clientEnd').prop('required', false)">
                                                                            <?php selectThisReseller($offer['client']); ?>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Purchaser:<br>
                                                                        <select class="form-control select2 client" id="inqContactPurchase" name="inqContactPurchase" disabled required onchange="$('#inqContactTechnican').prop('required', false);"></select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" id="addConBtn" data-dismiss="modal" onClick="$('#ifend').val('0');" data-toggle="modal" data-target="#addContact" disabled><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Location:<br>
                                                                        <select class="form-control select2 client" id="inqClientLocation" name="inqClientLocation" disabled required onchange="getContacts($(this).val(), $('#inqContactPurchase'), $('#inqContactTechnican'), 0);">
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Technican:<br>
                                                                        <select class="form-control select2 client" id="inqContactTechnican" name="inqContactTechnican" disabled required onchange="$('#inqContactPurchase').prop('required', false);"></select>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6">
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <h4>End client</h4>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Inquiry no:<br>
                                                                        <input type="text" name="inqInqNoEnd" placeholder="Inquiry number" class="form-control form-100 clientEnd" value="<?= $offer['endclientInquiryNo']; ?>">
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Company:<br>
                                                                        <select class="form-control select2-endclient clientEnd" id="inqClientEnd" name="inqClientEnd" required onchange="getCities($(this).val(), $('#inqEndClientLocation'));$('.client').prop('required', false)">
                                                                            <?php selectThisEndclient($offer['endClient']); ?>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Purchaser:<br>
                                                                        <select class="form-control select2 clientEnd" id="inqContactPurchaseEnd" name="inqContactPurchaseEnd" onchange="$('#inqContactTechnicanEnd').prop('required', false);" disabled required></select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" id="addEndConBtn" data-dismiss="modal" onClick="$('#ifend').val('1');" data-toggle="modal" data-target="#addContact" disabled><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Location:<br>
                                                                        <select class="form-control select2 clientEnd" id="inqEndClientLocation" name="inqEndClientLocation" disabled required onchange="getContacts($(this).val(), $('#inqContactPurchaseEnd'), $('#inqContactTechnicanEnd'), 1);"></select>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Technican:<br>
                                                                        <select class="form-control select2 clientEnd" id="inqContactTechnicanEnd" name="inqContactTechnicanEnd" onchange="$('#inqContactPurchaseEnd').prop('required', false);" disabled required></select>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="row">
                                                        <div class="col-lg-3">
                                                            <h4>Dates and Responsibilities</h4>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Inquiry date: *<br>
                                                                <input class="form-control form-100" type="date" name="inqInquiry" value="<?= $offer['inquiry']; ?>" required value="<?= $offer['inquiry']; ?>"></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Requested quote date: *<br>
                                                                <input class="form-control form-100" type="date" name="inqRequest" value="<?= $offer['request']; ?>" required value="<?= $offer['request']; ?>"></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Next follow-up date: *<br>
                                                                <input class="form-control form-100" type="date" name="inqFollowUpDate" required value="<?= $offer['nextContactDate']; ?>"></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Requested order date: *<br>
                                                                <input class="form-control form-100" type="date" name="inqRequestedOrderDate" required value="<?= $offer['requestedOrderDate']; ?>"></label>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Responsibility (R): *<br>
                                                                <select name="inqR" class="form-control select2" required>
                                                                    <?php selectVs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Inside Sales (IS): *<br>
                                                                <select name="inqIS" class="form-control select2" required>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Follow up: *<br>
                                                                <select name="inqFollowUp" class="form-control select2" required>
                                                                    <?php listFollowUpUsersAdd(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label for="name">Order Value Est. (OVE) k&euro;: *<br>
                                                                <input onfocusout="checkKiloEuro(this);" class="form-control form-100" step="0.1" type="number" name="inqOve" placeholder="OVE" required value="<?= $offer['OVE']; ?>">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" value="Save" name="saveInqData" onclick="redBorder();" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="lostOffer">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Lost offer.</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Status:<br>
                                                            <select class="form-control select2" name="lostAXNEW" id="lostAX">
                                                                <option value="" selected disabled>Select</option>
                                                                <option value="Terminated">Terminated</option>
                                                                <option value="Lost">Lost</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Reason:<br>
                                                            <select class="form-control select2" name="lostReasonNEW" id="lostReason" disabled>
                                                                <option value="" selected disabled>Select</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" name="lostSave" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addScope">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit component info.</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Scope:<br>
                                                        <input type="text" name="cmpScope" id="cmpScope" class="form-control form-100">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Segment:<br>
                                                        <select name="cmpSegment" id="cmpSegment" class="form-control select2" required>
                                                            <option value="V-Vessles/Apparatus ({mniej}{srednica}4000mm)">V-Vessles/Apparatus (<&#8960;4000mm)< /option>
                                                            <option value="V-Vessles/Apparatus ({wiecej}{srednica}4000mm)">V-Vessles/Apparatus (>&#8960;4000mm)</option>
                                                            <option value="V-Vessles/Apparatus (Pure TP)">V-Vessles/Apparatus (Pure TP)</option>
                                                            <option value="V-Vessles/Apparatus (FF)">V-Vessles/Apparatus (FF)</option>
                                                            <option value="P-Process Piping ({srednica}25-600mm)">P-Process Piping (&#8960;25-600mm)</option>
                                                            <option value="P-Pipelines ({srednica}600mm-1500mm)">P-Pipelines (&#8960;600mm-1500mm)</option>
                                                            <option value="L-Loose lining">L-Loose lining</option>
                                                            <option value="L-Sheet lining">L-Sheet lining</option>
                                                            <option value="L-Fixpoint lining">L-Fixpoint lining</option>
                                                            <option value="Sp-Wet ESP">Sp-Wet ESP</option>
                                                            <option value="Sp-Ducts">Sp-Ducts</option>
                                                            <option value="Sp-Chimney & Stacks">Sp-Chimney & Stacks</option>
                                                            <option value="Sp-Other">Sp-Other</option>
                                                            <option value="Si-Installation">Si-Installation</option>
                                                            <option value="Si-Daily service (Inspection, repairs)">Si-Daily service (Inspection, repairs)</option>
                                                            <option value="Si-Revamping">Si-Revamping</option>
                                                            <option value="Si-Shutdowns & Turnarounds">Si-Shutdowns & Turnarounds</option>
                                                            <option value="OM-Spare parts">OM-Spare parts</option>
                                                            <option value="OM-Trade components">OM-Trade components</option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Calculating person:<br>
                                                        <select class="form-control select2" id="calcPersonSelect">
                                                            <?php listUsers(); ?>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Production location:<br>
                                                        <select class="form-control select2" id="cmpProduction">
                                                            <option value='' selected>Select</option>
                                                            <?= getCompanies(); ?>
                                                        </select>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="number" name="cmpIdScope" id="cmpIdScope" class="hidden">
                                                <input type="submit" data-id="" id="saveScope" data-dismiss="modal" data-toggle="modal" data-target="#offerModal" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#offerModal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addComercial">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Add commercial.</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Quote value (QV):<br>
                                                        <input type="number" id="comercialOVvalue" step="0.01" min="0" class="form-control w100">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">CM%:<br>
                                                        <input type="number" id="comercialCM" min="0" max="100" class="form-control w100">
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="number" name="addCOmercialOVID" id="addCOmercialOVID" class="hidden">
                                                <input type="submit" data-id="" id="saveComercial" data-dismiss="modal" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteComponent">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Do you want to delete this component?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" data-id="" id="delThisComp" data-dismiss="modal" value="Yes" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editClientInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit reseller</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Company:<br>
                                                            <select class="form-control select2-reseller" name="client" onchange="getCities($(this).val(), $('#clientLocation'));">
                                                                <?php selectThisReseller($offer['client']); ?>
                                                            </select></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Location:<br>
                                                            <select class="form-control select2" id="clientLocation" name="clientLocation" disabled onchange="getContacts($(this).val(), $('#inqContactPurchase'), $('#contactTechnican'), 0);">
                                                            </select></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Contact purchase:<br>
                                                            <select class="form-control select2" id="contactPurchase" name="contactPurchase" disabled></select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Contact technican:<br>
                                                            <select class="form-control select2" id="contactTechnican" name="contactTechnican" disabled></select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Inquiry number:<br>
                                                            <input type="text" name="clientInquiryNo" class="form-control form-100" value="<?= $offer['inquiryNo']; ?>">
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" name="saveClientInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editEndClientInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit end client</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Company:<br>
                                                            <select class="form-control select2-endclient" name="endClient" onchange="getCities($(this).val(), $('#endClientLocation'));">
                                                                <?php selectThisEndclient($offer['endClient']); ?>
                                                            </select></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Location:<br>
                                                            <select class="form-control select2" id="endClientLocation" name="endClientLocation" disabled onchange="getContacts($(this).val(), $('#inqContactPurchaseEnd'), $('#contactTechnicanEnd'), 1);">
                                                            </select></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Contact purchase:<br>
                                                            <select class="form-control select2" id="contactPurchaseEnd" name="contactPurchaseEnd" disabled></select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Contact technican:<br>
                                                            <select class="form-control select2" id="contactTechnicanEnd" name="contactTechnicanEnd" disabled></select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Inquiry number:<br>
                                                            <input type="text" name="clientInquiryNoEnd" class="form-control form-100" value="<?= $offer['endclientInquiryNo']; ?>">
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" name="saveEndClientInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="changeStage">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit offer stage</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Select stage:<br>
                                                            <select class="form-control select2" name="stage" required>
                                                                <option value="1">Inquiry</option>
                                                                <option value="2">Offer</option>
                                                                <option value="3">Follow up</option>
                                                                <option value="4">Conclusion</option>
                                                                <option value="5">Delivery</option>
                                                            </select></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        If you change order back to any stage, Status and Reason field will be wiped.
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" name="saveStage" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteFile">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Are you sure to delete <span id="deleteFileSpan"></span> file?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="text" name="fileName" class="hidden" id="fileName">
                                                    <input type="submit" onclick="redBorder()" name="deleteFile" value="Yea, delete" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editProjectInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit offer</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Project name:<br>
                                                            <input type="text" class="form-control form-100" name="projectName" placeholder="Project name" value="<?= $offer['projectName']; ?>">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Inquiry number:<br>
                                                            <input type="text" name="inquiryNo" placeholder="Inquiry number" value="<?= $offer['inquiryNo']; ?>" class="form-control form-100">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Final client:<br>
                                                            <input type="text" name="finalClient" placeholder="Final client" value="<?= $offer['finalClient']; ?>" class="form-control form-100">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Plant location country:<br>
                                                            <select name="plantLocationCountry" class="form-control select2">
                                                                <?php listCountriesOffer(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Plant location city:<br>
                                                            <input type="text" class="form-control form-100" name="plantLocationCity" placeholder="Plant location city" value="<?= $offer['plantLocationCity']; ?>">
                                                        </label>
                                                    </div>
                                                    <!--<div class="row justify-content-center">
                                                            <label for="name" style="width:200px;">Scope:<br>
                                                                    <input type="text" name="scope" placeholder="Scope" value="<?= $offer['scope']; ?>" class="form-control form-100">
                                                            </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                            <label for="name" style="width:200px;">Segment:<br>
                                                                    <select class="form-control select2" id="segment" name="segment">
                                                                            <option value="" selected>Select segment</option>
                                                                            <option value="L-Fixpoint">L-Fixpoint</option>
                                                                            <option value="L-Sheet">L-Sheet</option> 
                                                                            <option value="Other">Other</option>
                                                                            <option value="P-{srednica}25-600mm">P-&#8960;25-600mm</option>
                                                                            <option value="P-{srednica}600mm-1500mm">P-&#8960;600mm-1500mm</option>
                                                                            <option value="Si-Inst.">Si-Inst.</option>
                                                                            <option value="Si-Revamping">Si-Revamping</option>
                                                                            <option value="Si-Service">Si-Service</option>
                                                                            <option value="Sp-Chimney">Sp-Chimney</option>
                                                                            <option value="Sp-Other">Sp-Other</option>
                                                                            <option value="Sp-Wet ESP">Sp-Wet ESP</option>
                                                                            <option value="V-{mniej}{srednica}4000mm">V-<&#8960;4000mm</option>
                                                                            <option value="V-{wiecej}{srednica}4000mm">V->&#8960;4000mm</option>
                                                                    </select>
                                                            </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                            <label for="name">BT:<br>
                                                            <select class="form-control form-100" name="BT">
                                                                    <option value="">Select BT</option>
                                                                    <option value="P">P</option>
                                                                    <option value="T">T</option>
                                                                    <option value="S">S</option>
                                                            </select></label>
                                                    </div>-->
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Production location:<br>
                                                            <select class="form-control select2" id="productionLocation" name="productionLocation">
                                                                <option value="" selected>Select</option>
                                                                <?= getCompanies(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" onclick="redBorder()" name="saveProjectInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editSalesInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit sales</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Order Value Estimated (OVE) k&euro;: *<br>
                                                            <input type="number" min="0" name="OVE" step="0.1" placeholder="Order value estimated" value="<?= $offer['OVE']; ?>" class="form-control form-100" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">GO: *<br>
                                                            <input type="number" min="0" max="100" name="GO" placeholder="GO" value="<?= $offer['GO']; ?>" class="form-control form-100" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">GET: *<br>
                                                            <input type="number" min="0" max="100" name="GET" placeholder="GET" value="<?= $offer['GET']; ?>" class="form-control form-100" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Competitor:<br>
                                                            <select class="form-control select2" name="competitor[]" id="competitorsSelect" multiple>
                                                                <?php listCompetitors(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                    <!--<div class="row justify-content-center">
                                                            <label for="name">Client order number:<br>
                                                                    <input type="text" name="salesClOrNo" placeholder="Client order no" value="<?= $offer['clientOrderNo']; ?>" class="form-control form-100">
                                                            </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                            <label style="width:200px;">Reason:<br><select class="form-control select2" name="reason"> 
    <?php reasonList(); ?>
                                                            </select>
                                                    </div>-->
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" onclick="redBorder()" name="saveSalesInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editStaffInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit staff and project</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Project:<br>
                                                            <select id='staffProject' name="staffProject" class="form-control select2">
                                                                <?php selectProjects(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Sales company: *<br>
                                                            <select class="form-control select2" name="staffSalesCmp" required>
                                                                <option value="" selected>Select</option>
                                                                <?= getCompanies(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Quote type (QT): *<br>
                                                            <select class="form-control select2" name="staffOfferType" required>
                                                                <option value="" selected>Select</option>
                                                                <option value="A">A</option>
                                                                <option value="B">B</option>
                                                                <option value="E">E</option>
                                                                <option value="Q">Q</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Project manager (PM):<br>
                                                            <select class="form-control select2" name="staffPM">
                                                                <?= listPMs($offer['pm']); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Production reservation:<br>
                                                            <select class="form-control select2" name="staffProductionReservation">
                                                                <option value="0">No</option>
                                                                <option value="1">Yes</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Description: *<br>
                                                            <input type="text" name="staffScope" value="<?= $offer['scope']; ?>" class="form-control" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" onclick="redBorder()" name="saveStaffInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editDatesInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit dates and staff</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Inquiry date: *<br>
                                                            <input type="date" value="<?= $offer['inquiry']; ?>" name="inquiry" class="form-control form-100" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Requested quote date: *<br>
                                                                <input type="date" value="<?= $offer['request']; ?>" name="request" class="form-control form-100" required>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Quote date:<br>
                                                                <input type="date" value="<?= $offer['offer']; ?>" name="offer" class="form-control form-100">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Next (or first) contact date: *<br>
                                                            <input type="date" value="<?= $offer['nextContactDate']; ?>" name="nextContactDateEditDates" class="form-control form-100" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Requested order date:<br>
                                                                <input type="date" value="<?= $offer['requestedOrderDate']; ?>" name="requestedOrderEditDates" class="form-control form-100" required>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Order date:<br>
                                                                <input type="date" value="<?= $offer['order']; ?>" name="order" class="form-control form-100">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Requested delivery date:<br>
                                                                <input type="date" value="<?= $offer['requestedDeliveryDate']; ?>" name="requestedDeliveryDate" class="form-control form-100">
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-6 text-center">
                                                            <label for="name" style="width:200px;">Delivery date:<br>
                                                                <input type="date" value="<?= $offer['deliveryDate']; ?>" name="delivery" class="form-control form-100">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Responsibility (R): *<br>
                                                            <select name="datesV" class="form-control select2" required>
                                                                <?= selectVs(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Inside Sales (IS): *<br>
                                                            <select name="datesID" class="form-control select2">
                                                                <?= selectIDs(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Follow up: *<br>
                                                            <select name="datesF" class="form-control select2" required>
                                                                <?= listFollowUpUsersAdd(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label>Project manager (PM):<br>
                                                            <select class='form-control form-100 select2' name='datesPM'>
                                                                <?= listPMs($offer['pm']); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?= $offer['id']; ?>">
                                                    <input type="submit" onclick="redBorder()" name="saveDatesInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editOrderInfo">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit order info</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label>Order number:<br><input class="form-control form-100" type="number" name="orderNo" value="<?php if (isset($offer)) echo $offer['orderNo']; ?>"></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label>Client order number:<br><input class="form-control form-100" type="text" name="clientOrderNo" value="<?php if (isset($offer)) echo $offer['clientOrderNo']; ?>"></label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input class="hidden" name="offerIdEdit" value="<?php if (isset($offer)) echo $offer['id']; ?>">
                                                    <input type="submit" onclick="redBorder()" name="saveOrderInfo" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editIncoterms">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit incoterms</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name" style="width:200px;">Incoterms:<br>
                                                            <select name="incoterms" class="form-control select2">
                                                                <option selected value="">Select</option>
                                                                <option value="EXW">EXW</option>
                                                                <option value="DAP">DAP</option>
                                                                <option value="DDP">DDP</option>
                                                                <option value="CIP">CIP</option>
                                                                <option value="DAT">DAT</option>
                                                                <option value="FCA">FCA</option>
                                                                <option value="CPT">CPT</option>
                                                                <option value="FAS">FAS</option>
                                                                <option value="FOB">FOB</option>
                                                                <option value="CFR">CFR</option>
                                                                <option value="CIF">CIF</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="saveIncoterms" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editBottomRight">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Edit</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label>Place of jur.:<br><input class="form-control form-100" type="text" name="placeOfJur" value="<?= $offer['plantLocationCity']; ?>"></label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="saveBottomRight" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addExisting">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Do you want to create component?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-footer">
                                                    <input type="submit" name="saveEx" value="Yes" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteOffer">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Are you sure to delete offer <?= $offer['offerNo']; ?>?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-footer">
                                                    <input type="submit" onclick="redBorder()" name="deleteOffer" value="Yes, delete" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="rev">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Do you want to create revision?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-footer">
                                                    <input class="hidden" name='revCmpId' id='revOfferNo' type="text">
                                                    <input type="submit" name="saveRev" value="Yes, create" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="copyPosition">
                                    <div class="modal-dialog" style="margin-top:10px;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Do you want to copy this position?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <div class="modal-footer">
                                                <input class="hidden" name='copyCmpId' id='copyCmpId' type="text">
                                                <input type="submit" id="saveCopy" name="saveCopy" value="Yes, create" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, go back</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addContact">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Adding new contact person</h4>
                                                <button type="button" class="close" data-dismiss="modal" data-toggle="modal" data-target="#inquiryModal">&times;</button>
                                            </div>
                                            <form method="POST" onsubmit="saveContact(); return false">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Gender: *<br>
                                                            <select id="contactGender" class="select2 form-control" required>
                                                                <option value="" selected disabled>Select</option>
                                                                <option value="Male">Male</option>
                                                                <option value="Female">Female</option>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Name: *<br>
                                                            <input type="text" placeholder="Name" class="form-control form-100" id="contactName" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Surname: *<br>
                                                            <input type="text" placeholder="Surname" class="form-control form-100" id="contactSurname" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Adress e-mail: *<br>
                                                            <input type="email" placeholder="Adress e-mail" class="form-control form-100" id="contactEmail" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Phone number 1: *<br>
                                                            <input type="text" placeholder="Phone number 1" class="form-control form-100" id="contactPhone1" required>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Phone number 2:<br>
                                                            <input type="text" placeholder="Phone number 2" class="form-control form-100" id="contactPhone2">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label style="width:200px;">Position:<br>
                                                            <input type="text" placeholder="Position" class="form-control form-100" id="contactPosition">
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="number" id="ifend" class="hidden">
                                                    <input type="number" id="addContactClientId" class="hidden">

                                                    <input type="submit" onclick="redBorder()" name="addContactPerson" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#inquiryModal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            }
                            if (isset($_POST['saveClientInfo'])) {
                                $link = connect();
                                $offerNo = $offer['offerNo'];
                                $client = ($offer['client'] == 0 ? 'EMPTY' : getClientName($offer['client']));
                                $newClient = ($_POST['clientLocation'] == 0 ? 'EMPTY' : getClientName($_POST['clientLocation']));
                                $query = sprintf(
                                    "UPDATE offers SET client='%s', purchase='%s', technican='%s', inquiryNo='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['clientLocation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactPurchase'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactTechnican'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['clientInquiryNo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $link->query($query);
                                $endclientName = getClientName($offer['endClient']);
                                $clients = "$newClient / $endclientName";
                                $link->query("UPDATE stats SET clientShortName='$clients' WHERE offerNo='$offerNo'");
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Changed reseller','Changed reseller from $client to $newClient in offer number $offerNo','$ip')");
                                $dirname = trim(ROOT_OFFERS_DIR . cleanStr($offerNo . "_" . getClientName($_POST['clientLocation']) . "-" . getClientName($offer['endClient']) . "_" . $offer['scope']));
                                $dirname = preg_replace('!\s+!', ' ', $dirname);
                                $oldFolder = glob(ROOT_OFFERS_DIR . $offer['offerNo'] . "*")[0];
                                rename($oldFolder, $dirname);
                                $link->query(sprintf(
                                    "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                                    mysqli_real_escape_string($link, $dirname),
                                    mysqli_real_escape_string($link, $offer['offerNo'])
                                ));
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveEndClientInfo'])) {
                                $link = connect();
                                $offerNo = $offer['offerNo'];
                                $client = ($offer['endClient'] == 0 ? 'EMPTY' : getClientName($offer['endClient']));
                                $newClient = ($_POST['endClientLocation'] == 0 ? 'EMPTY' : getClientName($_POST['endClientLocation']));
                                $query = sprintf(
                                    "UPDATE offers SET finalClient='%s', endclientContactPurchase='%s', endclientContactTechnican='%s', endclientInquiryNo='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['endClientLocation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactPurchaseEnd'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactTechnicanEnd'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['clientInquiryNoEnd'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $clientName = getClientName($offer['client']);
                                $clients = "$clientName / $newClient";
                                $link->query("UPDATE stats SET clientShortName='$clients' WHERE offerNo='$offerNo'");
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Changed end client','Changed end client from $client to $newClient in offer number $offerNo','$ip')");
                                $dirname = trim(ROOT_OFFERS_DIR . cleanStr($offerNo . "_" . getClientName($offer['client']) . "-" . getClientName($_POST['endClientLocation']) . "_" . $offer['scope']));
                                $dirname = preg_replace('!\s+!', ' ', $dirname);
                                $oldFolder = glob(ROOT_OFFERS_DIR . $offer['offerNo'] . "*")[0];
                                rename($oldFolder, $dirname);
                                $link->query(sprintf(
                                    "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                                    mysqli_real_escape_string($link, $dirname),
                                    mysqli_real_escape_string($link, $offer['offerNo'])
                                ));
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveProjectInfo'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET finalClient='%s', productionLocation='%s', plantLocationCountry='%s', plantLocationCity='%s', inquiryNo='%s', `projectName`='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['finalClient'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['productionLocation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['plantLocationCountry'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['plantLocationCity'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['inquiryNo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['projectName'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $link->query($query);
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveSalesInfo'])) {
                                $competitors = "";
                                foreach ($_POST['competitor'] as $compet)
                                    $competitors .= $compet . ";";
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET OVE='%s', `GO`='%s', `GET`='%s', competitor='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['OVE'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['GO'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['GET'])),
                                    mysqli_real_escape_string($link, strip_tags($competitors)),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $link->query($query);
                                $go = $_POST['GO'];
                                $get = $_POST['GET'];
                                $gxg = $go * $get / 100;
                                $link->query("UPDATE stats SET `GO`='$go', `GET`='$get', GxG='$gxg' WHERE idOferty='$id'");
                                $result = $link->query("SELECT * FROM stats WHERE idOferty='$id'");
                                while ($row = $result->fetch_object()) {
                                    $idCMP = $row->IdCmp;
                                    $OVgg = (($row->OV) / 100) * (($row->GO) / 100) * ($row->GET);
                                    $link->query("UPDATE stats SET OVgg='$OVgg' WHERE idOferty='$id' AND idCmp='$idCMP'");
                                }
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveStaffInfo'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET  OT='%s', projectName='%s', projectId='%s', PM='%s', InPM='%s', company='%s', scope='%s', productionReservation='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffOfferType'])),
                                    mysqli_real_escape_string($link, strip_tags(getProjectName($_POST['staffProject']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffProject'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffPM'])),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['staffPM']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffSalesCmp'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffScope'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['staffProductionReservation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $company = $_POST['staffSalesCmp'];
                                $on = $offer['offerNo'];
                                $link->query("UPDATE stats SET company='$company' WHERE offerNo='$on'");
                                $link->query($query);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer top bar edit','OT=" . $_POST['staffOfferType'] . ", projectName=" . getProjectName($_POST['staffProject']) . ", projectId=" . $_POST['staffProject'] . ", PM=" . $_POST['staffPM'] . ", InPM=" . inicialy($_POST['staffPM']) . ", company=" . $_POST['staffSalesCmp'] . ", description=" . $_POST['staffScope'] . ", productionReservation=" . $_POST['staffProductionReservation'] . " within offer number $on','$ip')");
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveDatesInfo'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `inquiry`='%s', `request`='%s', `offer`='%s', `nextContactDate`='%s', `order`='%s', `deliveryDate`='%s', `requestedDeliveryDate`='%s', `requestedOrderDate`='%s', V='%s', InR='%s', oID='%s', InID='%s', F='%s', InF='%s', PM='%s', inPM='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['inquiry'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['request'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offer'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['nextContactDateEditDates'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['order'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['delivery'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['requestedDeliveryDate'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['requestedOrderEditDates'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['datesV'])),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['datesV']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['datesID'])),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['datesID']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['datesF'])),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['datesF']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['datesPM'])),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['datesPM']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $link->query($query);
                                countStep($offer['id']);
                                $idate = $_POST['inquiry'];
                                $odate = $_POST['order'];
                                $offerId = $_POST['offerIdEdit'];
                                $link->query("UPDATE stats SET inquiryDate='$idate', orderDate='$odate' WHERE idOferty='$offerId'");
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer timeline edit','inquiry=" . $_POST['inquiry'] . ", request=" . $_POST['request'] . ", offer=" . $_POST['offer'] . ", nextContactDate=" . $_POST['nextContactDateEditDates'] . ", requestedOrder=" . $_POST['requestedOrderEditDates'] . ", order=" . $_POST['order'] . ", deliveryDate=" . $_POST['delivery'] . ", requestedDeliveryDate=" . $_POST['requestedDeliveryDate'] . ", R=" . $_POST['datesV'] . ", InR=" . inicialy($_POST['datesID']) . ", IS=" . $_POST['datesID'] . ", InIS=" . inicialy($_POST['datesID']) . ", F=" . $_POST['datesF'] . ", InF=" . inicialy($_POST['datesF']) . ", PM=" . $_POST['datesPM'] . ", inPM=" . inicialy($_POST['datesPM']) . "','$ip')");
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['lostSave'])) {
                                $status = "";
                                switch ($_POST['lostAXNEW']) {
                                    case 'Order':
                                        $status = '<i class="fa fa-check" style="color:#00FF00;margin:4px 3px;font-size:20px;"></i>';
                                        break;
                                    case 'Lost':
                                        $status = '<i class="fa fa-times" style="color:red;margin:4px 5px;font-size:20px;"></i>';
                                        break;
                                    case 'Terminated':
                                        $status = '<i class="fa fa-times" style="color:yellow;margin:4px 5px;font-size:20px;"></i>';
                                        break;
                                }
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `GO`='0', `GET`='0', `AX`='%s', `reason`='%s', step='4', firstColStatus='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['lostAXNEW'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['lostReasonNEW'])),
                                    mysqli_real_escape_string($link, htmlspecialchars($status)),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $offId = $offer['id'];
                                $ax = $_POST['lostAXNEW'];
                                $reason = $_POST['lostReasonNEW'];
                                $offerNo = $offer['offerNo'];
                                $link->query("UPDATE components SET AX='$ax', `GO`='0', `GET`='0', reason='$reason' WHERE offerNo='$offerNo'");
                                $link->query("UPDATE stats SET status='$ax', `GO`='0', `GET`='0' WHERE idOferty='$offId'");
                                $link->close();
                                countHitrates($offer['client']);
                                countStep($offId);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Lose offer','Offer number " . $offer['offerNo'] . " has been " . $_POST['lostAXNEW'] . " with reason " . $_POST['lostReasonNEW'] . "','$ip')");
                                header("Location: offer.php?id=" . $offer['id'] . "&lose=1");
                            }
                            if (isset($_POST['saveTechnicalInfo'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE components SET `WHP`='%s', `WHS`='%s', `stat`='%s', `prot`='%s', `medium`='%s', `DN`='%s', `m3`='%s', `kg`='%s', `pressure`='%s', `componentType`='%s', technicalDone='1' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['whp'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['whs'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['stat'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['prot'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['medium'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['DN'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['m3'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['kg'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['pressure'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['componentType'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['addTechId']))
                                );
                                $link->query($query);
                                $offerNo = $offer['offerNo'];
                                $hoursP = $_POST['whp'];
                                $hoursS = $_POST['whs'];
                                $link->query("UPDATE offers SET WHP=(SELECT SUM(WHP) FROM components WHERE offerNo='$offerNo' AND counts='1'), WHS=(SELECT SUM(WHS) FROM components WHERE offerNo='$offerNo' AND counts='1') WHERE offerNo='$offerNo'");
                                $link->close();
                                countSummaryOffer($offer['offerNo']);
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1&offerModal=1");
                            }
                            if (isset($_POST['saveOrderInfo'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `orderNo`='%s', `clientOrderNo`='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['orderNo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['clientOrderNo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offerIdEdit']))
                                );
                                $link->query($query);
                                $link->close();
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveIncoterms'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `incoterms`='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['incoterms'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveBottomRight'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `plantLocationCity`='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['placeOfJur'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveEx'])) {
                                $lastOfferIndex = lastOfferIndex($offer['offerNo']);
                                $i = $lastOfferIndex + 1;
                                $offerNo = "";
                                if ($i < 10)
                                    $offerNo = $offer['offerNo'] . "-0" . $i;
                                else
                                    $offerNo = $offer['offerNo'] . "-" . $i;
                                duplicateOffer($offer['id'], $offerNo);
                                $off = $offer['offerNo'];
                                $link = connect();
                                $link->query("UPDATE offers SET SOC=SOC+1 WHERE offerNo LIKE CONCAT('$offerNo','%')");
                                alertSuccess("Offer has been duplicated.");
                            }
                            if (isset($_POST['noteSave'])) {
                                $link = connect();
                                if (!empty($_POST['notesInput'])) {
                                    $query = sprintf(
                                        "INSERT INTO `comments`(`offerId`, `name`, `surname`, `note`) VALUES ('%s','%s','%s','%s')",
                                        mysqli_real_escape_string($link, strip_tags($offer['id'])),
                                        mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['imie'])),
                                        mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['nazwisko'])),
                                        mysqli_real_escape_string($link, strip_tags(str_replace("'", "`", $_POST['notesInput'])))
                                    );
                                    $link->query($query);
                                    $note = $offer['note'] . ";" . str_replace("'", "`", $_POST['notesInput']);
                                    $idOferty = $offer['id'];
                                    $link->query("UPDATE offers SET note='$note' WHERE id='$idOferty'");
                                    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                    $ip = $_SERVER['REMOTE_ADDR'];
                                    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer add comment','$note','$ip')");
                                }
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&noteSuccess=1");
                            }
                            if (isset($_POST['saveStage'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `AX`='', step='%s', reason='' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['stage'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $off = $offer['offerNo'];
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer change stage','Changed stage to " . $_POST['stage'] . " of offer number $off','$ip')");
                                $result = $link->query("SELECT max(position) as pos FROM components WHERE offerNo='$off'");
                                $row = $result->fetch_object();
                                $maxPos = $row->pos;
                                for ($i = 1; $i <= $maxPos; $i++) {
                                    $maxRevs = $link->query("SELECT max(revision) as rev FROM components WHERE offerNo='$off' AND position='$i'");
                                    $revRow = $maxRevs->fetch_object();
                                    $rev = $revRow->rev;
                                    $revId = $revRow->id;
                                    $offerId = $offer['id'];
                                    $link->query("UPDATE components SET AX='', reason='' WHERE offerNo='$off' AND position='$i' AND revision='$rev'");
                                    $thisIds = $link->query("SELECT * FROM components WHERE offerNo='$off' AND position='$i' AND revision='$rev'");
                                    $thisId = $thisIds->fetch_object();
                                    $cmpId = $thisId->id;
                                    $link->query("UPDATE stats SET status='Offer' WHERE idOferty='$offerId' AND idCmp='$cmpId'");
                                }
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&stageSuccess=1");
                            }
                            if (isset($_POST['saveOffer'])) {
                                $competitors = "";
                                foreach ($_POST['offCompetitor'] as $compet)
                                    $competitors .= $compet . ";";
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET offer='%s', `GO`='%s', `GET`='%s', OVE='%s', competitor='%s', productionReservation='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['offOfferDate'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offGO'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offGET'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offOve'])),
                                    mysqli_real_escape_string($link, strip_tags($competitors)),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offProdRes'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $query = sprintf(
                                    "UPDATE stats SET `GO`='%s', `GET`='%s' WHERE idOferty='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['offGO'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['offGET'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit offer','offer=" . $_POST['offOfferDate'] . ", GO=" . $_POST['offGO'] . ", GET=" . $_POST['offGET'] . ", OVE=" . $_POST['offOve'] . ", competitor=" . $competitors . ", productionReservation=" . $_POST['offProdRes'] . " within offer number " . $offer['offerNo'] . "','$ip')");
                                countStep($offer['id']);
                                $link->close();
                                countSummaryOffer($offer['offerNo']);
                                header("Location: offer.php?id=" . $offer['id'] . "&offerSuccess=1");
                            }
                            if (isset($_POST['deleteOffer'])) {
                                $link = connect();
                                $idOfferDelete = $offer['id'];
                                $off = $offer['offerNo'];
                                $link->query("DELETE FROM offers WHERE id='$idOfferDelete'");
                                $link->query("DELETE FROM components WHERE offerNo='$off'");
                                header("Location: index.php?deletedOffer=" . $offer['offerNo']);
                            }
                            if (isset($_POST['saveDel'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET deliveryDate='%s', plantLocationCity='%s', incoterms='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['delDeliveryDate'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['delPlantLocation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['delIncoterms'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                countStep($offer['id']);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer delivery edit','deliveryDate=" . $_POST['delDeliveryDate'] . ", plantLocationCity=" . $_POST['delPlantLocation'] . ", incoterms=" . $_POST['delIncoterms'] . " within offer number " . $offer['offerNo'] . "','$ip')");
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveService'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET serviceStart='%s', serviceEnd='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['serviceStart'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['serviceEnd'])),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                countStep($offer['id']);
                                header("Location: offer.php?id=" . $offer['id'] . "&editSuccess=1");
                            }
                            if (isset($_POST['saveOO'])) {
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $orderDate = $_POST['OOOrderDate'];
                                $pm = $_POST['OOPM'];
                                $clientOrderNo = $_POST['OOClientOrderNo'];
                                $inpm = inicialy($_POST['OOPM']);
                                $link = connect();
                                $off = $offer['offerNo'];
                                $cmpIds = $_POST['cmpIds'];
                                $orderCompany = $_POST['OOOrderCompany'];
                                $term = 1;
                                $order = 0;
                                $left = 0;
                                foreach ($cmpIds as $cmp) {
                                    $cmpInfo = getComponentInfo($cmp);
                                    $ov = $cmpInfo['OV'];
                                    $go = $cmpInfo['GO'];
                                    $get = $cmpInfo['GET'];
                                    $OVgg = (($ov) / 100) * (($go) / 100) * ($get);
                                    $ax = $_POST[$cmp . "ax"];
                                    $comPosLog = getCmpPos($cmp);
                                    if ($ax == "Lost" || $ax == "Terminated") {
                                        if ($ax == "Lost")
                                            $term = 0;
                                        $reason = $_POST[$cmp . 'reason'];
                                        $link->query("UPDATE components SET AX='$ax', reason='$reason', `GO`='0', `GET`='0' WHERE id='$cmp'");
                                        $link->query("UPDATE stats SET status='$ax', `GO`='0', `GET`='0', orderDate='$orderDate' WHERE idCmp='$cmp'");
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','Component position $comPosLog, AX=$ax, reason=$reason within offer number " . $offer['offerNo'] . "','$ip')");
                                    }
                                    if ($ax == "Order") {
                                        $order = 1;
                                        $link->query("UPDATE components SET AX='$ax', `GO`='100', `GET`='100', reason='' WHERE id='$cmp'");
                                        $link->query("UPDATE stats SET status='$ax', `GO`='100', `GET`='100', orderDate='$orderDate' WHERE idCmp='$cmp'");
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','Component position $comPosLog, AX=$ax, reason=, GO=100, GET=100, orderDate=$orderDate within offer number " . $offer['offerNo'] . "','$ip')");
                                    }
                                    if ($ax == "") {
                                        $link->query("UPDATE components SET AX='', reason='' WHERE id='$cmp'");
                                        $link->query("UPDATE stats SET status='Offer', `GO`='$go', `GET`='$get', OV='$ov', OVgg='$OVgg', orderDate='$orderDate' WHERE idCmp='$cmp'");
                                        $left = 1;
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','Component position $comPosLog, AX=, reason= within offer number " . $offer['offerNo'] . "','$ip')");
                                    }
                                }
                                if ($left == 0) {
                                    if ($order == 1) {
                                        $link->query("UPDATE offers SET AX='Order', `GO`='100', `GET`='100', PM='$pm', inPM='$inpm', `order`='$orderDate', clientOrderNo='$clientOrderNo', orderCompany='$orderCompany' WHERE offerNo='$off'");
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','PM=$pm, inPM=$inpm, order=$orderDate, clientOrderNo=$clientOrderNo, orderCompany=$orderCompany, AX=Order, GO=100, GET=100 within offer number " . $offer['offerNo'] . "','$ip')");
                                    } else {
                                        if ($term == 0) {
                                            $link->query("UPDATE offers SET AX='Lost', `GO`='0', `GET`='0', reason='$reason', PM='$pm', inPM='$inpm', `order`='$orderDate', clientOrderNo='$clientOrderNo', orderCompany='$orderCompany' WHERE offerNo='$off'");
                                            $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','PM=$pm, inPM=$inpm, order=$orderDate, clientOrderNo=$clientOrderNo, orderCompany=$orderCompany, AX=Lost, reason=$reason, GO=0, GET=0 within offer number " . $offer['offerNo'] . "','$ip')");
                                        } else {
                                            $link->query("UPDATE offers SET AX='Terminated', `GO`='0', `GET`='0', reason='$reason', PM='$pm', inPM='$inpm', `order`='$orderDate', clientOrderNo='$clientOrderNo', orderCompany='$orderCompany' WHERE offerNo='$off'");
                                            $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit conclusion','PM=$pm, inPM=$inpm, order=$orderDate, clientOrderNo=$clientOrderNo, orderCompany=$orderCompany, AX=Terminated, reason=$reason, GO=0, GET=0 within offer number " . $offer['offerNo'] . "','$ip')");
                                        }
                                    }
                                    $off = $offer['offerNo'];
                                    $result = $link->query("SELECT * FROM components WHERE AX='' AND counts=0 AND offerNo='$off'");
                                    while ($row = $result->fetch_object()) {
                                        $id = $row->id;
                                        $link->query("UPDATE components SET AX='Lost' WHERE id='$id'");
                                        $link->query("UPDATE stats SET status='Lost' WHERE IdCmp='$id'");
                                    }
                                } else
                                    $link->query("UPDATE offers SET AX='', reason='', PM='$pm', inPM='$inpm', `order`='$orderDate', clientOrderNo='$clientOrderNo', orderCompany='$orderCompany' WHERE offerNo='$off'");
                                countHitrates($offer['client']);
                                countHitrates($offer['endClient']);
                                countStep($offer['id']);
                                if ($order == 1)
                                    header("Location: offerOrder.php?id=" . $offer['id']);
                                else
                                    header("Location: offer.php?id=" . $offer['id'] . "&done=1");
                            }
                            if (isset($_POST['createCopy'])) {
                                $offer = getOfferId(createOfferCopy($offer['offerNo']));
                                header("Location: offer.php?id=" . $offer['id'] . "&copied=$offer");
                            }
                            if (isset($_POST['saveFill'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET projectName='%s', inquiryNo='%s', finalClient='%s', plantLocationCountry='%s', plantLocationCity='%s', scope='%s', segment='%s', BT='%s', orderLocation='%s', productionLocation='%s', offer='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillProjectName'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillInquiryNo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillFinalClient'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillPlantLocationCountry'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillPlantLocationCity'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillScope'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillSegment'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillBT'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillOrderLocation'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fillProductionLocation'])),
                                    mysqli_real_escape_string($link, strip_tags(date("Y-m-d"))),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                include('assets/libs/PHPMailer/mail.php');
                                echo "<div class='hidden'>";
                                //wyslijMaila(getUserEmail($offer['oID']), "Please fill technical information in this offer.", 'Offer '.$offer['offerNo'].' has been filled out.');
                                echo "</div>";
                                header("Location: offer.php?id=" . $offer['id'] . "&fill=1");
                            }
                            if (isset($_POST['saveFollow'])) {

                                $next_contact_date = $_POST['foNext'];
                                $contact_date = $_POST['foCurrent'];


                                $contact_error = 0;

                                if ($next_contact_date < date('Y-m-d')) {
                                    $contact_error = 1;
                                } else if ($contact_date > $next_contact_date) {
                                    $contact_error = 2;
                                } else if ($next_contact_date == $contact_date) {
                                    $contact_error = 3;
                                }

                                if ($contact_error) {
                                    header("Location: offer.php?id=" . $offer['id'] . "&contact_error=" . $contact_error . "");
                                    exit();
                                }

                                $link = connect();
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer follow up edit','GO=" . $_POST['foGO'] . ", GET=" . $_POST['foGET'] . ", order=" . $_POST['foOrderDate'] . ", nextContactDate=" . $_POST['foNext'] . ", lastComment=" . str_replace("'", "`", $_POST['foLastCommenct']) . ", OT=" . $_POST['foOT'] . ", OVE=" . $_POST['foOVE'] . ", requestedDeliveryDate=" . $_POST['foRequestedDeliveryDate'] . ", productionReservation=" . $_POST['foProdReserv'] . " within offer number " . $offer['offerNo'] . "','$ip')");
                                if ($_POST['includeContact'] == 1) {
                                    $query = sprintf(
                                        "UPDATE offers SET `GO`='%s', `GET`='%s', `requestedOrderDate`='%s', `nextContactDate`='%s', lastComment='%s', OT='%s', OVE='%s', requestedDeliveryDate='%s', productionReservation='%s', F='%s', inF='%s' WHERE id='%s'",
                                        mysqli_real_escape_string($link, strip_tags($_POST['foGO'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foGET'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOrderDate'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foNext'])),
                                        mysqli_real_escape_string($link, strip_tags(str_replace("'", "`", $_POST['foLastCommenct']))),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOT'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOVE'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foRequestedDeliveryDate'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foProdReserv'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['fFollowUp'])),
                                        mysqli_real_escape_string($link, inicialy(strip_tags($_POST['fFollowUp']))),
                                        mysqli_real_escape_string($link, strip_tags($offer['id']))
                                    );
                                    $link->query($query);
                                    countStep($offer['id']);
                                    $go = $_POST['foGO'];
                                    $get = $_POST['foGET'];
                                    $id = $offer['id'];
                                    $orderDate = $_POST['foOrderDate'];
                                    if ($offer['AX'] != "Order") {
                                        $link->query("UPDATE stats SET `GO`='$go', `GET`='$get', `orderDate`='$orderDate' WHERE status!='Lost' AND status!='Terminated' AND idOferty='$id'");
                                    }
                                    $cmnt = str_replace("'", "`", $_POST['foLastCommenct']);
                                    $next = $_POST['foNext'];
                                    $offerId = $offer['id'];
                                    $con = $_SESSION['plasticonDigitalUser']['id'];
                                    $data = $_POST['foCurrent'];
                                    $conW = $_POST['foContactWith'];

                                    $link->query("INSERT INTO `contacts`(`offerId`, `contact`, `contactWith`, `nextContactDate`, `contactDate`, `note`) VALUES ('$offerId','$con','$conW','$next','$data','$cmnt')");
                                    countHitrates($offer['client']);
                                    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer contact with client','contact=" . $con . ", contactWith=" . $conW . ", nextContactDate=" . $next . ", contactDate=" . $data . ", note=" . $cmnt . " within offer number " . $offer['offerNo'] . "','$ip')");
                                    header("Location: offer.php?id=" . $offer['id'] . "&follow=1");
                                } else {
                                    $query = sprintf(
                                        "UPDATE offers SET `GO`='%s', `GET`='%s', `requestedOrderDate`='%s', OT='%s', OVE='%s', requestedDeliveryDate='%s', productionReservation='%s' WHERE id='%s'",
                                        mysqli_real_escape_string($link, strip_tags($_POST['foGO'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foGET'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOrderDate'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOT'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foOVE'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foRequestedDeliveryDate'])),
                                        mysqli_real_escape_string($link, strip_tags($_POST['foProdReserv'])),
                                        mysqli_real_escape_string($link, strip_tags($offer['id']))
                                    );
                                    $link->query($query);
                                    countStep($offer['id']);
                                    $go = $_POST['foGO'];
                                    $get = $_POST['foGET'];
                                    $id = $offer['id'];
                                    $orderDate = $_POST['foOrderDate'];
                                    if ($offer['AX'] != "Order") {
                                        $link->query("UPDATE stats SET `GO`='$go', `GET`='$get', orderDate='$orderDate' WHERE status!='Lost' AND status!='Terminated' AND idOferty='$id'");
                                    }
                                    countHitrates($offer['client']);
                                    header("Location: offer.php?id=" . $offer['id'] . "&follow=1");
                                }
                            }
                            if (isset($_POST['acSave'])) {


                                $next_contact_date = $_POST['acNext'];
                                $contact_date = $_POST['acCurrent'];


                                $contact_error = 0;

                                if ($next_contact_date < date('Y-m-d')) {
                                    $contact_error = 1;
                                } else if ($contact_date > $next_contact_date) {
                                    $contact_error = 2;
                                } else if ($next_contact_date == $contact_date) {
                                    $contact_error = 3;
                                }

                                if ($contact_error) {
                                    header("Location: offer.php?id=" . $offer['id'] . "&contact_error=" . $contact_error . "");
                                    exit();
                                }

                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `nextContactDate`='%s', lastComment='%s', F='%s', inF='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($_POST['acNext'])),
                                    mysqli_real_escape_string($link, strip_tags(str_replace("'", "`", $_POST['acLastCommenct']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['fnFollowUp'])),
                                    mysqli_real_escape_string($link, inicialy(strip_tags($_POST['fnFollowUp']))),
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                $cmnt = str_replace("'", "`", $_POST['acLastCommenct']);
                                $next = $_POST['acNext'];
                                $offerId = $offer['id'];
                                $con = $_SESSION['plasticonDigitalUser']['id'];
                                $data = $_POST['acCurrent'];
                                $conW = $_POST['acContactWith'];
                                $link->query("INSERT INTO `contacts`(`offerId`, `contact`, `contactWith`, `nextContactDate`, `contactDate`, `note`) VALUES ('$offerId','$con','$conW','$next','$data','$cmnt')");
                                header("Location: offer.php?id=" . $offer['id'] . "&contactAdded=1");
                            }
                            if (isset($_POST['saveInqData'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET `OT`='%s', `scope`='%s', OVE='%s', oID='%s', InID='%s', V='%s', InR='%s', F='%s', InF='%s', projectId='%s', projectName='%s', company='%s', inquiry='%s', request='%s', `requestedOrderDate`='%s', client='%s', purchase='%s', technican='%s', inquiryNo='%s', finalClient='%s', endclientContactPurchase='%s', endclientContactTechnican='%s', endclientInquiryNo='%s', nextContactDate='%s', `oldOfferNo`='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, $_POST['inqOT']),
                                    mysqli_real_escape_string($link, $_POST['inqScope']),
                                    mysqli_real_escape_string($link, $_POST['inqOve']),
                                    mysqli_real_escape_string($link, $_POST['inqIS']),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['inqIS']))),
                                    mysqli_real_escape_string($link, $_POST['inqR']),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['inqR']))),
                                    mysqli_real_escape_string($link, $_POST['inqFollowUp']),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['inqFollowUp']))),
                                    mysqli_real_escape_string($link, strip_tags($_POST['inqProject'])),
                                    mysqli_real_escape_string($link, strip_tags(getProjectName($_POST['inqProject']))),
                                    mysqli_real_escape_string($link, $_POST['inqCompany']),
                                    mysqli_real_escape_string($link, $_POST['inqInquiry']),
                                    mysqli_real_escape_string($link, $_POST['inqRequest']),
                                    mysqli_real_escape_string($link, $_POST['inqRequestedOrderDate']),
                                    mysqli_real_escape_string($link, $_POST['inqClientLocation']),
                                    mysqli_real_escape_string($link, $_POST['inqContactPurchase']),
                                    mysqli_real_escape_string($link, $_POST['inqContactTechnican']),
                                    mysqli_real_escape_string($link, $_POST['inqInquiryNo']),
                                    mysqli_real_escape_string($link, $_POST['inqEndClientLocation']),
                                    mysqli_real_escape_string($link, $_POST['inqContactPurchaseEnd']),
                                    mysqli_real_escape_string($link, $_POST['inqContactTechnicanEnd']),
                                    mysqli_real_escape_string($link, $_POST['inqInqNoEnd']),
                                    mysqli_real_escape_string($link, $_POST['inqFollowUpDate']),
                                    mysqli_real_escape_string($link, $_POST['internalNumber']),
                                    mysqli_real_escape_string($link, $offer['id'])
                                );
                                $link->query($query);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Offer edit inquiry','OT=" . $_POST['inqOT'] . ", Description=" . $_POST['inqScope'] . ", OVE=" . $_POST['inqOve'] . ", IS=" . $_POST['inqIS'] . ", InIS=" . inicialy($_POST['inqIS']) . ", R=" . $_POST['inqR'] . ", InR=" . inicialy($_POST['inqR']) . ", F=" . $_POST['inqFollowUp'] . ", InF=" . inicialy($_POST['inqFollowUp']) . ", projectId=" . $_POST['inqProject'] . ", projectName=" . getProjectName($_POST['inqProject']) . ", company=" . $_POST['inqCompany'] . ", inquiry=" . $_POST['inqInquiry'] . ", request=" . $_POST['inqRequest'] . ", requestedOrderDate=" . $_POST['inqRequestedOrderDate'] . ", client=" . $_POST['inqClientLocation'] . ", purchase=" . $_POST['inqContactPurchase'] . ", technican=" . $_POST['inqContactTechnican'] . ", inquiryNo=" . $_POST['inqInquiryNo'] . ", finalClient=" . $_POST['inqEndClientLocation'] . ", endclientContactPurchase=" . $_POST['inqContactPurchaseEnd'] . ", endclientContactTechnican=" . $_POST['inqContactTechnicanEnd'] . ", endclientInquiryNo=" . $_POST['inqInqNoEnd'] . ", nextContactDate=" . $_POST['inqFollowUpDate'] . " within offer number " . $offer['offerNo'] . "','$ip')");
                                $query = sprintf(
                                    "UPDATE stats SET `OT`='%s', `scope`='%s', inIS='%s', inR='%s', company='%s', inquiryDate='%s', `orderDate`='%s', clientShortName='%s' WHERE offerNo='%s'",
                                    mysqli_real_escape_string($link, $_POST['inqOT']),
                                    mysqli_real_escape_string($link, $_POST['inqScope']),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['inqIS']))),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['inqR']))),
                                    mysqli_real_escape_string($link, $_POST['inqCompany']),
                                    mysqli_real_escape_string($link, $_POST['inqInquiry']),
                                    mysqli_real_escape_string($link, $_POST['inqRequestedOrderDate']),
                                    mysqli_real_escape_string($link, getClientShortName($_POST['inqClientLocation']) . " / " . getClientShortName($_POST['inqEndClientLocation'])),
                                    mysqli_real_escape_string($link, $offer['offerNo'])
                                );
                                $link->query($query);
                                $dirname = trim(ROOT_OFFERS_DIR . cleanStr($offer['offerNo'] . "_" . getClientName($_POST['inqClientLocation']) . "-" . getClientName($_POST['inqEndClientLocation']) . "_" . $_POST['inqScope']));
                                $dirname = preg_replace('!\s+!', ' ', $dirname);
                                $oldFolder = glob(ROOT_OFFERS_DIR . $offer['offerNo'] . "*")[0];
                                rename($oldFolder, $dirname);
                                $link->query(sprintf(
                                    "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                                    mysqli_real_escape_string($link, $dirname),
                                    mysqli_real_escape_string($link, $offer['offerNo'])
                                ));
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&inq=1");
                            }
                            if (isset($_POST['finishFollow'])) {
                                $link = connect();
                                $query = sprintf(
                                    "UPDATE offers SET step='3' WHERE id='%s'",
                                    mysqli_real_escape_string($link, strip_tags($offer['id']))
                                );
                                $link->query($query);
                                header("Location: offer.php?id=" . $offer['id'] . "&follow=1");
                            }
                            if (isset($_POST['saveRev'])) {
                                $id = $_POST['revCmpId'];
                                $cmp = getComponentInfo($id);
                                $link = connect();
                                $offerNo = $cmp['offerNo'];
                                $link->query("UPDATE offers SET SOC=SOC+1 WHERE offerNo LIKE CONCAT('$offerNo','%')");
                                createRev($id, $cmp);
                                header("Location: offer.php?id=" . $offer['id'] . "&rev=1&offerModal=1");
                            }
                            if (isset($_POST['deleteFile'])) {
                                unlink($_POST['fileName']);
                                header("Location: offer.php?id=" . $_POST['offerIdEdit'] . "&fileDelete=1");
                            }
                            if (isset($_POST['createFolder'])) {

                                $link = connect();

                                $dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", str_replace("\\", "-", $offer['offerNo'] . "_" . getClientName($offer['client']) . "-" . getClientName($offer['endClient']) . "_" . $offer['scope']))))));
                                $dirname = preg_replace('!\s+!', ' ', $dirname);

                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];

                                // Create folders for offer
                                $folder_error = false;
                                $parent_folder_created = mkdir("../../offers/$dirname");

                                if (!$parent_folder_created) {
                                    $folder_error = 1;
                                    $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
                                }

                                if (!$folder_error) {
                                    $companies = ["PP", "PG", "PTN", "TP", "PT"];

                                    foreach ($companies as $com) {
                                        $company_folder_created = mkdir("../../offers/$dirname/$com");

                                        if (!$company_folder_created) {
                                            $folder_error = 2;
                                            $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
                                            continue;
                                        }

                                        $nested_folders = ["1 Inquiry", "2 Subsuplier", "3 Calculation", "4 Offer", "5 Order", "6 Others"];

                                        foreach ($nested_folders as $nested) {
                                            $nested_folder_created = mkdir("../../offers/$dirname/$com/$nested");

                                            if (!$nested_folder_created) {
                                                $folder_error = 3;
                                                $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
                                                continue;
                                            }
                                        }
                                    }
                                }

                                if ($parent_folder_created) {
                                    $link->query(sprintf(
                                        "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                                        mysqli_real_escape_string($link, $dirname),
                                        mysqli_real_escape_string($link, $offer['offerNo'])
                                    ));
                                    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Created offer folder','Created offer folder $dirname','$ip')");
                                }

                                $link->close();

                                $go_to = "Location: offer.php?id=" . $offer['id'] . "";

                                if (!$folder_error) {
                                    $go_to .= "&folder=1";
                                }

                                if ($folder_error) {
                                    $go_to .= "&folder_error=" . $folder_error;
                                }

                                header($go_to);
                            }
                            ?>
                            <!--<div class="row">
                                    <div class="col-lg-2 text-center"><button data-toggle="modal" data-target="#add" class="btn btn-primary form-100">Add contact</button></div>
                            </div>-->
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="add">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Add contact.</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label for="name">Contact With:<br>
                                                        <select name="contactWith" class="form-control select2">
                                                            <?php listContactsPerson($offer['client']); ?>
                                                        </select><span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" data-dismiss="modal" data-toggle="modal" data-target="#addCon">Add new</button></span>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name">Contact date:<br>
                                                        <input class="form-control form-100" type="date" name="contactDate" placeholder="Contact date" required></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name">Next contact date:<br>
                                                        <input class="form-control form-100" type="date" name="nextContactDate" placeholder="Next contact date" required></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="surname">Note:<br>
                                                        <textarea class="form-control form-100" style="height:300px;" name="note"></textarea></label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" name="saveContact" onclick="redBorder()" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addCon">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Adding new contact person</h4>
                                            <button type="button" class="close" data-dismiss="modal" data-toggle="modal" data-target="#followUpModal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label>Gender: *<br>
                                                        <select name="contactGender" class="select2 form-contro" required>
                                                            <option value="" selected disabled>Select</option>
                                                            <option value="Male">Male</option>
                                                            <option value="Female">Female</option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Name: *<br>
                                                        <input type="text" placeholder="Name" class="form-control" name="contactName" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Surname: *<br>
                                                        <input type="text" placeholder="Surname" class="form-control" name="contactSurname" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Adress e-mail: *<br>
                                                        <input type="email" placeholder="Adress e-mail" class="form-control" name="contactEmail" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Direct number: *<br>
                                                        <input type="text" placeholder="Direct number" class="form-control" name="contactPhone1" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Mobile number:<br>
                                                        <input type="text" placeholder="Mobile number" class="form-control" name="contactPhone2">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label>Position:<br>
                                                        <input type="text" placeholder="Position" class="form-control" name="contactPosition">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" onclick="redBorder()" name="addContactPerson" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#followUpModal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteR">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Are you sure to delete this contact?</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-footer">
                                                    <input type="number" class="hidden" name="deleteId" id="deleteId">
                                                    <input type="submit" name="deleteContact" value="Yes, delete" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editR">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Editing contact.</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name">Contact With:<br>
                                                            <select name="contactWithEdit" id='contactWithEdit' class="form-control select2">
                                                                <?php listContactsPerson($offer['client']); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name">Contact date:<br>
                                                            <input class="form-control form-100" type="date" name="contactDateEdit" id="contactDateEdit" placeholder="Contact date" required></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name">Next contact date:<br>
                                                            <input class="form-control form-100" type="date" name="nextContactDateEdit" id="nextContactDateEdit" placeholder="Next contact date" required></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="surname">Note:<br>
                                                            <textarea class="form-control form-100" style="height:300px;" name="noteEdit" id="noteEdit"></textarea></label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="number" class="hidden" name="editId" id="editId">
                                                    <input type="submit" onclick="redBorder()" name="editContact" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addCalc">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Add calculation</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST">
                                                <div class="modal-body">
                                                    <div class="row justify-content-center">
                                                        <label for="name">Seller: <br>
                                                            <select class="form-control form-100" name="calcFrom" required>
                                                                <option value="" disabled selected>Select</option>
                                                                <?= getCompanies(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name">Buyer: <br>
                                                            <select class="form-control form-100" name="calcTo" required>
                                                                <option value="" disabled selected>Select</option>
                                                                <option value="End client">End client</option>
                                                                <?= getCompanies(); ?>
                                                            </select>
                                                        </label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name">Quote Value (QV) k&euro;:<br>
                                                            <input class="form-control form-100" type="number" min="0" step="0.1" name="calcOV" placeholder="Quote value"></label>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <label for="name">CM%:<br>
                                                            <input class="form-control form-100" type="number" min="0" max="100" name="calcCMp" placeholder="CM%"></label>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="number" class="hidden" name="calcOfferID" id="calcOfferID">
                                                    <input type="submit" name="saveCalc" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php
                            }
                            if (isset($_POST['saveCalc'])) {
                                $link = connect();
                                $query = sprintf(
                                    "INSERT INTO `calculations`(`idOferty`,`sell`,`buy`,`OV`,`CMp`,`CMe`) VALUES ('%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcOfferID'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcFrom'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcTo'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcOV'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcCMp'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['calcOV'] / 100 * $_POST['calcCMp']))
                                );
                                $link->query($query);
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&addCalc=true");
                            }
                            if (isset($_POST['addContactPerson'])) {
                                $link = connect();
                                $clientid = $offer['client'];
                                if ($offer['client'] == 0 || $offer['client'] == "")
                                    $clientid = $offer['endClient'];
                                $query = sprintf(
                                    "INSERT INTO `clientsContacts`(`clientId`, `gender`, `name`, `surname`, `email`, `phone1`, `phone2`, `position`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($link, $clientid),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactGender'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactName'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactSurname'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactEmail'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactPhone1'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactPhone2'])),
                                    mysqli_real_escape_string($link, strip_tags($_POST['contactPosition']))
                                );
                                $link->query($query);
                                $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                $ip = $_SERVER['REMOTE_ADDR'];
                                $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new contact person','gender=" . $_POST['contactGender'] . ", name=" . $_POST['contactName'] . ", surname=" . $_POST['contactSurname'] . ", email=" . $_POST['contactEmail'] . ", phone1=" . $_POST['contactPhone1'] . ", phone2=" . $_POST['contactPhone2'] . ", position=" . $_POST['contactPosition'] . " within client " . getClientName($clientid) . "','$ip')");
                                $link->close();
                                header("Location: offer.php?id=" . $offer['id'] . "&addContactPerson=true");
                            }
                            if (isset($_POST['saveContact'])) {
                                if (!empty($_POST['contactDate']) && !empty($_POST['nextContactDate'])) {
                                    $link = connect();
                                    $offerIds = getOfferIds($id);
                                    foreach ($offerIds as $offerId) {
                                        $query = sprintf(
                                            "INSERT INTO `contacts`(`offerId`, `contact`, `contactWith`, `contactDate`, `nextContactDate`, `note`) VALUES ('%s','%s','%s','%s','%s','%s')",
                                            mysqli_real_escape_string($link, $offerId),
                                            mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['id'])),
                                            mysqli_real_escape_string($link, strip_tags($_POST['contactWith'])),
                                            mysqli_real_escape_string($link, strip_tags($_POST['contactDate'])),
                                            mysqli_real_escape_string($link, strip_tags($_POST['nextContactDate'])),
                                            mysqli_real_escape_string($link, str_replace("\n", "<br>", strip_tags(str_replace("'", "`", $_POST['note']))))
                                        );
                                        $link->query($query);
                                        $ncd = strip_tags($_POST['nextContactDate']);
                                        $link->query("UPDATE offers SET nextContactDate='$ncd' WHERE id='$offerId'");
                                    }
                                    alertSuccess("Contact has been added.");
                                } else
                                    alertDanger("FIll all required fields!");
                            }
                            if (isset($_POST['editContact'])) {
                                if (!empty($_POST['contactDateEdit']) && !empty($_POST['nextContactDateEdit'])) {
                                    $link = connect();
                                    $conIds = getConIds(strip_tags($_POST['editId']));
                                    foreach ($conIds as $conId) {
                                        $oid = $conId[1];
                                        $query = sprintf(
                                            "UPDATE contacts SET contactWith='%s', contactDate='%s', nextContactDate='%s', note='%s' WHERE id='%s'",
                                            mysqli_real_escape_string($link, strip_tags($_POST['contactWithEdit'])),
                                            mysqli_real_escape_string($link, strip_tags($_POST['contactDateEdit'])),
                                            mysqli_real_escape_string($link, strip_tags($_POST['nextContactDateEdit'])),
                                            mysqli_real_escape_string($link, strip_tags(str_replace("'", "`", $_POST['noteEdit']))),
                                            mysqli_real_escape_string($link, $conId[0])
                                        );
                                        $link->query($query);
                                        $nc = $_POST['nextContactDateEdit'];
                                        $lc = $_POST['noteEdit'];
                                        $link->query("UPDATE offers SET nextContactDate='$nc', lastComment='$lc' WHERE id='$oid'");
                                    }
                                    alertSuccess("Contact has been updated.");
                                } else
                                    alertDanger("FIll all required fields!");
                            }
                            if (isset($_POST['deleteContact'])) {
                                $delId = $_POST['deleteId'];
                                $link = connect();
                                $link->query("DELETE FROM contacts WHERE id='$delId'");

                                alertSuccess("Contact has been deleted.");
                            }
                            if (isset($_GET['editSuccess']))
                                alertSuccess("Changes have been saved.");
                            if (isset($_GET['fileSuccess']))
                                alertSuccess("File has been added.");
                            if (isset($_GET['fileDelete']))
                                alertSuccess("File has been deleted.");
                            if (isset($_GET['saveOO']))
                                alertSuccess("Offer has been changed into an order.");
                            if (isset($_GET['lose']))
                                alertSuccess("Offer has been lost.");
                            if (isset($_GET['fill']))
                                alertSuccess("Offer has been filled out.");
                            if (isset($_GET['follow']))
                                alertSuccess("Follow up has been saved.");
                            if (isset($_GET['addContactPerson']))
                                alertSuccess("New contact person has been added to client contact list.");
                            if (isset($_GET['noteSuccess']))
                                alertSuccess("Saved.");
                            if (isset($_GET['stageSuccess']))
                                alertSuccess("Stage has been changed.");
                            if (isset($_GET['addCalc']))
                                alertSuccess("Calculation has been added.");
                            if (isset($_GET['rev']))
                                alertSuccess("Revision added.");
                            if (isset($_GET['copy']))
                                alertSuccess("Copy created.");
                            if (isset($_GET['copied']))
                                alertSuccess("Offer has been copied. <a href='offer.php?id=" . $_GET['copied'] . "'>Click here</a> to see it.");
                            if (isset($_GET['offerSuccess']))
                                alertSuccess("Offer created!");
                            if (isset($_GET['done']))
                                alertSuccess("Offer has become an order!");
                            if (isset($_GET['inq']))
                                alertSuccess("Inquiry number has been updated!");
                            if (isset($_GET['contactAdded']))
                                alertSuccess("Contact has been done!");
                            if (isset($_GET['folder']))
                                alertSuccess("Folder has been created successfully!");

                            if (isset($_GET['folder_error'])) {
                                $code = (int) $_GET['folder_error'];

                                switch ($code) {
                                    case 1:
                                        alertDanger("Error creating offer folder.");
                                        break;
                                    case 2:
                                        alertDanger("Error creating company folders.");
                                        break;
                                    case 3:
                                        alertDanger("Error creating company subfolders.");
                                        break;
                                    default:
                                        alertDanger("Error creating folder.");
                                        break;
                                }
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php require('footer.php'); ?>
    <script>
        <?php
        if (isset($_GET['offerModal'])) {
        ?>
            $('#offerModal').modal("show");
        <?php
        }
        if (isset($_GET['openConcModal'])) {
        ?>
            $('#conclusionModal').modal("show");
        <?php
        }
        if (isset($_GET['openFollow'])) {
        ?>
            $('#followUpModal').modal("show");
        <?php
        }
        ?>
        this.$slideOut = $('#slideOut');

        // Slideout show
        this.$slideOut.find('.slideOutTab').on('click', function() {
            $("#slideOut").toggleClass('showSlideOut');
        });

        function setTechValues(id, tech) {
            var data = tech.split("[-]");
            $('#offerActualId').val(id)
            $('[name="stat"]').val(data[0]).trigger('change');
            $('[name="prot"]').val(data[1]).trigger('change');
            $('[name="medium"]').val(data[2]);
            $('[name="DN"]').val(data[3]);
            $('[name="m3"]').val(data[4]);
            $('[name="pressure"]').val(data[5]).trigger('change');
            $('[name="componentType"]').val(data[6]).trigger('change');
            $('[name="whp"]').val(data[7]);
            $('[name="whs"]').val(data[7]);
        }

        function format(d, id, tech) {
            data = JSON.parse(tech);
            var rtn = '';
            var pos = data['component'].position;
            if (data['component'].revision != 0)
                pos = data['component'].position + "-rev/" + data['component'].revision;
            rtn += '<br><strong>' + data['component'].scope + '</strong><br><table class="control-table" style="border:0px;width:100%">' +
                <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?> '<tr class="suboffers-row"><td class="suboffers-first-col">QV</td><td class="suboffers-second-col"><strong>' + data['component'].OV + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">QV CM%</td><td class="suboffers-second-col"><strong>' + data['component'].CMp + '</strong></td></tr>' +
                <?php } ?> '<tr class="suboffers-row"><td class="suboffers-first-col">Position</td><td class="suboffers-second-col"><strong>' + pos + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Sales article</td><td class="suboffers-second-col"><strong>' + data['component'].scope + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Calc. person</td><td class="suboffers-second-col"><strong>' + data['component'].calcPersonFull + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Project man.</td><td class="suboffers-second-col"><strong>' + data['component'].pmFull + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Status</td><td class="suboffers-second-col"><strong>' + data['component'].AX + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Reason</td><td class="suboffers-second-col"><strong>' + data['component'].reason + '</strong></td></tr>' +
                '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Technical</h4></td><td style="border:0;"></td></tr></table>' +
                '<table class="control-table" style="border:0px;width:100%">' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Static material</td><td class="suboffers-second-col"><strong>' + data['component'].stat + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Protection layer</td><td class="suboffers-second-col"><strong>' + data['component'].prot + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Medium</td><td class="suboffers-second-col"><strong>' + data['component'].medium + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">DN</td><td class="suboffers-second-col"><strong>' + data['component'].DN + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Volume</td><td class="suboffers-second-col"><strong>' + data['component'].m3 + ' m<sup>3</sup></strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Weight</td><td class="suboffers-second-col"><strong>' + data['component'].kg + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Pressure</td><td class="suboffers-second-col"><strong>' + data['component'].pressureRead + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Segment type</td><td class="suboffers-second-col"><strong>' + data['component'].componentType + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours project</td><td class="suboffers-second-col"><strong>' + data['component'].WHP + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours service</td><td class="suboffers-second-col"><strong>' + data['component'].WHS + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Req. start service</td><td class="suboffers-second-col"><strong>' + (data['component'].requestedStartService == '0000-00-00' ? "" : data['component'].requestedStartService) + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Req. end service</td><td class="suboffers-second-col"><strong>' + (data['component'].requestedEndService == '0000-00-00' ? "" : data['component'].requestedEndService) + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Segment</td><td class="suboffers-second-col"><strong>' + data['component'].segmentRead + '</strong></td></tr></table></tr></table>' +
                '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Components:</h4></td><td style="border:0;"></td></tr></table>';
            for (var i = 0; i < data['subcomponents'].length; i++) {
                rtn += '<br><strong>' + data['subcomponents'][i].scope + '</strong><br><table class="control-table" style="border:0px;width:100%">' +
                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?> '<tr class="suboffers-row"><td class="suboffers-first-col">QV</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].OfV + '</strong></td></tr>' +
                        '<tr class="suboffers-row"><td class="suboffers-first-col">QV CM%</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].CMp + '</strong></td></tr>' +
                    <?php } ?> '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Technical</h4></td><td style="border:0;"></td></tr></table>' +
                    '<table class="control-table" style="border:0px;width:100%">' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Static material</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].stat + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Protection layer</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].prot + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Medium</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].medium + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">DN</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].DN + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Volume</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].m3 + ' m<sup>3</sup></strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Weight</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].kg + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Pressure</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].pressure + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours project</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].WHP + '</strong></td></tr></table></tr></table>';
            }
            return rtn + "<hr>";
        }

        function formatO(d, id, tech) {
            data = JSON.parse(tech);
            console.log(data);
            var rtn = '';
            pos = data['component'].position + "-rev/" + data['component'].revision;
            rtn += '<br><strong>' + data['component'].scope + '</strong><br><table class="control-table" style="border:0px;width:100%">' +
                <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?> '<tr class="suboffers-row"><td class="suboffers-first-col">OV</td><td class="suboffers-second-col"><strong>' + data['component'].orderValue + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">OV CM%</td><td class="suboffers-second-col"><strong>' + data['component'].ORVCM + '</strong></td></tr>' +
                <?php } ?> '<tr class="suboffers-row"><td class="suboffers-first-col">Position</td><td class="suboffers-second-col"><strong>' + pos + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Sales article</td><td class="suboffers-second-col"><strong>' + data['component'].scope + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Calc. person</td><td class="suboffers-second-col"><strong>' + data['component'].calcPersonFull + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Project man.</td><td class="suboffers-second-col"><strong>' + data['component'].pmFull + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Status</td><td class="suboffers-second-col"><strong>' + data['component'].AX + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Reason</td><td class="suboffers-second-col"><strong>' + data['component'].reason + '</strong></td></tr>' +
                '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Technical</h4></td><td style="border:0;"></td></tr></table>' +
                '<table class="control-table" style="border:0px;width:100%">' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Static material</td><td class="suboffers-second-col"><strong>' + data['component'].stat + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Protection layer</td><td class="suboffers-second-col"><strong>' + data['component'].prot + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Medium</td><td class="suboffers-second-col"><strong>' + data['component'].medium + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">DN</td><td class="suboffers-second-col"><strong>' + data['component'].DN + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Volume</td><td class="suboffers-second-col"><strong>' + data['component'].m3 + ' m<sup>3</sup></strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Weight</td><td class="suboffers-second-col"><strong>' + data['component'].kg + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Pressure</td><td class="suboffers-second-col"><strong>' + data['component'].pressureRead + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Segment type</td><td class="suboffers-second-col"><strong>' + data['component'].componentType + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours project</td><td class="suboffers-second-col"><strong>' + data['component'].WHP + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours service</td><td class="suboffers-second-col"><strong>' + data['component'].WHS + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Req. start service</td><td class="suboffers-second-col"><strong>' + (data['component'].requestedStartService == '0000-00-00' ? "" : data['component'].requestedStartService) + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Req. end service</td><td class="suboffers-second-col"><strong>' + (data['component'].requestedEndService == '0000-00-00' ? "" : data['component'].requestedEndService) + '</strong></td></tr>' +
                '<tr class="suboffers-row"><td class="suboffers-first-col">Segment</td><td class="suboffers-second-col"><strong>' + data['component'].segmentRead + '</strong></td></tr></table></tr></table>' +
                '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Components:</h4></td><td style="border:0;"></td></tr></table>';
            for (var i = 0; i < data['subcomponents'].length; i++) {
                rtn += '<br><strong>' + data['subcomponents'][i].scope + '</strong><br><table class="control-table" style="border:0px;width:100%">' +
                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?> '<tr class="suboffers-row"><td class="suboffers-first-col">OV</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].OfV + '</strong></td></tr>' +
                        '<tr class="suboffers-row"><td class="suboffers-first-col">OV CM%</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].CMp + '</strong></td></tr>' +
                    <?php } ?> '<table class="control-table" border="0"><tr><td style="border:0;"><h4 style="margin-top: .5rem;">Technical</h4></td><td style="border:0;"></td></tr></table>' +
                    '<table class="control-table" style="border:0px;width:100%">' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Static material</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].stat + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Protection layer</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].prot + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Medium</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].medium + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">DN</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].DN + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Volume</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].m3 + ' m<sup>3</sup></strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Weight</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].kg + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Pressure</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].pressure + '</strong></td></tr>' +
                    '<tr class="suboffers-row"><td class="suboffers-first-col">Workhours project</td><td class="suboffers-second-col"><strong>' + data['subcomponents'][i].WHP + '</strong></td></tr></table></tr></table>';
            }
            return rtn + "<hr>";
        }

        function formatNote(d, id, note) {
            note = note.split("[-]");
            var rtn = '';
            rtn += '<table class="control-table" style="border:0px;width:100%">' +
                '<tr class="suboffers-row" style="border:0;"><td class="text-left">Contact with ' + note[1] + '</td></tr>' +
                '<tr class="suboffers-row"><td class="text-left" style="display:inline-block !important;word-wrap: break-word;overflow-wrap: break-word;">' + note[0] + '</td></tr></table>';
            return rtn;
        }

        function formatComponents(d, id, tech) {
            data = tech.split("[-]");
            var rtn = '';
            return rtn;
        }

        $(document).ready(function() {


            $('#inquiryModal').on('hide.bs.modal', function(e) {
                if ($('#inquiryModal .modal-body .alert-success').length) {
                    $('#inquiryModal .modal-body .alert-success').remove()
                }
            });

            function reloadComments(id) {
                $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=getComments&id=' + id + "&case=offer",
                    success: function(comments) {
                        const hasAlert = $('.comments-list-box').find('.alert');
                        let html = comments;
                        if (hasAlert.length) {
                            html = hasAlert[0].outerHTML + comments;
                        }
                        $(".comments-list-box").html(html);
                    }
                });
            }

            reloadComments(<?= $offer['id']; ?>);
            var saveComment = function(data) {

                // Convert pings to human readable format
                $(Object.keys(data.pings)).each(function(index, userId) {
                    var fullname = data.pings[userId];
                    var pingText = '@' + fullname;
                    data.content = data.content.replace(new RegExp('@' + userId, 'g'), pingText);
                });

                return data;
            }

            $(document).on('click', '.submit-del-comment', function() {
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');

                $('.comments-list-box').find('.alert').remove();

                $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=deleteComment&id=' + id,
                    success: function(res) {
                        if (res.status == "ok") {
                            setTimeout(function() {
                                $('.comments-list-box').prepend("<div class='alert alert-success' style='display: flex;'><div style='width:97%;' class='text-center'><strong>Comment deleted</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().remove();' style='cursor:pointer'></i></div></div>");
                            }, 300);
                            reloadComments(offerId);
                        }
                    }
                });
            });

            $(document).on('click', '.comment-del', function() {
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');

                $('#delCommentModal').modal('show');
                $('#delCommentModal').find('.modal-footer').find('.submit-del-comment').attr('data-id', id);
                $('#delCommentModal').find('.modal-footer').find('.submit-del-comment').attr('data-offer_id', offerId);
            });

            const getCommentContent = async (id) => {
                return $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=getCommentContent&id=' + id,
                });
            }

            const editComment = async (id, content) => {
                return $.ajax({
                    type: 'post',
                    url: 'assets/php/ajaxHandeler.php?action=editComment',
                    data: {
                        id: id,
                        content: content
                    }
                });
            }

            $(document).on('click', '.comment-edit', async function() {
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');

                const contentData = await getCommentContent(id);

                if (contentData.status == "ok") {
                    $('#editCommentModal').find('.modal-body').find('textarea').val(contentData.content);
                    $('#editCommentModal').modal('show');
                    $('#editCommentModal').find('.modal-footer').find('.save-edit-comment').attr('data-id', id);
                    $('#editCommentModal').find('.modal-footer').find('.save-edit-comment').attr('data-offer_id', offerId);
                }
            });

            $(document).on('click', '#editCommentModal .save-edit-comment', async function() {
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');
                const content = $('#editCommentModal').find('.modal-body').find('textarea').val();

                $('#editCommentModal').find('.modal-body').find('.alert').remove();
                $('#editCommentModal').find('.modal-body textarea').removeClass('borderRed');

                if (content.trim().length < 1) {
                    $('#editCommentModal').find('.modal-body textarea').addClass('borderRed');
                    $('#editCommentModal').find('.modal-body').prepend('<div class="alert alert-danger">Comment is empty.</div>');
                    return;
                }

                $('.comments-list-box').find('.alert').remove();

                const res = await editComment(id, content);

                if (res.status == "ok") {
                    $('#editCommentModal').modal('hide');
                    setTimeout(function() {
                        $('.comments-list-box').prepend("<div class='alert alert-success' style='display: flex;'><div style='width:97%;' class='text-center'><strong>Comment edited</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().remove();' style='cursor:pointer'></i></div></div>");
                    }, 300);
                    reloadComments(offerId);
                }
            });

            $('#commentsBox').comments({
                currentUserId: <?= $_SESSION['plasticonDigitalUser']['id']; ?>,
                roundProfilePictures: false,
                textareaRows: 1,
                enableAttachments: false,
                enableHashtags: false,
                enablePinging: true,
                scrollContainer: $(window),
                searchUsers: function(term, success, error) {
                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php?action=getUsersArray&user=' + term,
                        success: function(userArray) {
                            var arr = JSON.parse(userArray);
                            success(arr)
                        },
                        error: error
                    });
                },
                postComment: function(data, success, error) {
                    var users = [];
                    for (var key in data.pings) {
                        users.push(key);
                    }
                    users = users.join(";");

                    $('.comments-list-box').find('.alert').remove();

                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php?action=insertComment&content=' + encodeURIComponent(data.content) + '&userId=' + users + '&creator=' + <?= $_SESSION['plasticonDigitalUser']['id']; ?> + '&caseId=' + <?= $offer['id']; ?> + '&caseName=offer',
                        success: function(response) {
                            success(saveComment(data));
                            setTimeout(function() {
                                $('.comments-list-box').prepend("<div class='alert alert-success' style='display: flex;'><div style='width:97%;' class='text-center'><strong>Comment added</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().remove();' style='cursor:pointer'></i></div></div>");
                            }, 300);
                            reloadComments(<?= $offer['id']; ?>);
                        },
                        error: error
                    });
                }
            });
            $('.collapse-header').click(function() {
                var it = $(this).children().first();
                var next = $(this).next();
                if (!$(next).hasClass('show'))
                    $(it).delay(100).addClass('open');
                else
                    $(it).delay(100).removeClass('open');
            })
            var step = <?= $offer['step'] ?>;
            for (var i = 0; i <= step; i++)

                $('.step' + (i)).addClass('step-active-new');
            $('#offerClient').DataTable({
                "paging": false,
                "ordering": false,
                "info": false,
                "searching": false
            });
            var table = $('#suboffers').DataTable({
                "iDisplayLength": 50,
                "language": {
                    "emptyTable": " ",
                    "infoEmpty": " ",
                    "zeroRecords": " "
                },
                "infoCallback": function(settings, start, end, max, total, pre) {
                    var api = this.api();
                    var pageInfo = api.page.info();
                    var records = pageInfo.recordsDisplay;
                    if (records == 0)
                        $("#suboffers").children().first().children().first().children().last().html("");
                    else
                        $("#suboffers").children().first().children().first().children().last().html("k&euro;");
                },
                "paging": false,
                "ordering": false,
                "searching": false,
                stateSave: true,
                "stateDuration": 0,
                "processing": true,
                "serverSide": true,
                "columnDefs": [{
                    "targets": [0],
                    "createdCell": function(td, cellData, rowData, row, col) {
                        $(td).html("");
                    }
                }],
                'ajax': {
                    'url': 'assets/php/suboffersProcessing.php',
                    type: "POST",
                    "data": {
                        "offerNo": "<?= substr($offer['offerNo'], 0, 8); ?>",
                    },
                },
                'columns': [{
                        className: 'details-control',
                        data: 'exp',
                        name: 'exp'
                    },
                    {
                        data: 'scope',
                        name: 'scope',
                        className: 'text-left'
                    },
                    {
                        className: "text-center",
                        data: 'productionLocation',
                        name: 'productionLocation'
                    },
                    {
                        className: "text-right w20",
                        data: 'OV',
                        name: 'OV'
                    },
                ],
            });
            var subOrders = $('#suboffersOrdered').DataTable({
                "iDisplayLength": 50,
                "language": {
                    "emptyTable": " ",
                    "infoEmpty": " ",
                    "zeroRecords": " "
                },
                "infoCallback": function(settings, start, end, max, total, pre) {
                    var api = this.api();
                    var pageInfo = api.page.info();
                    var records = pageInfo.recordsDisplay;
                    if (records == 0)
                        $("#suboffersOrdered").children().first().children().first().children().last().html("");
                    else
                        $("#suboffersOrdered").children().first().children().first().children().last().html("k&euro;");
                },
                "paging": false,
                "ordering": false,
                "searching": false,
                stateSave: true,
                "stateDuration": 0,
                "processing": true,
                "serverSide": true,
                "columnDefs": [{
                    "targets": [0],
                    "createdCell": function(td, cellData, rowData, row, col) {
                        $(td).html("");
                    }
                }],
                'ajax': {
                    'url': 'assets/php/suboffersOrdersProcessing.php',
                    type: "POST",
                    "data": {
                        "offerNo": "<?= substr($offer['offerNo'], 0, 8); ?>",
                    },
                },
                'columns': [{
                        className: 'details-control',
                        data: 'exp',
                        name: 'exp'
                    },
                    {
                        data: 'scope',
                        name: 'scope',
                        className: 'text-left'
                    },
                    {
                        className: "text-center",
                        data: 'productionLocation',
                        name: 'productionLocation'
                    },
                    {
                        className: "text-right w20",
                        data: 'OV',
                        name: 'OV'
                    },
                ],
            });
            var tableCmp = $('#components').DataTable({
                "iDisplayLength": 15,
                stateSave: true,
                "stateDuration": 0,
                "columnDefs": [
                    //{"orderable": false, "targets": 24},
                    {
                        "orderable": false,
                        "targets": [0, 3, 4, 5, 6, 7, 8, 9, 10]
                    },
                    {
                        "targets": [1, 2, 3, 4, 5, 6, 7],
                        "createdCell": function(td, cellData, rowData, row, col) {
                            data = cellData.split("[-]");
                            $(td).addClass('pointer').attr("title", "Click to edit").attr("onclick", "(location.href='offerInfo.php?id=" + data[1] + "')");
                            $(td).html(data[0]);
                        }
                    }
                ],
                "order": [
                    [1, "desc"]
                ],
                "processing": true,
                "serverSide": true,
                "searching": false,
                "pageLength": 15,
                "lengthMenu": [15, 25, 50, 100],
                'ajax': {
                    'url': 'assets/php/componentsProcessing.php',
                    type: "POST",
                    "data": {
                        "offerNo": "<?= substr($offer['offerNo'], 0, 8); ?>",
                    },
                },
                dom: "<'row'<'col-sm-3 addDeleteClass'l><'col-sm-3'f><'col-sm-2'><'col-sm-1 pagePerPage'><'col-sm-3'p>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                'columns': [{
                        className: 'revClass',
                        data: 'check',
                        name: 'check'
                    },
                    {
                        data: 'position',
                        name: 'position'
                    },
                    {
                        data: 'scope',
                        name: 'scope'
                    },
                    {
                        data: 'segment',
                        name: 'segment'
                    },
                    {
                        data: 'calcPerson',
                        name: 'calcPerson'
                    },
                    {
                        className: 'revClass',
                        data: 'productionLocation',
                        name: 'productionLocation'
                    },
                    {
                        className: 'revClass',
                        data: 'tech',
                        name: 'tech'
                    },
                    {
                        className: 'revClass',
                        data: 'comercial',
                        name: 'comercial'
                    },
                    //{ className: 'revClass', data: 'edit', name: 'edit' },
                    {
                        className: 'revClass',
                        data: 'delComp',
                        name: 'delComp'
                    },
                    {
                        className: 'revClass',
                        data: 'revision',
                        name: 'revision'
                    },
                    {
                        className: 'revClass',
                        data: 'copy',
                        name: 'copy'
                    },
                ],
                "drawCallback": function() {
                    $('.countCheck').click(function() {
                        var id = $(this).attr('data-id');
                        var val = 0;
                        if ($(this).prop('checked'))
                            val = 1;
                        $.ajax({
                            url: "assets/php/ajaxHandeler.php",
                            method: "get",
                            data: {
                                action: 'changeCount',
                                value: val,
                                id: id
                            }
                        })
                    })
                    <?php if ($_SESSION['plasticonDigitalUser']['id'] != $offer['V'] && $_SESSION['plasticonDigitalUser']['id'] != $offer['oID'] && $_SESSION['plasticonDigitalUser']['id'] != $client['tl'] && $client['tl'] !== null) { ?>
                        //$('.countCheck').prop('disabled', true); 
                        //$('.addCalcPerson').prop('disabled', true); 
                        //$('#addComponent').prop('disabled', true); 
                        //$('.delBtn').prop('disabled', true);  
                        //$('.revIco').attr('onclick', "").attr('data-toggle', "").attr('data-target', "").css('cursor', "context-menu").css("color","grey"); 
                    <?php } ?>
                },
            });
            var tableCon = $('#conclusion').DataTable({
                "pageLength": 100,
                // "lengthMenu": [100, 150],
                paging: false,
                stateSave: true,
                searching: false,
                "stateDuration": 0,
                "columnDefs": [
                    //{"orderable": false, "targets": 24},
                    {
                        "orderable": false,
                        "targets": [1, 2, 3]
                    },
                ],
                "order": [
                    [0, "desc"]
                ],
                'columns': [{
                        className: 'tableS-50',
                        data: 'position',
                        name: 'position'
                    },
                    {
                        className: 'tableSB',
                        data: 'scope',
                        name: 'scope'
                    },
                    {
                        className: 'tableB',
                        data: 'orderNo',
                        name: 'orderNo'
                    },
                    {
                        className: 'revClass',
                        data: 'ax',
                        name: 'ax'
                    },
                    {
                        className: 'revClass',
                        data: 'reason',
                        name: 'reason'
                    },
                ],
            });
            $('#suboffers tbody').on('click', 'td.details-control', function() {
                var tr = $(this).closest('tr');
                var row = table.row(tr);

                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    tr.removeClass('shown');
                } else {
                    // Open this row
                    xmlhttp = new XMLHttpRequest();
                    xmlhttp.onreadystatechange = function() {
                        if (this.readyState == 4 && this.status == 200) {
                            var data = this.responseText;
                            row.child(format(data[0], row.data().exp, data)).show();
                        }
                    };
                    xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getCalculations&value=" + row.data().exp, true);
                    xmlhttp.send();
                    tr.addClass('shown');
                }
            });
            $('#suboffersOrdered tbody').on('click', 'td.details-control', function() {
                var tr = $(this).closest('tr');
                var row = subOrders.row(tr);

                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    tr.removeClass('shown');
                } else {
                    // Open this row
                    xmlhttp = new XMLHttpRequest();
                    xmlhttp.onreadystatechange = function() {
                        if (this.readyState == 4 && this.status == 200) {
                            var data = this.responseText;
                            row.child(formatO(data[0], row.data().exp, data)).show();
                        }
                    };
                    xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getCalculations&value=" + row.data().exp, true);
                    xmlhttp.send();
                    tr.addClass('shown');
                }
            });

            $('.modal').find('.select2').each(function() {
                $(this).select2({
                    dropdownParent: $(this).closest('.modal-content'),
                });
            });

            $(".select2-reseller").select2({
                allowClear: true,
                placeholder: 'Select',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getClients'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            $(".select2-endclient").select2({
                allowClear: true,
                placeholder: 'Select',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getClients'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            var conTable = $('#contacts').DataTable({
                "iDisplayLength": 50,
                "language": {
                    "emptyTable": " ",
                    "infoEmpty": " ",
                    "zeroRecords": " "
                },
                "paging": false,
                "ordering": false,
                "info": false,
                "searching": false,
                "processing": true,
                "serverSide": true,
                "columnDefs": [{
                    "targets": [0],
                    "createdCell": function(td, cellData, rowData, row, col) {
                        $(td).html("");
                    }
                }],
                'ajax': {
                    'url': 'assets/php/contactsProcessing.php',
                    type: "POST",
                    "data": {
                        "offerId": "<?= $offer['id']; ?>",
                    },
                },
                'columns': [{
                        className: 'details-control',
                        data: 'exp',
                        name: 'exp'
                    },
                    {
                        data: 'contact',
                        name: 'contact',
                        className: 'text-left'
                    },
                    {
                        data: 'contactDate',
                        name: 'contactDate'
                    },
                ],
            });
            $('#contacts tbody').on('click', 'td.details-control', function() {
                var tr = $(this).closest('tr');
                var row = conTable.row(tr);
                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    tr.removeClass('shown');
                } else {
                    // Open this row
                    xmlhttp = new XMLHttpRequest();
                    xmlhttp.onreadystatechange = function() {
                        if (this.readyState == 4 && this.status == 200) {
                            var data = this.responseText;
                            row.child(formatNote(data[0], row.data().exp, data)).show();
                        }
                    };
                    xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getNote&value=" + row.data().exp, true);
                    xmlhttp.send();
                    tr.addClass('shown');
                }
            });

            var goodExtFile = ["pdf", "PDF", ""];
            $('#fileInput').change(function() {
                if ($('#fileInput').val() != '') {
                    var ext = $('#fileInput').val();
                    ext = ext.split('.').pop();
                    if (goodExtFile.includes(ext)) {
                        $('#fileInput').addClass('nowe');
                    } else {
                        $('#fileInput').addClass('istnieje');
                        $('#formatFile').removeClass('hidden');
                    }
                    $('#removeFile').removeClass('hidden-opacity');
                }
            });
            $('.AXselect').change(function() {
                var orders = 0;
                var ax = $(this).val();
                $('.AXselect').each(function() {
                    if ($(this).val() == "Order")
                        orders++;
                })
                if (orders != 0)
                    $('#OOPM').prop('required', true);
                else
                    $('#OOPM').prop('required', false);
                if (ax != "Order" && ax != '') {
                    if (ax == "Lost")
                        $(this).parent().next().children().first().html("<option value='' selected disabled>Select</option><option value='Delivery time'>Delivery time</option><option value='Price'>Price</option><option value='Proposal capacity'>Proposal capacity</option><option value='Technology'>Technology</option><option value='Other'>Other</option>");
                    else
                        $(this).parent().next().children().first().html("<option value='' selected disabled>Select</option><option value='Material'>Material</option><option value='Investment terminated'>Investment terminated</option><option value='Client lost order'>Client lost order</option><option value='Forwarded to cluster company'>Forwarded to cluster company</option><option value='Other'>Other</option>");
                    $(this).parent().next().children().first().prop('disabled', false).prop('required', true);
                } else
                    $(this).parent().next().children().first().prop('disabled', true).prop('required', false).val("").trigger("change");
            })
            $('#lostAX').change(function() {
                var ax = $(this).val();
                if (ax != "Order") {
                    if (ax == "Lost")
                        $('#lostReason').html("<option value='' selected disabled>Select</option><option value='Delivery time'>Delivery time</option><option value='Price'>Price</option><option value='Proposal capacity'>Proposal capacity</option><option value='Technology'>Technology</option><option value='Other'>Other</option>");
                    else
                        $('#lostReason').html("<option value='' selected disabled>Select</option><option value='Material'>Material</option><option value='Investment terminated'>Investment terminated</option><option value='Client lost order'>Client lost order</option><option value='Forwarded to cluster company'>Forwarded to cluster company</option><option value='Other'>Other</option>");
                    $('#lostReason').prop('disabled', false).prop('required', true);
                } else
                    $('#lostReason').prop('disabled', true).prop('required', false).val("").trigger("change");
            })
            $('#removeFile').click(function() {
                $('#removeFile').addClass('hidden-opacity');
                $('#formatFile').addClass('hidden');
                $('#fileInput').val('').removeClass('nowe').removeClass('istnieje');
                $('#fileText').html('');
            });
            $('#fileInputAdd').change(function() {
                if ($('#fileInputAdd').val() != '') {
                    var ext = $('#fileInputAdd').val();
                    ext = ext.split('.').pop();
                    if (goodExtFile.includes(ext)) {
                        $('#fileInputAdd').addClass('nowe');
                    } else {
                        $('#fileInputAdd').addClass('istnieje');
                        $('#formatFileAdd').removeClass('hidden');
                    }
                    $('#removeFileAdd').removeClass('hidden-opacity');
                }
            });
            $('#removeFileAdd').click(function() {
                $('#removeFileAdd').addClass('hidden-opacity');
                $('#formatFileAdd').addClass('hidden');
                $('#fileInputAdd').val('').removeClass('nowe').removeClass('istnieje');
                $('#fileTextAdd').html('');
            });
            $('#addComponent').click(function() {
                $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: 'addComponent',
                            value: $('#addComponent').attr('offerNo')
                        }
                    })
                    .done(function(res) {
                        tableCmp.ajax.reload();
                        //$('[name="saveOffer"]').prop("disabled", true);
                        //$('[name="saveOffer"]').attr("title", "Fill all technical and comercial data!");
                    });
            })
            $('#saveScope').click(function() {
                $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: 'addScope',
                            cmp: $('#cmpIdScope').val(),
                            scope: $('#cmpScope').val(),
                            segment: $('#cmpSegment').val(),
                            person: $('#calcPersonSelect').val(),
                            production: $('#cmpProduction').val()
                        }
                    })
                    .done(function(res) {
                        tableCmp.ajax.reload();
                    });
            })
            $('#saveComercial').click(function() {
                $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: 'saveComercial',
                            id: $('#addCOmercialOVID').val(),
                            ov: $('#comercialOVvalue').val(),
                            cm: $('#comercialCM').val()
                        }
                    })
                    .done(function(res) {
                        if (res == 1) {
                            $('#saveOffer').prop('disabled', false);
                        }
                        tableCmp.ajax.reload();
                    });
            })
            $('#delThisComp').click(function() {
                $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: 'delComponent',
                            value: $('#delThisComp').attr('data-id')
                        }
                    })
                    .done(function(res) {
                        tableCmp.ajax.reload();
                        $.ajax({
                                url: "assets/php/ajaxHandeler.php",
                                method: "get",
                                data: {
                                    action: 'saveOfferButton',
                                    value: <?= $offer['offerNo']; ?>
                                }
                            })
                            .done(function(res) {
                                if (res == 0) {
                                    $('[name="saveOffer"]').prop("disabled", true);
                                    $('[name="saveOffer"]').attr("title", "Fill all technical and comercial data!");
                                } else {
                                    $('[name="saveOffer"]').prop("disabled", false);
                                    $('[name="saveOffer"]').attr("title", "");
                                }
                            });
                    });
            })
            $('#saveCopy').click(function() {
                $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: 'copyComponent',
                            id: $('#copyCmpId').val()
                        }
                    })
                    .done(function(res) {
                        tableCmp.ajax.reload();
                        $.ajax({
                                url: "assets/php/ajaxHandeler.php",
                                method: "get",
                                data: {
                                    action: 'saveOfferButton',
                                    value: <?= $offer['offerNo']; ?>
                                }
                            })
                            .done(function(res) {
                                $('#copyPosition').modal('hide');
                                if (res == 0) {
                                    $('[name="saveOffer"]').prop("disabled", true);
                                    $('[name="saveOffer"]').attr("title", "Fill all technical and comercial data!");
                                } else {
                                    $('[name="saveOffer"]').prop("disabled", false);
                                    $('[name="saveOffer"]').attr("title", "");
                                }
                            });
                    });
            })

            function daysInMonth(year, month) {
                return new Date(year, month + 1, 0).getDate();
            }
            Date.prototype.addMonths = function(m) {
                var d = new Date(this);
                var years = Math.floor(m / 12);
                var months = m - (years * 12);
                if (years)
                    d.setFullYear(d.getFullYear() + years);
                if (months)
                    d.setMonth(d.getMonth() + months);
                var days = d.getDate() + 1;
                var md = daysInMonth(d.getFullYear(), d.getMonth());
                if (md < days)
                    d.setDate(md);
                return d;
            }
            $('.addMonths').click(function() {
                var amount = $(this).attr("amount");
                //var data=$('[name="foNext"]').val(); DODAJE DO AKTUALNEJ Z POLA
                var data = '<?= date("Y-m-d"); ?>';
                data = data.split("-");
                data = new Date(data[0], data[1] - 1, data[2]);
                data = data.addMonths(amount);
                var m = data.getUTCMonth() + 1; //months from 1-12
                var d = data.getUTCDate();
                var y = data.getUTCFullYear();
                if (m <= 9)
                    m = 0 + "" + m;
                if (d <= 9)
                    d = 0 + "" + d;
                $('[name="foNext"]').val(y + "-" + m + "-" + d);
                $('[name="acNext"]').val(y + "-" + m + "-" + d);
            })
            var h = 0;
            $('.countHeight').each(function() {
                h += $(this).outerHeight() - 20;
            })
            $('[name="foOrderDate"]').change(function() {
                var date1 = new Date("'" + $(this).val() + "'").setHours(0, 0, 0, 0);
                var dateNow = new Date();
                dateNow = dateNow.setHours(0, 0, 0, 0);
                if (date1 < dateNow)
                    $('#datePast').html("WARNING! Date is in the past!");
                else
                    $('#datePast').html("");
            })
            $('#includeContact').click(function() {
                if ($(this).prop('checked') == true)
                    $('.incCont').prop('required', true).prop('disabled', false);
                else
                    $('.incCont').prop('required', false).prop('disabled', true);
            })
            $('.AXselect').change(function() {
                var order = 0;
                $(".AXselect").each(function() {
                    if ($(this).val() == "Order")
                        order = 1;
                })
                if (order != 0)
                    $('[name="OOOrderDate"]').prop('required', true);
                else
                    $('[name="OOOrderDate"]').prop('required', false);
            })
            $('.notes').css('height', (h - 52) + "px");
            $('[name="OT"]').val("<?= $offer['OT']; ?>").trigger("change");
            <?php if ($client['clientLongName'] != "") { ?>
                $('[name="client"]').html("<option selected value='" + <?= $client['id']; ?> + "'><?= $client['clientLongName']; ?></option>").trigger("change");
                $('[name="inqClient"]').html("<option selected value='" + <?= $client['id']; ?> + "'><?= $client['clientLongName']; ?></option>").trigger("change");
                $('[name="clientEx"]').html("<option selected value='" + <?= $client['id']; ?> + "'><?= $client['clientLongName']; ?></option>").trigger("change");
            <?php
            }
            if ($endclient['clientLongName'] != '') {
            ?>
                $('[name="inqClientEnd"]').html("<option selected value='" + <?= $endclient['id']; ?> + "'><?= $endclient['clientLongName']; ?></option>").trigger("change");
                $('[name="endClient"]').html("<option selected value='" + <?= $endclient['id']; ?> + "'><?= $endclient['clientLongName']; ?></option>").trigger("change");
            <?php } ?>
            $('[name="clientLocation"]').val("<?= $client['city']; ?>").trigger("change").prop("disabled", false);
            $('[name="orderLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="staffOrderLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="productionLocation"]').val("<?= $offer['productionLocation']; ?>").trigger("change");
            $('[name="staffProductionLocation"]').val("<?= $offer['productionLocation']; ?>").trigger("change");
            $('[name="segment"]').val("<?= $offer['segment']; ?>").trigger("change");
            $('[name="AX"]').val("<?= $offer['AX']; ?>").trigger("change");
            $('[name="reason"]').val("<?= $offer['reason']; ?>").trigger("change");
            $('[name="oID"]').val("<?= $offer['oID']; ?>").trigger("change");
            $('[name="V"]').val("<?= $offer['V']; ?>").trigger("change");
            $('[name="clientLocationEx"]').val("<?= $client['city']; ?>").trigger("change").prop("disabled", false);
            $('[name="IDEx"]').val("<?= $offer['oID']; ?>").trigger("change");
            $('[name="VEx"]').val("<?= $offer['V']; ?>").trigger("change");
            $('[name="OTEx"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="staffOfferType"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="plantLocationCountry"]').val("<?= $offer['plantLocationCountry']; ?>").trigger("change");
            $('[name="stat"]').val("<?= $offer['stat']; ?>").trigger("change");
            $('[name="prot"]').val("<?= $offer['prot']; ?>").trigger("change");
            $('[name="componentType"]').val("<?= $offer['componentType']; ?>").trigger("change");
            $('[name="pressure"]').val("<?= $offer['pressure']; ?>").trigger("change");
            $('[name="BT"]').val("<?= $offer['BT']; ?>").trigger("change");
            $('[name="foProductionLocation"]').val("<?= $offer['productionLocation']; ?>").trigger("change");
            $('[name="foOrderLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="foPlantLocationCountry"]').val("<?= $offer['plantLocationCountry']; ?>").trigger("change");
            $('[name="foSegment"]').val("<?= $offer['segment']; ?>").trigger("change");
            $('[name="foBT"]').val("<?= $offer['BT']; ?>").trigger("change");
            $('[name="fillOrderLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="fillSegment"]').val("<?= $offer['segment']; ?>").trigger("change");
            $('[name="fillPlantLocationCountry"]').val("<?= $offer['plantLocationCountry']; ?>").trigger("change");
            $('[name="fillProductionLocation"]').val("<?= $offer['productionLocation']; ?>").trigger("change");
            $('[name="fillBT"]').val("<?= $offer['BT']; ?>").trigger("change");
            $('[name="fillOT"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="ooOrderLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="ooProductionLocation"]').val("<?= $offer['productionLocation']; ?>").trigger("change");
            $('[name="ooOT"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="foOT"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="loseAX"]').val("<?= $offer['AX']; ?>").trigger("change");
            $('[name="lostAXNEW"]').val("<?= $offer['AX']; ?>").trigger("change");
            $('[name="lostReasonNEW"]').val("<?= $offer['reason']; ?>").trigger("change");
            $('[name="plantCountryOffer"]').val("<?= $offer['plantLocationCountry']; ?>").trigger("change");
            $('[name="concProductionLocation"]').val("<?= $offer['orderLocation']; ?>").trigger("change");
            $('[name="stage"]').val("<?= $offer['step']; ?>").trigger("change");
            $('[name="staffProject"]').val("<?= $offer['projectId']; ?>").trigger("change");
            $('[name="staffProductionReservation"]').val("<?= $offer['productionReservation']; ?>").trigger("change");
            $('[name="staffSalesCmp"]').val("<?= $offer['company']; ?>").trigger("change");
            $('[name="datesV"]').val("<?= $offer['V']; ?>").trigger("change");
            $('[name="datesID"]').val("<?= $offer['oID']; ?>").trigger("change");
            $('[name="datesF"]').val("<?= $offer['F']; ?>").trigger("change");
            $('[name="incoterms"]').val("<?= $offer['incoterms']; ?>").trigger("change");
            $('[name="delIncoterms"]').val("<?= $offer['incoterms']; ?>").trigger("change");
            $('[name="inqOT"]').val("<?= $offer['OT']; ?>").trigger("change");
            $('[name="inqIS"]').val("<?= $offer['oID']; ?>").trigger("change");
            $('[name="inqR"]').val("<?= $offer['V']; ?>").trigger("change");
            $('[name="inqFollowUp"]').val("<?= $offer['F']; ?>").trigger("change");
            $('[name="inqProject"]').val("<?= $offer['projectId']; ?>").trigger("change");
            $('[name="inqCompany"]').val("<?= $offer['company']; ?>").trigger("change");
            $('[name="inqPM"]').val("<?= $offer['pm']; ?>").trigger("change");
            $('[name="foProdReserv"]').val("<?= $offer['productionReservation']; ?>").trigger("change");
            $('[name="OOPM"]').val("<?= $offer['pm']; ?>").trigger("change");
            $('[name="OOOrderCompany"]').val("<?= $offer['orderCompany']; ?>").trigger("change");
            $('[name="fFollowUp"]').val("<?= $offer['F']; ?>").trigger("change");
            $('[name="foContactWith"]').val("<?= $offer['contactWith']; ?>").trigger("change");
            $('[name="fnFollowUp"]').val("<?= $offer['F']; ?>").trigger("change");
            <?php
            if (isset($_SESSION['offerFormData']['ProdRes'])) {
            ?>
                $('[name="offProdRes"]').val("<?= $_SESSION['offerFormData']['ProdRes']; ?>").trigger("change");
            <?php
            } else {
            ?>
                $('[name="offProdRes"]').val("<?= $offer['productionReservation']; ?>").trigger("change");
            <?php
            }
            if (isset($_SESSION['offerFormData']['competitor']))
                foreach (explode(",", $_SESSION['offerFormData']['competitor']) as $competitor) {
                    echo '$("#competitorsSelect option[value=' . "'" . $competitor . "'" . ']").prop("selected", true).trigger("change");';
                    echo '$("#offCompetitorsSelect option[value=' . "'" . $competitor . "'" . ']").prop("selected", true).trigger("change");';
                }
            else
                foreach (explode(";", $offer['competitor']) as $competitor) {
                    echo '$("#competitorsSelect option[value=' . "'" . $competitor . "'" . ']").prop("selected", true).trigger("change");';
                    echo '$("#offCompetitorsSelect option[value=' . "'" . $competitor . "'" . ']").prop("selected", true).trigger("change");';
                }
            ?>
            $('[name="endClientLocation"]').val("<?= $endclient['city']; ?>").trigger("change").prop('disabled', false);

        });

        function checkKiloEuro(x) {
            if (x.value >= 500) {
                let confirmation = confirm("Are you sure? Remember that this value should be expressed in k€");
                if (confirmation) {
                    x.blur();
                } else {
                    x.value = 0;
                    x.select();
                }
            }
        }


        function saveContact() {
            $('#addContact').modal('hide');
            var gender = $('#contactGender').val();
            var name = $('#contactName').val();
            var surname = $('#contactSurname').val();
            var email = $('#contactEmail').val();
            var phone1 = $('#contactPhone1').val();
            var phone2 = $('#contactPhone2').val();
            var position = $('#contactPosition').val();
            var id = $('#inqClientLocation').val();
            var ifEnd = $('#ifend').val();
            if (ifEnd == 1) {
                id = $('#inqEndClientLocation').val();
            }
            xmlhttp = new XMLHttpRequest();
            xmlhttp.onreadystatechange = function() {
                if (this.readyState == 4 && this.status == 200) {
                    var response = this.responseText.split("[-]");
                    if (ifEnd == 0) {
                        $('#contactPurchase').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                        $('#contactTechnican').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                    } else {
                        $('#contactPurchaseEnd').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                        $('#contactTechnicanEnd').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                    }

                    if ($('#inquiryModal .modal-body .alert-success').length == 0) {
                        $('#inquiryModal').find('.modal-body').prepend("<div><br><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>Contact has been added!</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>");
                    }

                    $('#inquiryModal').modal('show');
                }
                $('#contactName').val("");
                $('#contactSurname').val("");
                $('#contactEmail').val("");
                $('#contactPhone1').val("");
                $('#contactPhone2').val("");
                $('#contactPosition').val("");
            };
            xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=saveContact&gender=" + gender + "&name=" + name + "&surname=" + surname + "&email=" + email + "&phone1=" + phone1 + "&phone2=" + phone2 + "&position=" + position + "&id=" + id, true);
            xmlhttp.send();
            if (ifEnd == 0) {
                getContacts($('#inqClientLocation').val(), $('#inqContactPurchase'), $('#inqContactTechnican'), 0);
            } else {
                getContacts($('#inqEndClientLocation').val(), $('#inqContactPurchaseEnd'), $('#inqContactTechnicanEnd'), 1);
            }
        }
    </script>