﻿<?php
/*
 * INCLUDE CONFIG
 */
if (!defined('DS')) {
    define('DS', DIRECTORY_SEPARATOR);
}
if (!defined('APP_ROOT')) {
    define('APP_ROOT', __DIR__ . DS . ".." . DS . ".." . DS . ".." . DS);
}
require(APP_ROOT . "db-config.php");
/*
 * LOAD AutoLoad
 */
require(APP_ROOT . 'const.php');
require_once(APP_ROOT . 'AutoLoad.php');
// NOBO
require_once('nobo-functions.php');
// --NOBO 

function connect()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_CRM;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectGeneraldata()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_GENERALDATA;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectOMS()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_OMS;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectUsers()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_USERS;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

function connectMilestones()
{
    $host = DB_HOST;
    $db_user = DB_USER;
    $db_password = DB_PW;
    $db_name = DB_MILESTONES;
    $db_port = DB_PORT;
    $link = new mysqli($host, $db_user, $db_password, $db_name, $db_port);
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $link->query('SET GLOBAL sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    $link->query('SET SESSION sql_mode = "ALLOW_INVALID_DATES,NO_ENGINE_SUBSTITUTION"');
    return $link;
}

//FUNCTIONS

function bez_pl($string)
{
    $polskie = array(' - ', 'ę', 'Ę', 'ó', 'Ó', 'Ą', 'ą', 'Ś', 's', 'ł', 'Ł', 'ż', 'Ż', 'Ź', 'ź', 'ć', 'Ć', 'ń', 'Ń', '-', "'", "/", "?", '"', ":", 'ś', '!', '&', '&', '#', ';', '[', ']', 'domena.pl', '(', ')', '`', '%', '”', '„', '…');
    $miedzyn = array('-', 'e', 'e', 'o', 'o', 'a', 'a', 's', 's', 'l', 'l', 'z', 'z', 'z', 'z', 'c', 'c', 'n', 'n', '-', "", "", "", "", "", 's', '', '', '', '', '', '', '', '', '', '', '', '', '');
    $string = str_replace($polskie, $miedzyn, $string);
    return $string;
}

function cleanStr($txt)
{
    $transliterationTable = array('á' => 'a', 'Á' => 'A', 'à' => 'a', 'À' => 'A', 'ă' => 'a', 'Ă' => 'A', 'â' => 'a', 'Â' => 'A', 'å' => 'a', 'Å' => 'A', 'ã' => 'a', 'Ã' => 'A', 'ą' => 'a', 'Ą' => 'A', 'ā' => 'a', 'Ā' => 'A', 'ä' => 'ae', 'Ä' => 'Ae', 'æ' => 'ae', 'Æ' => 'AE', 'ḃ' => 'b', 'Ḃ' => 'B', 'ć' => 'c', 'Ć' => 'C', 'ĉ' => 'c', 'Ĉ' => 'C', 'č' => 'c', 'Č' => 'C', 'ċ' => 'c', 'Ċ' => 'C', 'ç' => 'c', 'Ç' => 'C', 'ď' => 'd', 'Ď' => 'D', 'ḋ' => 'd', 'Ḋ' => 'D', 'đ' => 'd', 'Đ' => 'D', 'ð' => 'dh', 'Ð' => 'Dh', 'é' => 'e', 'É' => 'E', 'è' => 'e', 'È' => 'E', 'ĕ' => 'e', 'Ĕ' => 'E', 'ê' => 'e', 'Ê' => 'E', 'ě' => 'e', 'Ě' => 'E', 'ë' => 'e', 'Ë' => 'E', 'ė' => 'e', 'Ė' => 'E', 'ę' => 'e', 'Ę' => 'E', 'ē' => 'e', 'Ē' => 'E', 'ḟ' => 'f', 'Ḟ' => 'F', 'ƒ' => 'f', 'Ƒ' => 'F', 'ğ' => 'g', 'Ğ' => 'G', 'ĝ' => 'g', 'Ĝ' => 'G', 'ġ' => 'g', 'Ġ' => 'G', 'ģ' => 'g', 'Ģ' => 'G', 'ĥ' => 'h', 'Ĥ' => 'H', 'ħ' => 'h', 'Ħ' => 'H', 'í' => 'i', 'Í' => 'I', 'ì' => 'i', 'Ì' => 'I', 'î' => 'i', 'Î' => 'I', 'ï' => 'i', 'Ï' => 'I', 'ĩ' => 'i', 'Ĩ' => 'I', 'į' => 'i', 'Į' => 'I', 'ī' => 'i', 'Ī' => 'I', 'ĵ' => 'j', 'Ĵ' => 'J', 'ķ' => 'k', 'Ķ' => 'K', 'ĺ' => 'l', 'Ĺ' => 'L', 'ľ' => 'l', 'Ľ' => 'L', 'ļ' => 'l', 'Ļ' => 'L', 'ł' => 'l', 'Ł' => 'L', 'ṁ' => 'm', 'Ṁ' => 'M', 'ń' => 'n', 'Ń' => 'N', 'ň' => 'n', 'Ň' => 'N', 'ñ' => 'n', 'Ñ' => 'N', 'ņ' => 'n', 'Ņ' => 'N', 'ó' => 'o', 'Ó' => 'O', 'ò' => 'o', 'Ò' => 'O', 'ô' => 'o', 'Ô' => 'O', 'ő' => 'o', 'Ő' => 'O', 'õ' => 'o', 'Õ' => 'O', 'ø' => 'oe', 'Ø' => 'OE', 'ō' => 'o', 'Ō' => 'O', 'ơ' => 'o', 'Ơ' => 'O', 'ö' => 'oe', 'Ö' => 'OE', 'ṗ' => 'p', 'Ṗ' => 'P', 'ŕ' => 'r', 'Ŕ' => 'R', 'ř' => 'r', 'Ř' => 'R', 'ŗ' => 'r', 'Ŗ' => 'R', 'ś' => 's', 'Ś' => 'S', 'ŝ' => 's', 'Ŝ' => 'S', 'š' => 's', 'Š' => 'S', 'ṡ' => 's', 'Ṡ' => 'S', 'ş' => 's', 'Ş' => 'S', 'ș' => 's', 'Ș' => 'S', 'ß' => 'SS', 'ť' => 't', 'Ť' => 'T', 'ṫ' => 't', 'Ṫ' => 'T', 'ţ' => 't', 'Ţ' => 'T', 'ț' => 't', 'Ț' => 'T', 'ŧ' => 't', 'Ŧ' => 'T', 'ú' => 'u', 'Ú' => 'U', 'ù' => 'u', 'Ù' => 'U', 'ŭ' => 'u', 'Ŭ' => 'U', 'û' => 'u', 'Û' => 'U', 'ů' => 'u', 'Ů' => 'U', 'ű' => 'u', 'Ű' => 'U', 'ũ' => 'u', 'Ũ' => 'U', 'ų' => 'u', 'Ų' => 'U', 'ū' => 'u', 'Ū' => 'U', 'ư' => 'u', 'Ư' => 'U', 'ü' => 'ue', 'Ü' => 'UE', 'ẃ' => 'w', 'Ẃ' => 'W', 'ẁ' => 'w', 'Ẁ' => 'W', 'ŵ' => 'w', 'Ŵ' => 'W', 'ẅ' => 'w', 'Ẅ' => 'W', 'ý' => 'y', 'Ý' => 'Y', 'ỳ' => 'y', 'Ỳ' => 'Y', 'ŷ' => 'y', 'Ŷ' => 'Y', 'ÿ' => 'y', 'Ÿ' => 'Y', 'ź' => 'z', 'Ź' => 'Z', 'ž' => 'z', 'Ž' => 'Z', 'ż' => 'z', 'Ż' => 'Z', 'þ' => 'th', 'Þ' => 'Th', 'µ' => 'u', 'а' => 'a', 'А' => 'a', 'б' => 'b', 'Б' => 'b', 'в' => 'v', 'В' => 'v', 'г' => 'g', 'Г' => 'g', 'д' => 'd', 'Д' => 'd', 'е' => 'e', 'Е' => 'E', 'ё' => 'e', 'Ё' => 'E', 'ж' => 'zh', 'Ж' => 'zh', 'з' => 'z', 'З' => 'z', 'и' => 'i', 'И' => 'i', 'й' => 'j', 'Й' => 'j', 'к' => 'k', 'К' => 'k', 'л' => 'l', 'Л' => 'l', 'м' => 'm', 'М' => 'm', 'н' => 'n', 'Н' => 'n', 'о' => 'o', 'О' => 'o', 'п' => 'p', 'П' => 'p', 'р' => 'r', 'Р' => 'r', 'с' => 's', 'С' => 's', 'т' => 't', 'Т' => 't', 'у' => 'u', 'У' => 'u', 'ф' => 'f', 'Ф' => 'f', 'х' => 'h', 'Х' => 'h', 'ц' => 'c', 'Ц' => 'c', 'ч' => 'ch', 'Ч' => 'ch', 'ш' => 'sh', 'Ш' => 'sh', 'щ' => 'sch', 'Щ' => 'sch', 'ъ' => '', 'Ъ' => '', 'ы' => 'y', 'Ы' => 'y', 'ь' => '', 'Ь' => '', 'э' => 'e', 'Э' => 'e', 'ю' => 'ju', 'Ю' => 'ju', 'я' => 'ja', 'Я' => 'ja', '>' => 'Larger', '<' => 'Smaller', '.' => ' ', '"' => ' ', "'" => ' ', "/" => '-', "|" => '', ":" => '', "%" => '');
    return str_replace(array_keys($transliterationTable), array_values($transliterationTable), $txt);
}

function ifEmailExists($mail)
{
    $link = connect();
    $result = $link->query("SELECT * FROM users WHERE email='$mail'");
    $link->close();
    if ($result->num_rows != 0)
        return true;
    return false;
}

function alertDanger($t)
{
    echo "<div><div class='row alert-danger justify-content-center'><div style='width:97%;' class='text-center'><strong>$t</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>";
}

function selectThisEndclient($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $row = $result->fetch_object();
    if ($row) {
        echo "<option value='" . ($row->clientLongName) . "' selected>" . ($row->clientLongName) . "</option>";
    }
    return false;
}

function selectThisReseller($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $row = $result->fetch_object();
    if ($row) {
        echo "<option value='" . ($row->clientLongName) . "' selected>" . ($row->clientLongName) . "</option>";
    }
}

function alertSuccess($t)
{
    echo "<div><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>$t</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>";
}

function inicialy($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();

    if (!empty($row->imie)) {
        $encoding = 'UTF-8';
        $initials = mb_substr($row->imie, 0, 1, $encoding) . mb_substr($row->nazwisko, 0, 1, $encoding);
        $initials = mb_strtoupper($initials, $encoding);
        return $initials;
    }

    return "";
}

function getUserAvatar($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    $av = $row->avatar;
    if (empty($av))
        return "assets/images/avatars/admin.png";
    else
        return INTRANET_LINK . "/assets/upload/" . $av;
}

function getNameAndSurname($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return ($row->imie) . " " . ($row->nazwisko);
    return false;
}

function getClientLocation($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->city;
    return false;
}

function getUserName($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->imie;
    return false;
}

function getUserSurname($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->nazwisko;
    return false;
}

function getUserEmail($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    return $row->email;
}

function getUserCompany($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    return $row->company;
}

function getClientName($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row) {
        if ($row->clientLongName != "")
            return $row->clientLongName;
        else
            return $row->clientShortName;
    }
    return false;
}

function getClientShortName($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    if ($row)
        return $row->clientShortName;
    return false;
}

function getClientCity($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $link->close();
    $row = $result->fetch_object();
    return $row->city;
}

function imageResize($imageResourceId, $width, $height)
{
    $targetWidth = 60;
    $targetHeight = 60;
    $targetLayer = imagecreatetruecolor($targetWidth, $targetHeight);
    imagecopyresampled($targetLayer, $imageResourceId, 0, 0, 0, 0, $targetWidth, $targetHeight, $width, $height);
    return $targetLayer;
}

function checkPw($pw, $mail)
{
    $link = connectUsers();
    $query = sprintf(
        "SELECT * FROM users WHERE password='%s' AND email='%s'",
        mysqli_real_escape_string($link, strip_tags($pw)),
        mysqli_real_escape_string($link, strip_tags($mail))
    );
    $result = $link->query($query);
    if ($result->num_rows == 1)
        return true;
    else
        return false;
}

function listCountries()
{
    echo '
									<option value="" disabled selected>Select</option>
								   <option value="Afghanistan">Afghanistan</option>
								   <option value="Albania">Albania</option>
								   <option value="Algeria">Algeria</option>
								   <option value="American Samoa">American Samoa</option>
								   <option value="Andorra">Andorra</option>
								   <option value="Angola">Angola</option>
								   <option value="Anguilla">Anguilla</option>
								   <option value="Antigua and Barbuda">Antigua & Barbuda</option>
								   <option value="Argentina">Argentina</option>
								   <option value="Armenia">Armenia</option>
								   <option value="Aruba">Aruba</option>
								   <option value="Australia">Australia</option>
								   <option value="Austria">Austria</option>
								   <option value="Azerbaijan">Azerbaijan</option>
								   <option value="Bahamas">Bahamas</option>
								   <option value="Bahrain">Bahrain</option>
								   <option value="Bangladesh">Bangladesh</option>
								   <option value="Barbados">Barbados</option>
								   <option value="Belarus">Belarus</option>
								   <option value="Belgium">Belgium</option>
								   <option value="Belize">Belize</option>
								   <option value="Benin">Benin</option>
								   <option value="Bermuda">Bermuda</option>
								   <option value="Bhutan">Bhutan</option>
								   <option value="Bolivia">Bolivia</option>
								   <option value="Bonaire">Bonaire</option>
								   <option value="Bosnia & Herzegovina">Bosnia & Herzegovina</option>
								   <option value="Botswana">Botswana</option>
								   <option value="Brazil">Brazil</option>
								   <option value="British Indian Ocean Ter">British Indian Ocean Ter</option>
								   <option value="Brunei">Brunei</option>
								   <option value="Bulgaria">Bulgaria</option>
								   <option value="Burkina Faso">Burkina Faso</option>
								   <option value="Burundi">Burundi</option>
								   <option value="Cambodia">Cambodia</option>
								   <option value="Cameroon">Cameroon</option>
								   <option value="Canada">Canada</option>
								   <option value="Canary Islands">Canary Islands</option>
								   <option value="Cape Verde">Cape Verde</option>
								   <option value="Cayman Islands">Cayman Islands</option>
								   <option value="Central African Republic">Central African Republic</option>
								   <option value="Chad">Chad</option>
								   <option value="Channel Islands">Channel Islands</option>
								   <option value="Chile">Chile</option>
								   <option value="China">China</option>
								   <option value="Christmas Island">Christmas Island</option>
								   <option value="Cocos Island">Cocos Island</option>
								   <option value="Colombia">Colombia</option>
								   <option value="Comoros">Comoros</option>
								   <option value="Congo">Congo</option>
								   <option value="Cook Islands">Cook Islands</option>
								   <option value="Costa Rica">Costa Rica</option>
								   <option value="Cote DIvoire">Cote DIvoire</option>
								   <option value="Croatia">Croatia</option>
								   <option value="Cuba">Cuba</option>
								   <option value="Curaco">Curacao</option>
								   <option value="Cyprus">Cyprus</option>
								   <option value="Czech Republic">Czech Republic</option>
								   <option value="Denmark">Denmark</option>
								   <option value="Djibouti">Djibouti</option>
								   <option value="Dominica">Dominica</option>
								   <option value="Dominican Republic">Dominican Republic</option>
								   <option value="East Timor">East Timor</option>
								   <option value="Ecuador">Ecuador</option>
								   <option value="Egypt">Egypt</option>
								   <option value="El Salvador">El Salvador</option>
								   <option value="Equatorial Guinea">Equatorial Guinea</option>
								   <option value="Eritrea">Eritrea</option>
								   <option value="Estonia">Estonia</option>
								   <option value="Ethiopia">Ethiopia</option>
								   <option value="Falkland Islands">Falkland Islands</option>
								   <option value="Faroe Islands">Faroe Islands</option>
								   <option value="Fiji">Fiji</option>
								   <option value="Finland">Finland</option>
								   <option value="France">France</option>
								   <option value="French Guiana">French Guiana</option>
								   <option value="French Polynesia">French Polynesia</option>
								   <option value="French Southern Ter">French Southern Ter</option>
								   <option value="Gabon">Gabon</option>
								   <option value="Gambia">Gambia</option>
								   <option value="Georgia">Georgia</option>
								   <option value="Germany">Germany</option>
								   <option value="Ghana">Ghana</option>
								   <option value="Gibraltar">Gibraltar</option>
								   <option value="Great Britain">Great Britain</option>
								   <option value="Greece">Greece</option>
								   <option value="Greenland">Greenland</option>
								   <option value="Grenada">Grenada</option>
								   <option value="Guadeloupe">Guadeloupe</option>
								   <option value="Guam">Guam</option>
								   <option value="Guatemala">Guatemala</option>
								   <option value="Guinea">Guinea</option>
								   <option value="Guyana">Guyana</option>
								   <option value="Haiti">Haiti</option>
								   <option value="Hawaii">Hawaii</option>
								   <option value="Honduras">Honduras</option>
								   <option value="Hong Kong">Hong Kong</option>
								   <option value="Hungary">Hungary</option>
								   <option value="Iceland">Iceland</option>
								   <option value="Indonesia">Indonesia</option>
								   <option value="India">India</option>
								   <option value="Iran">Iran</option>
								   <option value="Iraq">Iraq</option>
								   <option value="Ireland">Ireland</option>
								   <option value="Isle of Man">Isle of Man</option>
								   <option value="Israel">Israel</option>
								   <option value="Italy">Italy</option>
								   <option value="Jamaica">Jamaica</option>
								   <option value="Japan">Japan</option>
								   <option value="Jordan">Jordan</option>
								   <option value="Kazakhstan">Kazakhstan</option>
								   <option value="Kenya">Kenya</option>
								   <option value="Kiribati">Kiribati</option>
								   <option value="Korea North">Korea North</option>
								   <option value="Korea South">Korea South</option>
								   <option value="Kuwait">Kuwait</option>
								   <option value="Kyrgyzstan">Kyrgyzstan</option>
								   <option value="Laos">Laos</option>
								   <option value="Latvia">Latvia</option>
								   <option value="Lebanon">Lebanon</option>
								   <option value="Lesotho">Lesotho</option>
								   <option value="Liberia">Liberia</option>
								   <option value="Libya">Libya</option>
								   <option value="Liechtenstein">Liechtenstein</option>
								   <option value="Lithuania">Lithuania</option>
								   <option value="Luxembourg">Luxembourg</option>
								   <option value="Macau">Macau</option>
								   <option value="Macedonia">Macedonia</option>
								   <option value="Madagascar">Madagascar</option>
								   <option value="Malaysia">Malaysia</option>
								   <option value="Malawi">Malawi</option>
								   <option value="Maldives">Maldives</option>
								   <option value="Mali">Mali</option>
								   <option value="Malta">Malta</option>
								   <option value="Marshall Islands">Marshall Islands</option>
								   <option value="Martinique">Martinique</option>
								   <option value="Mauritania">Mauritania</option>
								   <option value="Mauritius">Mauritius</option>
								   <option value="Mayotte">Mayotte</option>
								   <option value="Mexico">Mexico</option>
								   <option value="Midway Islands">Midway Islands</option>
								   <option value="Moldova">Moldova</option>
								   <option value="Monaco">Monaco</option>
								   <option value="Mongolia">Mongolia</option>
								   <option value="Montserrat">Montserrat</option>
								   <option value="Morocco">Morocco</option>
								   <option value="Mozambique">Mozambique</option>
								   <option value="Myanmar">Myanmar</option>
								   <option value="Nambia">Nambia</option>
								   <option value="Nauru">Nauru</option>
								   <option value="Nepal">Nepal</option>
								   <option value="Netherlands">Netherlands</option>
								   <option value="Nevis">Nevis</option>
								   <option value="New Caledonia">New Caledonia</option>
								   <option value="New Zealand">New Zealand</option>
								   <option value="Nicaragua">Nicaragua</option>
								   <option value="Niger">Niger</option>
								   <option value="Nigeria">Nigeria</option>
								   <option value="Niue">Niue</option>
								   <option value="Norfolk Island">Norfolk Island</option>
								   <option value="Norway">Norway</option>
								   <option value="Oman">Oman</option>
								   <option value="Pakistan">Pakistan</option>
								   <option value="Palau Island">Palau Island</option>
								   <option value="Palestine">Palestine</option>
								   <option value="Panama">Panama</option>
								   <option value="Papua New Guinea">Papua New Guinea</option>
								   <option value="Paraguay">Paraguay</option>
								   <option value="Peru">Peru</option>
								   <option value="Phillipines">Philippines</option>
								   <option value="Pitcairn Island">Pitcairn Island</option>
								   <option value="Poland">Poland</option>
								   <option value="Portugal">Portugal</option>
								   <option value="Puerto Rico">Puerto Rico</option>
								   <option value="Qatar">Qatar</option>
								   <option value="Republic of Montenegro">Republic of Montenegro</option>
								   <option value="Republic of Serbia">Republic of Serbia</option>
								   <option value="Reunion">Reunion</option>
								   <option value="Romania">Romania</option>
								   <option value="Russia">Russia</option>
								   <option value="Rwanda">Rwanda</option>
								   <option value="St Barthelemy">St Barthelemy</option>
								   <option value="St Eustatius">St Eustatius</option>
								   <option value="St Helena">St Helena</option>
								   <option value="St Kitts-Nevis">St Kitts-Nevis</option>
								   <option value="St Lucia">St Lucia</option>
								   <option value="St Maarten">St Maarten</option>
								   <option value="St Pierre & Miquelon">St Pierre & Miquelon</option>
								   <option value="St Vincent & Grenadines">St Vincent & Grenadines</option>
								   <option value="Saipan">Saipan</option>
								   <option value="Samoa">Samoa</option>
								   <option value="Samoa American">Samoa American</option>
								   <option value="San Marino">San Marino</option>
								   <option value="Sao Tome & Principe">Sao Tome & Principe</option>
								   <option value="Saudi Arabia">Saudi Arabia</option>
								   <option value="Senegal">Senegal</option>
								   <option value="Seychelles">Seychelles</option>
								   <option value="Sierra Leone">Sierra Leone</option>
								   <option value="Singapore">Singapore</option>
								   <option value="Slovakia">Slovakia</option>
								   <option value="Slovenia">Slovenia</option>
								   <option value="Solomon Islands">Solomon Islands</option>
								   <option value="Somalia">Somalia</option>
								   <option value="South Africa">South Africa</option>
								   <option value="Spain">Spain</option>
								   <option value="Sri Lanka">Sri Lanka</option>
								   <option value="Sudan">Sudan</option>
								   <option value="Suriname">Suriname</option>
								   <option value="Swaziland">Swaziland</option>
								   <option value="Sweden">Sweden</option>
								   <option value="Switzerland">Switzerland</option>
								   <option value="Syria">Syria</option>
								   <option value="Tahiti">Tahiti</option>
								   <option value="Taiwan">Taiwan</option>
								   <option value="Tajikistan">Tajikistan</option>
								   <option value="Tanzania">Tanzania</option>
								   <option value="Thailand">Thailand</option>
								   <option value="Togo">Togo</option>
								   <option value="Tokelau">Tokelau</option>
								   <option value="Tonga">Tonga</option>
								   <option value="Trinidad & Tobago">Trinidad & Tobago</option>
								   <option value="Tunisia">Tunisia</option>
								   <option value="Turkey">Turkey</option>
								   <option value="Turkmenistan">Turkmenistan</option>
								   <option value="Turks & Caicos Is">Turks & Caicos Is</option>
								   <option value="Tuvalu">Tuvalu</option>
								   <option value="Uganda">Uganda</option>
								   <option value="United Kingdom">United Kingdom</option>
								   <option value="Ukraine">Ukraine</option>
								   <option value="United Arab Emirates">United Arab Emirates</option>
								   <option value="United States of America">United States of America</option>
								   <option value="Uraguay">Uruguay</option>
								   <option value="Uzbekistan">Uzbekistan</option>
								   <option value="Vanuatu">Vanuatu</option>
								   <option value="Vatican City State">Vatican City State</option>
								   <option value="Venezuela">Venezuela</option>
								   <option value="Vietnam">Vietnam</option>
								   <option value="Virgin Islands (Brit)">Virgin Islands (Brit)</option>
								   <option value="Virgin Islands (USA)">Virgin Islands (USA)</option>
								   <option value="Wake Island">Wake Island</option>
								   <option value="Wallis & Futana Is">Wallis & Futana Is</option>
								   <option value="Yemen">Yemen</option>
								   <option value="Zaire">Zaire</option>
								   <option value="Zambia">Zambia</option>
								   <option value="Zimbabwe">Zimbabwe</option>
	';
}

function listCountriesFilters()
{
    echo '
								   <option value="Afghanistan">Afghanistan</option>
								   <option value="Albania">Albania</option>
								   <option value="Algeria">Algeria</option>
								   <option value="American Samoa">American Samoa</option>
								   <option value="Andorra">Andorra</option>
								   <option value="Angola">Angola</option>
								   <option value="Anguilla">Anguilla</option>
								   <option value="Antigua and Barbuda">Antigua & Barbuda</option>
								   <option value="Argentina">Argentina</option>
								   <option value="Armenia">Armenia</option>
								   <option value="Aruba">Aruba</option>
								   <option value="Australia">Australia</option>
								   <option value="Austria">Austria</option>
								   <option value="Azerbaijan">Azerbaijan</option>
								   <option value="Bahamas">Bahamas</option>
								   <option value="Bahrain">Bahrain</option>
								   <option value="Bangladesh">Bangladesh</option>
								   <option value="Barbados">Barbados</option>
								   <option value="Belarus">Belarus</option>
								   <option value="Belgium">Belgium</option>
								   <option value="Belize">Belize</option>
								   <option value="Benin">Benin</option>
								   <option value="Bermuda">Bermuda</option>
								   <option value="Bhutan">Bhutan</option>
								   <option value="Bolivia">Bolivia</option>
								   <option value="Bonaire">Bonaire</option>
								   <option value="Bosnia & Herzegovina">Bosnia & Herzegovina</option>
								   <option value="Botswana">Botswana</option>
								   <option value="Brazil">Brazil</option>
								   <option value="British Indian Ocean Ter">British Indian Ocean Ter</option>
								   <option value="Brunei">Brunei</option>
								   <option value="Bulgaria">Bulgaria</option>
								   <option value="Burkina Faso">Burkina Faso</option>
								   <option value="Burundi">Burundi</option>
								   <option value="Cambodia">Cambodia</option>
								   <option value="Cameroon">Cameroon</option>
								   <option value="Canada">Canada</option>
								   <option value="Canary Islands">Canary Islands</option>
								   <option value="Cape Verde">Cape Verde</option>
								   <option value="Cayman Islands">Cayman Islands</option>
								   <option value="Central African Republic">Central African Republic</option>
								   <option value="Chad">Chad</option>
								   <option value="Channel Islands">Channel Islands</option>
								   <option value="Chile">Chile</option>
								   <option value="China">China</option>
								   <option value="Christmas Island">Christmas Island</option>
								   <option value="Cocos Island">Cocos Island</option>
								   <option value="Colombia">Colombia</option>
								   <option value="Comoros">Comoros</option>
								   <option value="Congo">Congo</option>
								   <option value="Cook Islands">Cook Islands</option>
								   <option value="Costa Rica">Costa Rica</option>
								   <option value="Cote DIvoire">Cote DIvoire</option>
								   <option value="Croatia">Croatia</option>
								   <option value="Cuba">Cuba</option>
								   <option value="Curaco">Curacao</option>
								   <option value="Cyprus">Cyprus</option>
								   <option value="Czech Republic">Czech Republic</option>
								   <option value="Denmark">Denmark</option>
								   <option value="Djibouti">Djibouti</option>
								   <option value="Dominica">Dominica</option>
								   <option value="Dominican Republic">Dominican Republic</option>
								   <option value="East Timor">East Timor</option>
								   <option value="Ecuador">Ecuador</option>
								   <option value="Egypt">Egypt</option>
								   <option value="El Salvador">El Salvador</option>
								   <option value="Equatorial Guinea">Equatorial Guinea</option>
								   <option value="Eritrea">Eritrea</option>
								   <option value="Estonia">Estonia</option>
								   <option value="Ethiopia">Ethiopia</option>
								   <option value="Falkland Islands">Falkland Islands</option>
								   <option value="Faroe Islands">Faroe Islands</option>
								   <option value="Fiji">Fiji</option>
								   <option value="Finland">Finland</option>
								   <option value="France">France</option>
								   <option value="French Guiana">French Guiana</option>
								   <option value="French Polynesia">French Polynesia</option>
								   <option value="French Southern Ter">French Southern Ter</option>
								   <option value="Gabon">Gabon</option>
								   <option value="Gambia">Gambia</option>
								   <option value="Georgia">Georgia</option>
								   <option value="Germany">Germany</option>
								   <option value="Ghana">Ghana</option>
								   <option value="Gibraltar">Gibraltar</option>
								   <option value="Great Britain">Great Britain</option>
								   <option value="Greece">Greece</option>
								   <option value="Greenland">Greenland</option>
								   <option value="Grenada">Grenada</option>
								   <option value="Guadeloupe">Guadeloupe</option>
								   <option value="Guam">Guam</option>
								   <option value="Guatemala">Guatemala</option>
								   <option value="Guinea">Guinea</option>
								   <option value="Guyana">Guyana</option>
								   <option value="Haiti">Haiti</option>
								   <option value="Hawaii">Hawaii</option>
								   <option value="Honduras">Honduras</option>
								   <option value="Hong Kong">Hong Kong</option>
								   <option value="Hungary">Hungary</option>
								   <option value="Iceland">Iceland</option>
								   <option value="Indonesia">Indonesia</option>
								   <option value="India">India</option>
								   <option value="Iran">Iran</option>
								   <option value="Iraq">Iraq</option>
								   <option value="Ireland">Ireland</option>
								   <option value="Isle of Man">Isle of Man</option>
								   <option value="Israel">Israel</option>
								   <option value="Italy">Italy</option>
								   <option value="Jamaica">Jamaica</option>
								   <option value="Japan">Japan</option>
								   <option value="Jordan">Jordan</option>
								   <option value="Kazakhstan">Kazakhstan</option>
								   <option value="Kenya">Kenya</option>
								   <option value="Kiribati">Kiribati</option>
								   <option value="Korea North">Korea North</option>
								   <option value="Korea South">Korea South</option>
								   <option value="Kuwait">Kuwait</option>
								   <option value="Kyrgyzstan">Kyrgyzstan</option>
								   <option value="Laos">Laos</option>
								   <option value="Latvia">Latvia</option>
								   <option value="Lebanon">Lebanon</option>
								   <option value="Lesotho">Lesotho</option>
								   <option value="Liberia">Liberia</option>
								   <option value="Libya">Libya</option>
								   <option value="Liechtenstein">Liechtenstein</option>
								   <option value="Lithuania">Lithuania</option>
								   <option value="Luxembourg">Luxembourg</option>
								   <option value="Macau">Macau</option>
								   <option value="Macedonia">Macedonia</option>
								   <option value="Madagascar">Madagascar</option>
								   <option value="Malaysia">Malaysia</option>
								   <option value="Malawi">Malawi</option>
								   <option value="Maldives">Maldives</option>
								   <option value="Mali">Mali</option>
								   <option value="Malta">Malta</option>
								   <option value="Marshall Islands">Marshall Islands</option>
								   <option value="Martinique">Martinique</option>
								   <option value="Mauritania">Mauritania</option>
								   <option value="Mauritius">Mauritius</option>
								   <option value="Mayotte">Mayotte</option>
								   <option value="Mexico">Mexico</option>
								   <option value="Midway Islands">Midway Islands</option>
								   <option value="Moldova">Moldova</option>
								   <option value="Monaco">Monaco</option>
								   <option value="Mongolia">Mongolia</option>
								   <option value="Montserrat">Montserrat</option>
								   <option value="Morocco">Morocco</option>
								   <option value="Mozambique">Mozambique</option>
								   <option value="Myanmar">Myanmar</option>
								   <option value="Nambia">Nambia</option>
								   <option value="Nauru">Nauru</option>
								   <option value="Nepal">Nepal</option>
								   <option value="Netherlands">Netherlands</option>
								   <option value="Nevis">Nevis</option>
								   <option value="New Caledonia">New Caledonia</option>
								   <option value="New Zealand">New Zealand</option>
								   <option value="Nicaragua">Nicaragua</option>
								   <option value="Niger">Niger</option>
								   <option value="Nigeria">Nigeria</option>
								   <option value="Niue">Niue</option>
								   <option value="Norfolk Island">Norfolk Island</option>
								   <option value="Norway">Norway</option>
								   <option value="Oman">Oman</option>
								   <option value="Pakistan">Pakistan</option>
								   <option value="Palau Island">Palau Island</option>
								   <option value="Palestine">Palestine</option>
								   <option value="Panama">Panama</option>
								   <option value="Papua New Guinea">Papua New Guinea</option>
								   <option value="Paraguay">Paraguay</option>
								   <option value="Peru">Peru</option>
								   <option value="Phillipines">Philippines</option>
								   <option value="Pitcairn Island">Pitcairn Island</option>
								   <option value="Poland">Poland</option>
								   <option value="Portugal">Portugal</option>
								   <option value="Puerto Rico">Puerto Rico</option>
								   <option value="Qatar">Qatar</option>
								   <option value="Republic of Montenegro">Republic of Montenegro</option>
								   <option value="Republic of Serbia">Republic of Serbia</option>
								   <option value="Reunion">Reunion</option>
								   <option value="Romania">Romania</option>
								   <option value="Russia">Russia</option>
								   <option value="Rwanda">Rwanda</option>
								   <option value="St Barthelemy">St Barthelemy</option>
								   <option value="St Eustatius">St Eustatius</option>
								   <option value="St Helena">St Helena</option>
								   <option value="St Kitts-Nevis">St Kitts-Nevis</option>
								   <option value="St Lucia">St Lucia</option>
								   <option value="St Maarten">St Maarten</option>
								   <option value="St Pierre & Miquelon">St Pierre & Miquelon</option>
								   <option value="St Vincent & Grenadines">St Vincent & Grenadines</option>
								   <option value="Saipan">Saipan</option>
								   <option value="Samoa">Samoa</option>
								   <option value="Samoa American">Samoa American</option>
								   <option value="San Marino">San Marino</option>
								   <option value="Sao Tome & Principe">Sao Tome & Principe</option>
								   <option value="Saudi Arabia">Saudi Arabia</option>
								   <option value="Senegal">Senegal</option>
								   <option value="Seychelles">Seychelles</option>
								   <option value="Sierra Leone">Sierra Leone</option>
								   <option value="Singapore">Singapore</option>
								   <option value="Slovakia">Slovakia</option>
								   <option value="Slovenia">Slovenia</option>
								   <option value="Solomon Islands">Solomon Islands</option>
								   <option value="Somalia">Somalia</option>
								   <option value="South Africa">South Africa</option>
								   <option value="Spain">Spain</option>
								   <option value="Sri Lanka">Sri Lanka</option>
								   <option value="Sudan">Sudan</option>
								   <option value="Suriname">Suriname</option>
								   <option value="Swaziland">Swaziland</option>
								   <option value="Sweden">Sweden</option>
								   <option value="Switzerland">Switzerland</option>
								   <option value="Syria">Syria</option>
								   <option value="Tahiti">Tahiti</option>
								   <option value="Taiwan">Taiwan</option>
								   <option value="Tajikistan">Tajikistan</option>
								   <option value="Tanzania">Tanzania</option>
								   <option value="Thailand">Thailand</option>
								   <option value="Togo">Togo</option>
								   <option value="Tokelau">Tokelau</option>
								   <option value="Tonga">Tonga</option>
								   <option value="Trinidad & Tobago">Trinidad & Tobago</option>
								   <option value="Tunisia">Tunisia</option>
								   <option value="Turkey">Turkey</option>
								   <option value="Turkmenistan">Turkmenistan</option>
								   <option value="Turks & Caicos Is">Turks & Caicos Is</option>
								   <option value="Tuvalu">Tuvalu</option>
								   <option value="Uganda">Uganda</option>
								   <option value="United Kingdom">United Kingdom</option>
								   <option value="Ukraine">Ukraine</option>
								   <option value="United Arab Emirates">United Arab Emirates</option>
								   <option value="United States of America">United States of America</option>
								   <option value="Uraguay">Uruguay</option>
								   <option value="Uzbekistan">Uzbekistan</option>
								   <option value="Vanuatu">Vanuatu</option>
								   <option value="Vatican City State">Vatican City State</option>
								   <option value="Venezuela">Venezuela</option>
								   <option value="Vietnam">Vietnam</option>
								   <option value="Virgin Islands (Brit)">Virgin Islands (Brit)</option>
								   <option value="Virgin Islands (USA)">Virgin Islands (USA)</option>
								   <option value="Wake Island">Wake Island</option>
								   <option value="Wallis & Futana Is">Wallis & Futana Is</option>
								   <option value="Yemen">Yemen</option>
								   <option value="Zaire">Zaire</option>
								   <option value="Zambia">Zambia</option>
								   <option value="Zimbabwe">Zimbabwe</option>
	';
}

function listCountriesOffer()
{
    echo '
									<option value="" selected>Select country</option>
								   <option value="Afghanistan">Afghanistan</option>
								   <option value="Albania">Albania</option>
								   <option value="Algeria">Algeria</option>
								   <option value="American Samoa">American Samoa</option>
								   <option value="Andorra">Andorra</option>
								   <option value="Angola">Angola</option>
								   <option value="Anguilla">Anguilla</option>
								   <option value="Antigua and Barbuda">Antigua & Barbuda</option>
								   <option value="Argentina">Argentina</option>
								   <option value="Armenia">Armenia</option>
								   <option value="Aruba">Aruba</option>
								   <option value="Australia">Australia</option>
								   <option value="Austria">Austria</option>
								   <option value="Azerbaijan">Azerbaijan</option>
								   <option value="Bahamas">Bahamas</option>
								   <option value="Bahrain">Bahrain</option>
								   <option value="Bangladesh">Bangladesh</option>
								   <option value="Barbados">Barbados</option>
								   <option value="Belarus">Belarus</option>
								   <option value="Belgium">Belgium</option>
								   <option value="Belize">Belize</option>
								   <option value="Benin">Benin</option>
								   <option value="Bermuda">Bermuda</option>
								   <option value="Bhutan">Bhutan</option>
								   <option value="Bolivia">Bolivia</option>
								   <option value="Bonaire">Bonaire</option>
								   <option value="Bosnia & Herzegovina">Bosnia & Herzegovina</option>
								   <option value="Botswana">Botswana</option>
								   <option value="Brazil">Brazil</option>
								   <option value="British Indian Ocean Ter">British Indian Ocean Ter</option>
								   <option value="Brunei">Brunei</option>
								   <option value="Bulgaria">Bulgaria</option>
								   <option value="Burkina Faso">Burkina Faso</option>
								   <option value="Burundi">Burundi</option>
								   <option value="Cambodia">Cambodia</option>
								   <option value="Cameroon">Cameroon</option>
								   <option value="Canada">Canada</option>
								   <option value="Canary Islands">Canary Islands</option>
								   <option value="Cape Verde">Cape Verde</option>
								   <option value="Cayman Islands">Cayman Islands</option>
								   <option value="Central African Republic">Central African Republic</option>
								   <option value="Chad">Chad</option>
								   <option value="Channel Islands">Channel Islands</option>
								   <option value="Chile">Chile</option>
								   <option value="China">China</option>
								   <option value="Christmas Island">Christmas Island</option>
								   <option value="Cocos Island">Cocos Island</option>
								   <option value="Colombia">Colombia</option>
								   <option value="Comoros">Comoros</option>
								   <option value="Congo">Congo</option>
								   <option value="Cook Islands">Cook Islands</option>
								   <option value="Costa Rica">Costa Rica</option>
								   <option value="Cote DIvoire">Cote DIvoire</option>
								   <option value="Croatia">Croatia</option>
								   <option value="Cuba">Cuba</option>
								   <option value="Curaco">Curacao</option>
								   <option value="Cyprus">Cyprus</option>
								   <option value="Czech Republic">Czech Republic</option>
								   <option value="Denmark">Denmark</option>
								   <option value="Djibouti">Djibouti</option>
								   <option value="Dominica">Dominica</option>
								   <option value="Dominican Republic">Dominican Republic</option>
								   <option value="East Timor">East Timor</option>
								   <option value="Ecuador">Ecuador</option>
								   <option value="Egypt">Egypt</option>
								   <option value="El Salvador">El Salvador</option>
								   <option value="Equatorial Guinea">Equatorial Guinea</option>
								   <option value="Eritrea">Eritrea</option>
								   <option value="Estonia">Estonia</option>
								   <option value="Ethiopia">Ethiopia</option>
								   <option value="Falkland Islands">Falkland Islands</option>
								   <option value="Faroe Islands">Faroe Islands</option>
								   <option value="Fiji">Fiji</option>
								   <option value="Finland">Finland</option>
								   <option value="France">France</option>
								   <option value="French Guiana">French Guiana</option>
								   <option value="French Polynesia">French Polynesia</option>
								   <option value="French Southern Ter">French Southern Ter</option>
								   <option value="Gabon">Gabon</option>
								   <option value="Gambia">Gambia</option>
								   <option value="Georgia">Georgia</option>
								   <option value="Germany">Germany</option>
								   <option value="Ghana">Ghana</option>
								   <option value="Gibraltar">Gibraltar</option>
								   <option value="Great Britain">Great Britain</option>
								   <option value="Greece">Greece</option>
								   <option value="Greenland">Greenland</option>
								   <option value="Grenada">Grenada</option>
								   <option value="Guadeloupe">Guadeloupe</option>
								   <option value="Guam">Guam</option>
								   <option value="Guatemala">Guatemala</option>
								   <option value="Guinea">Guinea</option>
								   <option value="Guyana">Guyana</option>
								   <option value="Haiti">Haiti</option>
								   <option value="Hawaii">Hawaii</option>
								   <option value="Honduras">Honduras</option>
								   <option value="Hong Kong">Hong Kong</option>
								   <option value="Hungary">Hungary</option>
								   <option value="Iceland">Iceland</option>
								   <option value="Indonesia">Indonesia</option>
								   <option value="India">India</option>
								   <option value="Iran">Iran</option>
								   <option value="Iraq">Iraq</option>
								   <option value="Ireland">Ireland</option>
								   <option value="Isle of Man">Isle of Man</option>
								   <option value="Israel">Israel</option>
								   <option value="Italy">Italy</option>
								   <option value="Jamaica">Jamaica</option>
								   <option value="Japan">Japan</option>
								   <option value="Jordan">Jordan</option>
								   <option value="Kazakhstan">Kazakhstan</option>
								   <option value="Kenya">Kenya</option>
								   <option value="Kiribati">Kiribati</option>
								   <option value="Korea North">Korea North</option>
								   <option value="Korea South">Korea South</option>
								   <option value="Kuwait">Kuwait</option>
								   <option value="Kyrgyzstan">Kyrgyzstan</option>
								   <option value="Laos">Laos</option>
								   <option value="Latvia">Latvia</option>
								   <option value="Lebanon">Lebanon</option>
								   <option value="Lesotho">Lesotho</option>
								   <option value="Liberia">Liberia</option>
								   <option value="Libya">Libya</option>
								   <option value="Liechtenstein">Liechtenstein</option>
								   <option value="Lithuania">Lithuania</option>
								   <option value="Luxembourg">Luxembourg</option>
								   <option value="Macau">Macau</option>
								   <option value="Macedonia">Macedonia</option>
								   <option value="Madagascar">Madagascar</option>
								   <option value="Malaysia">Malaysia</option>
								   <option value="Malawi">Malawi</option>
								   <option value="Maldives">Maldives</option>
								   <option value="Mali">Mali</option>
								   <option value="Malta">Malta</option>
								   <option value="Marshall Islands">Marshall Islands</option>
								   <option value="Martinique">Martinique</option>
								   <option value="Mauritania">Mauritania</option>
								   <option value="Mauritius">Mauritius</option>
								   <option value="Mayotte">Mayotte</option>
								   <option value="Mexico">Mexico</option>
								   <option value="Midway Islands">Midway Islands</option>
								   <option value="Moldova">Moldova</option>
								   <option value="Monaco">Monaco</option>
								   <option value="Mongolia">Mongolia</option>
								   <option value="Montserrat">Montserrat</option>
								   <option value="Morocco">Morocco</option>
								   <option value="Mozambique">Mozambique</option>
								   <option value="Myanmar">Myanmar</option>
								   <option value="Nambia">Nambia</option>
								   <option value="Nauru">Nauru</option>
								   <option value="Nepal">Nepal</option>
								   <option value="Netherlands">Netherlands</option>
								   <option value="Nevis">Nevis</option>
								   <option value="New Caledonia">New Caledonia</option>
								   <option value="New Zealand">New Zealand</option>
								   <option value="Nicaragua">Nicaragua</option>
								   <option value="Niger">Niger</option>
								   <option value="Nigeria">Nigeria</option>
								   <option value="Niue">Niue</option>
								   <option value="Norfolk Island">Norfolk Island</option>
								   <option value="Norway">Norway</option>
								   <option value="Oman">Oman</option>
								   <option value="Pakistan">Pakistan</option>
								   <option value="Palau Island">Palau Island</option>
								   <option value="Palestine">Palestine</option>
								   <option value="Panama">Panama</option>
								   <option value="Papua New Guinea">Papua New Guinea</option>
								   <option value="Paraguay">Paraguay</option>
								   <option value="Peru">Peru</option>
								   <option value="Phillipines">Philippines</option>
								   <option value="Pitcairn Island">Pitcairn Island</option>
								   <option value="Poland">Poland</option>
								   <option value="Portugal">Portugal</option>
								   <option value="Puerto Rico">Puerto Rico</option>
								   <option value="Qatar">Qatar</option>
								   <option value="Republic of Montenegro">Republic of Montenegro</option>
								   <option value="Republic of Serbia">Republic of Serbia</option>
								   <option value="Reunion">Reunion</option>
								   <option value="Romania">Romania</option>
								   <option value="Russia">Russia</option>
								   <option value="Rwanda">Rwanda</option>
								   <option value="St Barthelemy">St Barthelemy</option>
								   <option value="St Eustatius">St Eustatius</option>
								   <option value="St Helena">St Helena</option>
								   <option value="St Kitts-Nevis">St Kitts-Nevis</option>
								   <option value="St Lucia">St Lucia</option>
								   <option value="St Maarten">St Maarten</option>
								   <option value="St Pierre & Miquelon">St Pierre & Miquelon</option>
								   <option value="St Vincent & Grenadines">St Vincent & Grenadines</option>
								   <option value="Saipan">Saipan</option>
								   <option value="Samoa">Samoa</option>
								   <option value="Samoa American">Samoa American</option>
								   <option value="San Marino">San Marino</option>
								   <option value="Sao Tome & Principe">Sao Tome & Principe</option>
								   <option value="Saudi Arabia">Saudi Arabia</option>
								   <option value="Senegal">Senegal</option>
								   <option value="Seychelles">Seychelles</option>
								   <option value="Sierra Leone">Sierra Leone</option>
								   <option value="Singapore">Singapore</option>
								   <option value="Slovakia">Slovakia</option>
								   <option value="Slovenia">Slovenia</option>
								   <option value="Solomon Islands">Solomon Islands</option>
								   <option value="Somalia">Somalia</option>
								   <option value="South Africa">South Africa</option>
								   <option value="Spain">Spain</option>
								   <option value="Sri Lanka">Sri Lanka</option>
								   <option value="Sudan">Sudan</option>
								   <option value="Suriname">Suriname</option>
								   <option value="Swaziland">Swaziland</option>
								   <option value="Sweden">Sweden</option>
								   <option value="Switzerland">Switzerland</option>
								   <option value="Syria">Syria</option>
								   <option value="Tahiti">Tahiti</option>
								   <option value="Taiwan">Taiwan</option>
								   <option value="Tajikistan">Tajikistan</option>
								   <option value="Tanzania">Tanzania</option>
								   <option value="Thailand">Thailand</option>
								   <option value="Togo">Togo</option>
								   <option value="Tokelau">Tokelau</option>
								   <option value="Tonga">Tonga</option>
								   <option value="Trinidad & Tobago">Trinidad & Tobago</option>
								   <option value="Tunisia">Tunisia</option>
								   <option value="Turkey">Turkey</option>
								   <option value="Turkmenistan">Turkmenistan</option>
								   <option value="Turks & Caicos Is">Turks & Caicos Is</option>
								   <option value="Tuvalu">Tuvalu</option>
								   <option value="Uganda">Uganda</option>
								   <option value="United Kingdom">United Kingdom</option>
								   <option value="Ukraine">Ukraine</option>
								   <option value="United Arab Emirates">United Arab Emirates</option>
								   <option value="United States of America">United States of America</option>
								   <option value="Uraguay">Uruguay</option>
								   <option value="Uzbekistan">Uzbekistan</option>
								   <option value="Vanuatu">Vanuatu</option>
								   <option value="Vatican City State">Vatican City State</option>
								   <option value="Venezuela">Venezuela</option>
								   <option value="Vietnam">Vietnam</option>
								   <option value="Virgin Islands (Brit)">Virgin Islands (Brit)</option>
								   <option value="Virgin Islands (USA)">Virgin Islands (USA)</option>
								   <option value="Wake Island">Wake Island</option>
								   <option value="Wallis & Futana Is">Wallis & Futana Is</option>
								   <option value="Yemen">Yemen</option>
								   <option value="Zaire">Zaire</option>
								   <option value="Zambia">Zambia</option>
								   <option value="Zimbabwe">Zimbabwe</option>
	';
}

function getCountryShortcut($cName)
{
    $countries = array("AF" => "Afghanistan", "AL" => "Albania", "DZ" => "Algeria", "AS" => "American Samoa", "AD" => "Andorra", "AO" => "Angola", "AI" => "Anguilla", "AQ" => "Antarctica", "AG" => "Antigua and Barbuda", "AR" => "Argentina", "AM" => "Armenia", "AW" => "Aruba", "AU" => "Australia", "AT" => "Austria", "AZ" => "Azerbaijan", "BS" => "Bahamas", "BH" => "Bahrain", "BD" => "Bangladesh", "BB" => "Barbados", "BY" => "Belarus", "BE" => "Belgium", "BZ" => "Belize", "BJ" => "Benin", "BM" => "Bermuda", "BT" => "Bhutan", "BO" => "Bolivia", "BA" => "Bosnia and Herzegovina", "BW" => "Botswana", "BV" => "Bouvet Island", "BR" => "Brazil", "IO" => "British Indian Ocean Territory", "BN" => "Brunei Darussalam", "BG" => "Bulgaria", "BF" => "Burkina Faso", "BI" => "Burundi", "KH" => "Cambodia", "CM" => "Cameroon", "CA" => "Canada", "CV" => "Cape Verde", "KY" => "Cayman Islands", "CF" => "Central African Republic", "TD" => "Chad", "CL" => "Chile", "CN" => "China", "CX" => "Christmas Island", "CC" => "Cocos (Keeling) Islands", "CO" => "Colombia", "KM" => "Comoros", "CG" => "Congo", "CD" => "Congo", "CK" => "Cook Islands", "CR" => "Costa Rica", "CI" => "Cote D'Ivoire", "HR" => "Croatia", "CU" => "Cuba", "CY" => "Cyprus", "CZ" => "Czech Republic", "DK" => "Denmark", "DJ" => "Djibouti", "DM" => "Dominica", "DO" => "Dominican Republic", "EC" => "Ecuador", "EG" => "Egypt", "SV" => "El Salvador", "GQ" => "Equatorial Guinea", "ER" => "Eritrea", "EE" => "Estonia", "ET" => "Ethiopia", "FK" => "Falkland Islands (Malvinas)", "FO" => "Faroe Islands", "FJ" => "Fiji", "FI" => "Finland", "FR" => "France", "GF" => "French Guiana", "PF" => "French Polynesia", "TF" => "French Southern Territories", "GA" => "Gabon", "GM" => "Gambia", "GE" => "Georgia", "DE" => "Germany", "GH" => "Ghana", "GI" => "Gibraltar", "GR" => "Greece", "GL" => "Greenland", "GD" => "Grenada", "GP" => "Guadeloupe", "GU" => "Guam", "GT" => "Guatemala", "GN" => "Guinea", "GW" => "Guinea-Bissau", "GY" => "Guyana", "HT" => "Haiti", "HM" => "Heard Island and Mcdonald Islands", "VA" => "Holy See (Vatican City State)", "HN" => "Honduras", "HK" => "Hong Kong", "HU" => "Hungary", "IS" => "Iceland", "IN" => "India", "ID" => "Indonesia", "IR" => "Iran", "IQ" => "Iraq", "IE" => "Ireland", "IL" => "Israel", "IT" => "Italy", "JM" => "Jamaica", "JP" => "Japan", "JO" => "Jordan", "KZ" => "Kazakhstan", "KE" => "Kenya", "KI" => "Kiribati", "KP" => "Korea North", "KR" => "Korea South", "KW" => "Kuwait", "KG" => "Kyrgyzstan", "LA" => "Lao People's Democratic Republic", "LV" => "Latvia", "LB" => "Lebanon", "LS" => "Lesotho", "LR" => "Liberia", "LY" => "Libyan Arab Jamahiriya", "LI" => "Liechtenstein", "LT" => "Lithuania", "LU" => "Luxembourg", "MO" => "Macao", "MK" => "Macedonia", "MG" => "Madagascar", "MW" => "Malawi", "MY" => "Malaysia", "MV" => "Maldives", "ML" => "Mali", "MT" => "Malta", "MH" => "Marshall Islands", "MQ" => "Martinique", "MR" => "Mauritania", "MU" => "Mauritius", "YT" => "Mayotte", "MX" => "Mexico", "FM" => "Micronesia, Federated States of", "MD" => "Moldova", "MC" => "Monaco", "MN" => "Mongolia", "MS" => "Montserrat", "MA" => "Morocco", "MZ" => "Mozambique", "MM" => "Myanmar", "NA" => "Namibia", "NR" => "Nauru", "NP" => "Nepal", "NL" => "Netherlands", "AN" => "Netherlands Antilles", "NC" => "New Caledonia", "NZ" => "New Zealand", "NI" => "Nicaragua", "NE" => "Niger", "NG" => "Nigeria", "NU" => "Niue", "NF" => "Norfolk Island", "MP" => "Northern Mariana Islands", "NO" => "Norway", "OM" => "Oman", "PK" => "Pakistan", "PW" => "Palau", "PS" => "Palestinian Territory, Occupied", "PA" => "Panama", "PG" => "Papua New Guinea", "PY" => "Paraguay", "PE" => "Peru", "PH" => "Philippines", "PN" => "Pitcairn", "PL" => "Poland", "PT" => "Portugal", "PR" => "Puerto Rico", "QA" => "Qatar", "RE" => "Reunion", "RO" => "Romania", "RU" => "Russian Federation", "RW" => "Rwanda", "SH" => "Saint Helena", "KN" => "Saint Kitts and Nevis", "LC" => "Saint Lucia", "PM" => "Saint Pierre and Miquelon", "VC" => "Saint Vincent and the Grenadines", "WS" => "Samoa", "SM" => "San Marino", "ST" => "Sao Tome and Principe", "SA" => "Saudi Arabia", "SN" => "Senegal", "CS" => "Serbia and Montenegro", "SC" => "Seychelles", "SL" => "Sierra Leone", "SG" => "Singapore", "SK" => "Slovakia", "SI" => "Slovenia", "SB" => "Solomon Islands", "SO" => "Somalia", "ZA" => "South Africa", "GS" => "South Georgia and the South Sandwich Islands", "ES" => "Spain", "LK" => "Sri Lanka", "SD" => "Sudan", "SR" => "Suriname", "SJ" => "Svalbard and Jan Mayen", "SZ" => "Swaziland", "SE" => "Sweden", "CH" => "Switzerland", "SY" => "Syrian Arab Republic", "TW" => "Taiwan", "TJ" => "Tajikistan", "TZ" => "Tanzania", "TH" => "Thailand", "TL" => "Timor-Leste", "TG" => "Togo", "TK" => "Tokelau", "TO" => "Tonga", "TT" => "Trinidad and Tobago", "TN" => "Tunisia", "TR" => "Turkey", "TM" => "Turkmenistan", "TC" => "Turks and Caicos Islands", "TV" => "Tuvalu", "UG" => "Uganda", "UA" => "Ukraine", "AE" => "United Arab Emirates", "GB" => "Great Britain", "UK" => "United Kingdom", "US" => "United States of America", "UM" => "United States Minor Outlying Islands", "UY" => "Uruguay", "UZ" => "Uzbekistan", "VU" => "Vanuatu", "VE" => "Venezuela", "VN" => "Viet Nam", "VG" => "Virgin Islands, British", "VI" => "Virgin Islands, U.s.", "WF" => "Wallis and Futuna", "EH" => "Western Sahara", "YE" => "Yemen", "ZM" => "Zambia", "ZW" => "Zimbabwe");
    return strtolower(array_search($cName, $countries));
}

function getCountryFromShortcut($sh)
{
    $countries = array("AF" => "Afghanistan", "AL" => "Albania", "DZ" => "Algeria", "AS" => "American Samoa", "AD" => "Andorra", "AO" => "Angola", "AI" => "Anguilla", "AQ" => "Antarctica", "AG" => "Antigua and Barbuda", "AR" => "Argentina", "AM" => "Armenia", "AW" => "Aruba", "AU" => "Australia", "AT" => "Austria", "AZ" => "Azerbaijan", "BS" => "Bahamas", "BH" => "Bahrain", "BD" => "Bangladesh", "BB" => "Barbados", "BY" => "Belarus", "BE" => "Belgium", "BZ" => "Belize", "BJ" => "Benin", "BM" => "Bermuda", "BT" => "Bhutan", "BO" => "Bolivia", "BA" => "Bosnia and Herzegovina", "BW" => "Botswana", "BV" => "Bouvet Island", "BR" => "Brazil", "IO" => "British Indian Ocean Territory", "BN" => "Brunei Darussalam", "BG" => "Bulgaria", "BF" => "Burkina Faso", "BI" => "Burundi", "KH" => "Cambodia", "CM" => "Cameroon", "CA" => "Canada", "CV" => "Cape Verde", "KY" => "Cayman Islands", "CF" => "Central African Republic", "TD" => "Chad", "CL" => "Chile", "CN" => "China", "CX" => "Christmas Island", "CC" => "Cocos (Keeling) Islands", "CO" => "Colombia", "KM" => "Comoros", "CG" => "Congo", "CD" => "Congo", "CK" => "Cook Islands", "CR" => "Costa Rica", "CI" => "Cote D'Ivoire", "HR" => "Croatia", "CU" => "Cuba", "CY" => "Cyprus", "CZ" => "Czech Republic", "DK" => "Denmark", "DJ" => "Djibouti", "DM" => "Dominica", "DO" => "Dominican Republic", "EC" => "Ecuador", "EG" => "Egypt", "SV" => "El Salvador", "GQ" => "Equatorial Guinea", "ER" => "Eritrea", "EE" => "Estonia", "ET" => "Ethiopia", "FK" => "Falkland Islands (Malvinas)", "FO" => "Faroe Islands", "FJ" => "Fiji", "FI" => "Finland", "FR" => "France", "GF" => "French Guiana", "PF" => "French Polynesia", "TF" => "French Southern Territories", "GA" => "Gabon", "GM" => "Gambia", "GE" => "Georgia", "DE" => "Germany", "GH" => "Ghana", "GI" => "Gibraltar", "GR" => "Greece", "GL" => "Greenland", "GD" => "Grenada", "GP" => "Guadeloupe", "GU" => "Guam", "GT" => "Guatemala", "GN" => "Guinea", "GW" => "Guinea-Bissau", "GY" => "Guyana", "HT" => "Haiti", "HM" => "Heard Island and Mcdonald Islands", "VA" => "Holy See (Vatican City State)", "HN" => "Honduras", "HK" => "Hong Kong", "HU" => "Hungary", "IS" => "Iceland", "IN" => "India", "ID" => "Indonesia", "IR" => "Iran", "IQ" => "Iraq", "IE" => "Ireland", "IL" => "Israel", "IT" => "Italy", "JM" => "Jamaica", "JP" => "Japan", "JO" => "Jordan", "KZ" => "Kazakhstan", "KE" => "Kenya", "KI" => "Kiribati", "KP" => "Korea North", "KR" => "Korea South", "KW" => "Kuwait", "KG" => "Kyrgyzstan", "LA" => "Lao People's Democratic Republic", "LV" => "Latvia", "LB" => "Lebanon", "LS" => "Lesotho", "LR" => "Liberia", "LY" => "Libyan Arab Jamahiriya", "LI" => "Liechtenstein", "LT" => "Lithuania", "LU" => "Luxembourg", "MO" => "Macao", "MK" => "Macedonia", "MG" => "Madagascar", "MW" => "Malawi", "MY" => "Malaysia", "MV" => "Maldives", "ML" => "Mali", "MT" => "Malta", "MH" => "Marshall Islands", "MQ" => "Martinique", "MR" => "Mauritania", "MU" => "Mauritius", "YT" => "Mayotte", "MX" => "Mexico", "FM" => "Micronesia, Federated States of", "MD" => "Moldova", "MC" => "Monaco", "MN" => "Mongolia", "MS" => "Montserrat", "MA" => "Morocco", "MZ" => "Mozambique", "MM" => "Myanmar", "NA" => "Namibia", "NR" => "Nauru", "NP" => "Nepal", "NL" => "Netherlands", "AN" => "Netherlands Antilles", "NC" => "New Caledonia", "NZ" => "New Zealand", "NI" => "Nicaragua", "NE" => "Niger", "NG" => "Nigeria", "NU" => "Niue", "NF" => "Norfolk Island", "MP" => "Northern Mariana Islands", "NO" => "Norway", "OM" => "Oman", "PK" => "Pakistan", "PW" => "Palau", "PS" => "Palestinian Territory, Occupied", "PA" => "Panama", "PG" => "Papua New Guinea", "PY" => "Paraguay", "PE" => "Peru", "PH" => "Philippines", "PN" => "Pitcairn", "PL" => "Poland", "PT" => "Portugal", "PR" => "Puerto Rico", "QA" => "Qatar", "RE" => "Reunion", "RO" => "Romania", "RU" => "Russian Federation", "RW" => "Rwanda", "SH" => "Saint Helena", "KN" => "Saint Kitts and Nevis", "LC" => "Saint Lucia", "PM" => "Saint Pierre and Miquelon", "VC" => "Saint Vincent and the Grenadines", "WS" => "Samoa", "SM" => "San Marino", "ST" => "Sao Tome and Principe", "SA" => "Saudi Arabia", "SN" => "Senegal", "CS" => "Serbia and Montenegro", "SC" => "Seychelles", "SL" => "Sierra Leone", "SG" => "Singapore", "SK" => "Slovakia", "SI" => "Slovenia", "SB" => "Solomon Islands", "SO" => "Somalia", "ZA" => "South Africa", "GS" => "South Georgia and the South Sandwich Islands", "ES" => "Spain", "LK" => "Sri Lanka", "SD" => "Sudan", "SR" => "Suriname", "SJ" => "Svalbard and Jan Mayen", "SZ" => "Swaziland", "SE" => "Sweden", "CH" => "Switzerland", "SY" => "Syrian Arab Republic", "TW" => "Taiwan", "TJ" => "Tajikistan", "TZ" => "Tanzania", "TH" => "Thailand", "TL" => "Timor-Leste", "TG" => "Togo", "TK" => "Tokelau", "TO" => "Tonga", "TT" => "Trinidad and Tobago", "TN" => "Tunisia", "TR" => "Turkey", "TM" => "Turkmenistan", "TC" => "Turks and Caicos Islands", "TV" => "Tuvalu", "UG" => "Uganda", "UA" => "Ukraine", "AE" => "United Arab Emirates", "GB" => "Great Britain", "UK" => "United Kingdom", "US" => "United States of America", "UM" => "United States Minor Outlying Islands", "UY" => "Uruguay", "UZ" => "Uzbekistan", "VU" => "Vanuatu", "VE" => "Venezuela", "VN" => "Viet Nam", "VG" => "Virgin Islands, British", "VI" => "Virgin Islands, U.s.", "WF" => "Wallis and Futuna", "EH" => "Western Sahara", "YE" => "Yemen", "ZM" => "Zambia", "ZW" => "Zimbabwe");
    return $countries[$sh];
}

function listClients()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM clients WHERE isDeleted='0'");
    while ($row = $result->fetch_object()) {
        echo "<tr><td>" . ($row->id) . "</td><td>" . ($row->clientLongName) . "</td><td>" . ($row->clientShortName) . "</td><td>" . ($row->enterprise) . "</td><td>" . ($row->country) . "</td><td>" . ($row->zip) . "</td><td>" . ($row->city) . "</td><td><a href='client-one-management.php?id=" . ($row->id) . "'>Manage</a></td></tr>";
    }
    $link->close();
}

function getClientInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM clients WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    $client = array(
        'id' => "",
        'clientLongName' => "",
        'enterprise' => "",
        'clientShortName' => "",
        'country' => "",
        'zip' => "",
        'city' => "",
        'adres' => "",
        'rei' => "",
        'market' => "",
        'type' => "",
        'kam' => "",
        'asm' => "",
        'de' => "",
        'pl' => "",
        'ep' => "",
        'noInq' => "",
        'volInq' => "",
        'noAxInqR' => "",
        'volAxInqR' => "",
        'noOrd' => "",
        'volOrd' => "",
        'cmOrd' => "",
        'tl' => "",
        'emailPurchase' => "",
        'emailTechnican' => "",
        'subType' => "",
        'hitrate' => "",
        'offers' => "",
        'orders' => "",
        'ordersValue' => "",
        'offersValue' => "",
        'region' => "",
        'url' => "",
        'email' => "",
        'phone' => "",
        'previousName' => "",
        'followUp' => "",
        'nextContactDate' => "",
        'vatNumber' => "",
        'invAddr' => "",
        'invExtraMA' => "",
        'invCity' => "",
        'invZip' => "",
        'invCountry' => "",
        'invIBAN' => "",
    );

    if ($row) {
        $client = array(
            'id' => ($row->id),
            'clientLongName' => ($row->clientLongName),
            'enterprise' => ($row->enterprise),
            'clientShortName' => ($row->clientShortName),
            'country' => ($row->country),
            'zip' => ($row->zip),
            'city' => ($row->city),
            'adres' => ($row->adres),
            'rei' => ($row->rei),
            'market' => ($row->market),
            'type' => ($row->type),
            'kam' => ($row->kam),
            'asm' => ($row->asm),
            'de' => ($row->de),
            'pl' => ($row->pl),
            'ep' => ($row->ep),
            'noInq' => ($row->noInq),
            'volInq' => ($row->volInq),
            'noAxInqR' => ($row->noAxInqR),
            'volAxInqR' => ($row->volAxInqR),
            'noOrd' => ($row->noOrd),
            'volOrd' => ($row->volOrd),
            'cmOrd' => ($row->cmOrd),
            'tl' => ($row->tl),
            'emailPurchase' => ($row->emailPurchase),
            'emailTechnican' => ($row->emailTechnican),
            'subType' => ($row->subType),
            'hitrate' => ($row->hitrate),
            'offers' => ($row->offers),
            'orders' => ($row->orders),
            'ordersValue' => ($row->ordersValue),
            'offersValue' => ($row->offersValue),
            'region' => ($row->region),
            'url' => ($row->url),
            'email' => ($row->email),
            'phone' => ($row->phone),
            'previousName' => ($row->previousName),
            'followUp' => ($row->followUp),
            'nextContactDate' => ($row->nextContactDate),
            'vatNumber' => ($row->vatNumber),
            'invAddr' => ($row->invAddr),
            'invExtraMA' => ($row->invExtraMA),
            'invCity' => ($row->invCity),
            'invZip' => ($row->invZip),
            'invCountry' => ($row->invCountry),
            'invIBAN' => ($row->invIBAN),
        );
        return $client;
    }
    return [
        'id' => '',
        'clientLongName' => '',
        'city' => '',
        'country' => '',
        'market' => '',
        'clientShortName' => '',
        'tl' => null
    ];
}

function selectClients()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients");
    $link->close();
    echo "<option value='' selected disabled>Select client</option>";
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
}

function selectEndClients()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients WHERE rei='E'");
    $link->close();
    echo "<option value='' selected>Select client</option>";
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
}

function selectResellerClients()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients WHERE rei!='E'");
    $link->close();
    echo "<option value='' selected>Select client</option>";
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
}

function selectClientsEdit($c = "")
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients");
    $link->close();
    echo "<option value='' selected disabled>Select client</option>";
    while ($row = $result->fetch_object()) {
        if ($c == $row->clientLongName)
            echo "<option selected value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
        else
            echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
    }
}

function selectClientsFiltersReseller($c = "")
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients WHERE rei!='E'");
    $link->close();
    echo "<option value='' selected>--Reseller--</option>";
    while ($row = $result->fetch_object()) {
        if ($c == $row->clientLongName)
            echo "<option selected value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
        else
            echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
    }
}

function selectClientsFiltersEndclient($c = "")
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT DISTINCT(clientLongName) FROM clients WHERE rei='E'");
    $link->close();
    echo "<option value='' selected>--End client--</option>";
    while ($row = $result->fetch_object()) {
        if ($c == $row->clientLongName)
            echo "<option selected value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
        else
            echo "<option value='" . ($row->clientLongName) . "'>" . ($row->clientLongName) . "</option>";
    }
}

function listReplaceEndclients()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM clients WHERE rei='E' AND isDeleted=0");
    $link->close();
    echo "<option value='' selected>Clients</option>";
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->id) . "'>" . ($row->id) . " | " . ($row->clientLongName) . " | " . ($row->city) . "</option>";
}

function listReplaceResellers()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM clients WHERE rei!='E' AND isDeleted=0");
    $link->close();
    echo "<option value='' selected>Clients</option>";
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->id) . "'>" . ($row->id) . " | " . ($row->clientLongName) . " | " . ($row->city) . "</option>";
}

function selectV($v = "", $showall = "")
{
    $link = connectUsers();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listV='1' AND u.isActive='1' ORDER BY u.nazwisko ASC");
    $link->close();
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        echo '<option value="">Empty values</option>';
    } else
        echo '<option value="empty">Empty values</option>';
    while ($row = $result->fetch_object()) {
        if ($v == $row->id)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function selectTLs($tl = "", $showall = "")
{
    $link = connectUsers();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listTL='1' AND u.isActive='1' ORDER BY u.nazwisko ASC");
    $link->close();
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        echo '<option value="">Empty values</option>';
    } else
        echo '<option value="empty">Empty values</option>';
    while ($row = $result->fetch_object()) {
        if ($tl == $row->id)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function listTls()
{
    $link = connectUsers();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listTL='1' AND u.isActive='1' ORDER BY u.nazwisko ASC");
    $link->close();
    echo "<option value='' selected>Select</option>";
    while ($row = $result->fetch_object()) {
        if ($tl == $row->id)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function detectIP()
{

    $ip = "";

    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED'];
    } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_FORWARDED'])) {
        $ip = $_SERVER['HTTP_FORWARDED'];
    } else if (isset($_SERVER['REMOTE_ADDR'])) {
        $ip = $_SERVER['REMOTE_ADDR'];
    }

    if (is_array($ip)) {
        $ip = "";
    }
    $ip = preg_replace('/[^a-zA-Z0-9,._=\+\()\-\:]/', "_", $ip);

    return $ip;
}

function saveSecLog($offer_id, $desc)
{
    $link = connect();

    $idUserAction = 0;

    if (isset($_SESSION['plasticonDigitalUser']['id'])) {
        $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    }
    $ip = detectIP();

    $query = sprintf(
        "INSERT INTO seclogs (`userId`, `offer`, `details`, `ip`, `event_date`) VALUES ('%s','%s','%s','%s', NOW())",
        mysqli_real_escape_string($link, strip_tags($idUserAction)),
        mysqli_real_escape_string($link, strip_tags($offer_id)),
        mysqli_real_escape_string($link, strip_tags($desc)),
        mysqli_real_escape_string($link, strip_tags($ip))
    );

    $link->query($query);

    $link->close();
}

function getOfferInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM offers WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    if ($row)
        $clientInfo = getClientInfo($row->client);
    $clientLongName = isset($clientInfo['clientLongName']) ? $clientInfo['clientLongName'] : "";
    $clientInfoCity = isset($clientInfo['city']) ? $clientInfo['city'] : "";
    $cmp = 0;
    $offer = array(
        'id' => "",
        'offerNo' => -1,
        'oldOfferNo' => "",
        'OT' => "",
        'inquiryNo' => "",
        'client' => "",
        'clientLongName' => "",
        'clientCity' => "",
        'endClient' => "",
        'finalClient' => "",
        'endClientName' => "",
        'endclientContactPurchase' => "",
        'endclientContactTechnican' => "",
        'endclientInquiryNo' => "",
        'orderLocation' => "",
        'productionLocation' => "",
        'plantLocationCountry' => "",
        'plantLocationCity' => "",
        'deliveryDate' => "",
        'scope' => "",
        'oID' => "",
        'V' => "",
        'DN' => "",
        'm3' => "",
        'kg' => "",
        'inquiry' => "",
        'request' => "",
        'offer' => "",
        'order' => "",
        'requestedOrderDate' => "",
        'OVE' => "",
        'OV' => "",
        'GO' => "",
        'GET' => "",
        'GxG' => "",
        'OVEgg' => "",
        'AX' => "",
        'reason' => "",
        'WHP' => "",
        'WHS' => "",
        'costPrice' => "",
        'BT' => "",
        'orderValue' => "",
        'projectName' => "",
        'company' => "",
        'pm' => "",
        'inpm' => "",
        'clientOrderNo' => "",
        'note' => "",
        'InR' => "",
        'InID' => "",
        'SOC' => "",
        'step' => 0,
        'orderNo' => "",
        'CMp' => "",
        'segment' => "",
        'projectId' => "",
        'productionOrderNo' => "",
        'productionPM' => "",
        'productionValue' => "",
        'nextContactDate' => "",
        'technican' => "",
        'purchase' => "",
        'F' => "",
        'InF' => "",
        'productionReservation' => "",
        'incoterms' => "",
        'competitor' => "",
        'requestedDeliveryDate' => "",
        'ORVCM' => "",
        'prodValCM' => "",
        'orderCompany' => "",
        'calcPersons' => "",
        'serviceStart' => "",
        'serviceEnd' => "",
        'mailRev' => "",
        /*
         * NOT EXISTS IN DATABASE
         */
        'stat' => '',
        'prot' => '',
        'componentType' => '',
        'pressure' => '',
        'contactWith' => '',
        /*
         * for GCE, WPC and TC extranll app links
         */
        'gce_id' => '0',
        'wpc_id' => '0',
        'wtc_id' => '0'
    );

    if ($row) {
        if ($row->costPrice != 0)
            $cmp = round(((($row->OVE) - ($row->costPrice)) * 100) / ($row->costPrice), 2);
        $offer = array(
            'id' => ($row->id),
            'offerNo' => ($row->offerNo),
            'oldOfferNo' => ($row->oldOfferNo),
            'OT' => ($row->OT),
            'inquiryNo' => ($row->inquiryNo),
            'client' => ($row->client),
            'clientLongName' => $clientLongName,
            'clientCity' => $clientInfoCity,
            'endClient' => ($row->finalClient),
            'finalClient' => ($row->finalClient),
            'endClientName' => getClientName($row->finalClient),
            'endclientContactPurchase' => ($row->endclientContactPurchase),
            'endclientContactTechnican' => ($row->endclientContactTechnican),
            'endclientInquiryNo' => ($row->endclientInquiryNo),
            'orderLocation' => ($row->orderLocation),
            'productionLocation' => ($row->productionLocation),
            'plantLocationCountry' => ($row->plantLocationCountry),
            'plantLocationCity' => ($row->plantLocationCity),
            'deliveryDate' => ($row->deliveryDate),
            'scope' => ($row->scope),
            'oID' => ($row->oID),
            'V' => ($row->V),
            'inquiry' => ($row->inquiry),
            'request' => ($row->request),
            'offer' => ($row->offer),
            'order' => ($row->order),
            'requestedOrderDate' => ($row->requestedOrderDate),
            'OVE' => ($row->OVE),
            'OV' => ($row->OV),
            'GO' => ($row->GO),
            'GET' => ($row->GET),
            'GxG' => ($row->GO) / 100 * ($row->GET),
            'OVEgg' => (($row->OVE) / 100) * (($row->GO) / 100) * ($row->GET),
            'AX' => ($row->AX),
            'reason' => ($row->reason),
            'WHP' => ($row->WHP),
            'WHS' => ($row->WHS),
            'costPrice' => ($row->costPrice),
            'BT' => ($row->BT),
            'orderValue' => ($row->orderValue),
            'projectName' => ($row->projectName),
            'company' => ($row->company),
            'pm' => ($row->PM),
            'inpm' => ($row->inPM),
            'clientOrderNo' => ($row->clientOrderNo),
            'note' => ($row->note),
            'InR' => ($row->InR),
            'InID' => ($row->InID),
            'SOC' => ($row->SOC),
            'step' => ($row->step),
            'orderNo' => ($row->orderNo),
            'CMp' => ($row->CMp),
            'segment' => ($row->segment),
            'projectId' => ($row->projectId),
            'productionOrderNo' => ($row->productionOrderNo),
            'productionPM' => ($row->productionPM),
            'productionValue' => ($row->productionValue),
            'nextContactDate' => ($row->nextContactDate),
            'technican' => ($row->technican),
            'purchase' => ($row->purchase),
            'F' => ($row->F),
            'InF' => ($row->InF),
            'productionReservation' => ($row->productionReservation),
            'incoterms' => ($row->incoterms),
            'competitor' => ($row->competitor),
            'requestedDeliveryDate' => ($row->requestedDeliveryDate),
            'ORVCM' => ($row->ORVCM),
            'prodValCM' => ($row->prodValCM),
            'orderCompany' => ($row->orderCompany),
            'calcPersons' => ($row->calcPersons),
            'serviceStart' => ($row->serviceStart),
            'serviceEnd' => ($row->serviceEnd),
            'mailRev' => ($row->mailRev),
            /*
             * NOT EXISTS IN DATABASE
             */
            'stat' => '',
            'prot' => '',
            'componentType' => '',
            'pressure' => '',
            'contactWith' => '',
            /*
             * for GCE, WPC and TC extranll app links
             */
            'gce_id' => $row->gce_id,
            'wpc_id' => $row->wpc_id,
            'wtc_id' => $row->wtc_id
        );
    }
    return $offer;
}

function getComponentInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM components WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    $component = array(
        'id' => ($row->id),
        'offerNo' => ($row->offerNo),
        'position' => ($row->position),
        'revision' => ($row->revision),
        'scope' => ($row->scope),
        'segment' => ($row->segment),
        'segmentRead' => str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($row->segment)))),
        'segmentOMS' => str_replace("{wiecej}", "&gt;", str_replace("{mniej}", "&lt;", str_replace("{srednica}", "&#8960;", ($row->segment)))),
        'stat' => ($row->stat),
        'prot' => ($row->prot),
        'DN' => ($row->DN),
        'm3' => ($row->m3),
        'kg' => ($row->kg),
        'pressure' => ($row->pressure),
        'pressureRead' => str_replace("{wiecejrowne}", ">=", str_replace("{mniej}", "<", ($row->pressure))),
        'medium' => ($row->medium),
        'componentType' => ($row->componentType),
        'OV' => ($row->OV),
        'GO' => ($row->GO),
        'GET' => ($row->GET),
        'AX' => ($row->AX),
        'reason' => ($row->reason),
        'orderNo' => ($row->orderNo),
        'WHP' => ($row->WHP),
        'WHS' => ($row->WHS),
        'costPrice' => ($row->costPrice),
        'CMp' => ($row->CMp),
        'pm' => ($row->PM),
        'pmFull' => getNameAndSurname($row->PM),
        'counts' => ($row->counts),
        'orderValue' => ($row->orderValue),
        'orderNoProduction' => ($row->orderNoProduction),
        'delivery' => ($row->delivery),
        'calcPerson' => ($row->calcPerson),
        'calcPersonFull' => getUserName($row->calcPerson) . " " . getUserSurname($row->calcPerson),
        'productionOrderValue' => ($row->productionOrderValue),
        'ORVCM' => ($row->ORVCM),
        'prodValCM' => ($row->prodValCM),
        'prodDate' => ($row->prodDate),
        'productionLocation' => ($row->productionLocation),
        'requestedStartService' => ($row->requestedStartService),
        'requestedEndService' => ($row->requestedEndService),
        'ddS' => ($row->ddS),
        'ddE' => ($row->ddE),
        'aoeS' => ($row->aoeS),
        'aoeE' => ($row->aoeE),
        'fS' => ($row->fS),
        'fE' => ($row->fE),
        'tS' => ($row->tS),
        'tE' => ($row->tE),
        'siS' => ($row->siS),
        'siE' => ($row->siE),
        'wp_milestone_start' => ($row->wp_milestone_start),
        'wp_milestone_end' => ($row->wp_milestone_end),
        'qc_milestone_start' => ($row->qc_milestone_start),
        'qc_milestone_end' => ($row->qc_milestone_end),
        'nobo' => ($row->nobo),
        'noboClient' => ($row->noboClient),
        'ped' => ($row->ped),
    );
    return $component;
}

function getSubcomponentInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM subcomponents WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_array();
    return $row;
}

function getCompetitorInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM competitors WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    $competitor = array(
        'id' => ($row->id),
        'name' => ($row->name),
        'location' => ($row->location),
        'VA41' => ($row->VA41),
        'VA42' => ($row->VA42),
        'VAPTP' => ($row->VAPTP),
        'VAFF' => ($row->VAFF),
        'piping25600' => ($row->piping25600),
        'piping6001500' => ($row->piping6001500),
        'LLining' => ($row->LLining),
        'SLining' => ($row->SLining),
        'FPLining' => ($row->FPLining),
        'wetESP' => ($row->wetESP),
        'ducts' => ($row->ducts),
        'chimneyStacks' => ($row->chimneyStacks),
        'other' => ($row->other),
        'installation' => ($row->installation),
        'dailyService' => ($row->dailyService),
        'revamping' => ($row->revamping),
        'note' => ($row->note),
        'nextContactDate' => ($row->nextContactDate)
    );
    return $competitor;
}

function dniPomiedzy($d1, $d2)
{
    $d1 = strtotime($d1);
    $d2 = strtotime($d2);
    $diff = $d2 - $d1;
    return round($diff / (60 * 60 * 24));
}

function getContactData($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clientsContacts WHERE id='$id'");
    $row = $result->fetch_object();
    if ($row) {
        return ($row->name) . " " . ($row->surname);
    }
    return false;
}

function listContacts($id)
{
    $link = connect();
    $query = sprintf(
        "SELECT * FROM contacts WHERE offerId='%s' ORDER BY id DESC",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $link->close();
    $i = 1;
    while ($row = $result->fetch_object()) {
        $nextContactDate = "";
        if ($row->nextContactDate != '0000-00-00')
            $nextContactDate = $row->nextContactDate;
        $contactDate = "";
        if ($row->contactDate != '0000-00-00')
            $contactDate = $row->contactDate;
        echo "<tr><td>" . getNameAndSurname($row->contact) . "</td><td><strong>" . $contactDate . "</strong></td></tr>";
        // data-toggle='modal' data-target='#editR' onclick='setEditValuesCon($(this).attr(".'"editvalues"'."));'>Edit</a></td>
    }
}

function getCompetitorName($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM competitors WHERE id='$id'");
    $row = $result->fetch_object();
    if ($row) {
        return $row->name;
    }
    return false;
}

function listCompetitors()
{
    $link = connect();
    $result = $link->query("SELECT * FROM competitors WHERE isDeleted='0'");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->id) . "'>" . ($row->name) . "</option>";
}

function getUserInfo($id)
{
    $link = connectUsers();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT c.*, u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    if ($row) {
        $user = array(
            'id' => ($row->id),
            'imie' => ($row->imie),
            'nazwisko' => ($row->nazwisko),
            'email' => ($row->email),
            'phone' => ($row->phone),
            'company' => ($row->company),
            'avatar' => ($row->avatar),
            'listTL' => ($row->listTL),
            'listID' => ($row->listID),
            'listV' => ($row->listV),
            'listKAM' => ($row->listKAM),
            'listASM' => ($row->listASM),
            'listPM' => ($row->listPM),
            'listFollow' => ($row->listFollow),
            'clientManagement' => ($row->addClient),
            'userManagement' => ($row->addUser),
            'statistics' => ($row->statistics),
            'technicalDashboard' => ($row->technicalDashboard),
            'onlyPersonalInfo' => ($row->onlyPersonalInfo),
            'visibleOtherCompanies' => ($row->visibleOtherCompanies),
            'addOffer' => ($row->addOffer),
            'deleteOffer' => ($row->deleteOffer),
            'clientInfo' => ($row->clientInfo),
            'projectInfo' => ($row->projectInfo),
            'salesInfo' => ($row->salesInfo),
            'staffInfo' => ($row->staffInfo),
            'datesInfo' => ($row->datesInfo),
            'technicalInfo' => ($row->technicalInfo),
            'orderInfo' => ($row->orderInfo),
            'admin' => ($row->admin),
            'projectManagement' => ($row->projectManagement),
            'competitorsManagement' => ($row->competitorsManagement),
            'editButtons' => ($row->editButtons),
            'export' => ($row->export),
            'exportProducts' => ($row->exportProducts),
            'externalCompany' => ($row->externalCompany),
            'secret' => ($row->secret)
        );
        return $user;
    }
    return null;
}

function lastOfferNo()
{
    $link = connect();
    $result = $link->query("SELECT * FROM offers WHERE CHAR_LENGTH(offerNo)=8 ORDER BY offerNo desc");
    $row = $result->fetch_object();
    return $row->offerNo;
}

function lastProjectNo()
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects ORDER BY projectNo desc");
    $row = $result->fetch_object();
    return $row->projectNo;
}

function clientLocations($cl, $city)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE clientLongName='$cl'");
    while ($row = $result->fetch_object())
        if ($city == $row->city)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->city) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->city) . "</option>";
}

function listUniqueOfferNo()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(offerNo) FROM offers WHERE offerNo NOT LIKE '%-%' ORDER by offerNo DESC");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->offerNo) . "'>" . ($row->offerNo) . "</option>";
}

function lastOfferIndex($o)
{
    $o = "%" . $o . "%";
    $link = connect();
    $result = $link->query("SELECT offerNo FROM offers WHERE offerNo LIKE '$o' ORDER by offerNo DESC");
    $row = $result->fetch_object();
    $index = explode("-", $row->offerNo);
    $index = explode(".", $index[1]);
    return $index[0];
}

function listClientsEnterprise()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(enterprise) FROM clients WHERE enterprise!=''");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->enterprise) . "'>" . ($row->enterprise) . "</option>";
}

function listClientsCountries()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(country) FROM clients WHERE country!=''");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->country) . "'>" . ($row->country) . "</option>";
}

function listClientsZip()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(zip) FROM clients WHERE zip!=''");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->zip) . "'>" . ($row->zip) . "</option>";
}

function listClientsTL()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(tl) FROM clients WHERE tl!='' AND isDeleted='0'");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->tl) . "'>" . getUserName($row->tl) . " " . getUserSurname($row->tl) . "</option>";
}

function listClientsKAM()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(kam) FROM clients WHERE kam!='' AND isDeleted='0'");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->kam) . "'>" . getUserName($row->kam) . " " . getUserSurname($row->kam) . "</option>";
}

function listClientsASM()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(asm) FROM clients WHERE asm!='' AND isDeleted='0'");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->asm) . "'>" . getUserName($row->asm) . " " . getUserSurname($row->asm) . "</option>";
}

function getOfferId($offer)
{
    $link = connect();
    $result = $link->query("SELECT * FROM offers WHERE offerNo='$offer'");
    $row = $result->fetch_object();
    return $row->id;
}

function getOfferNo($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM offers WHERE id='$id'");
    $row = $result->fetch_object();
    return $row->offerNo;
}

function getProjectNo($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects WHERE id='$id'");
    $row = $result->fetch_object();
    return $row->projectNo;
}

function inquiriesThisMonth($date)
{
    $d1 = substr($date, 0, 8) . "01";
    $d2 = date("Y-m-t", strtotime($date));
    $link = connect();
    $result = $link->query("SELECT COUNT(inquiry) as ile FROM offers WHERE inquiry BETWEEN '$d1' AND '$d2'");
    $row = $result->fetch_object();
    return $row->ile;
}

function inquiriesThisFY($date)
{
    if (date("m", strtotime($date)) < 7) {
        $d1 = (date("Y", strtotime($date)) - 1) . "-07-01";
        $d2 = date("Y", strtotime($date)) . "-06-30";
    } else {
        $d1 = date("Y", strtotime($date)) . "-07-01";
        $d2 = (date("Y", strtotime($date)) + 1) . "-06-30";
    }
    $link = connect();
    $result = $link->query("SELECT COUNT(inquiry) as ile FROM offers WHERE inquiry BETWEEN '$d1' AND '$d2'");
    $row = $result->fetch_object();
    return $row->ile;
}

function offersThisMonth($date)
{
    $d1 = substr($date, 0, 8) . "01";
    $d2 = date("Y-m-t", strtotime($date));
    $link = connect();
    $result = $link->query("SELECT COUNT(offer) as ile FROM offers WHERE offer BETWEEN '$d1' AND '$d2'");
    $row = $result->fetch_object();
    return $row->ile;
}

function offersThisFY($date)
{
    if (date("m", strtotime($date)) < 7) {
        $d1 = (date("Y", strtotime($date)) - 1) . "-07-01";
        $d2 = date("Y", strtotime($date)) . "-06-30";
    } else {
        $d1 = date("Y", strtotime($date)) . "-07-01";
        $d2 = (date("Y", strtotime($date)) + 1) . "-06-30";
    }
    $link = connect();
    $result = $link->query("SELECT COUNT(offer) as ile FROM offers WHERE offer BETWEEN '$d1' AND '$d2'");
    $row = $result->fetch_object();
    return $row->ile;
}

function translateCompanies($cmp)
{
    switch ($cmp) {
        case 'PTN':
            return 'Plasticon the Netherlands';
            break;
        case 'PP':
            return 'Plasticon Poland';
            break;
        case 'PG':
            return 'Plasticon Germany';
            break;
        case 'TP':
            return 'Thermopol';
            break;
        case 'PT':
            return 'Plasto-Tec';
            break;
        case 'TNV':
            return 'Thermopol NV';
            break;
        case 'TBV':
            return 'Thermopol BV';
            break;
        default:
            return '';
            break;
    }
}

function addClKams($kam)
{
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' AND c.listKAM='1' ORDER BY u.nazwisko ASC");
    echo '<option value="" selected>Select</option>';
    while ($row = $result->fetch_object()) {
        if ($row->id == $kam)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function addClAsms($asm)
{
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' AND c.listASM='1' ORDER BY u.nazwisko ASC");
    echo '<option value="" selected>Select</option>';
    while ($row = $result->fetch_object()) {
        if ($row->id == $asm)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function addClTls($tl)
{
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' AND c.listTL='1' ORDER BY u.nazwisko ASC");
    echo '<option value="" selected>Select</option>';
    while ($row = $result->fetch_object()) {
        if ($row->id == $tl)
            echo "<option selected value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
}

function listClientContacts($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clientsContacts WHERE clientId='$id'");
    while ($row = $result->fetch_object()) {
        $del = "";
        if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0)
            $del = "<a href='#' deleteId='" . ($row->id) . "' cntnt='" . ($row->name) . " " . ($row->surname) . "' class='deleteRow' data-toggle='modal' data-target='#deleteR' onclick='setDeleteValues($(this).attr(" . '"deleteId"' . "),$(this).attr(" . '"cntnt"' . "));'>Delete</a> | ";
        $ava = "";
        if ($row->avaliable == 1)
            $ava = "<i class='fas fa-check'></i>";
        $title = "";
        if ($row->gender == 'Female')
            $title = "Ms.";
        if ($row->gender == 'Male')
            $title = "Mr.";
        echo "<tr><td>" . ($row->id) . "</td><td>" . $title . "</td><td>" . ($row->name) . "</td><td>" . ($row->surname) . "</td><td>" . ($row->email) . "</td><td><a href='tel:" . ($row->phone1) . "'>" . ($row->phone1) . "</a></td><td><a href='tel:" . ($row->phone2) . "'>" . ($row->phone2) . "</a></td><td>" . ($row->position) . "</td><td class='text-center'>" . $ava . "</td><td>$del<a href='#' editvalues='" . ($row->id) . "[-]" . ($row->name) . "[-]" . ($row->surname) . "[-]" . ($row->email) . "[-]" . ($row->phone1) . "[-]" . ($row->phone2) . "[-]" . ($row->position) . "[-]" . ($row->avaliable) . "[-]" . ($row->gender) . "' data-toggle='modal' data-target='#editR' onclick='setEditValues($(this).attr(" . '"editvalues"' . "));'>Edit</a></td></tr>";
    }
    $link->close();
}

function listClientOffers($id)
{
    $link = connect();
    $result = $link->query("SELECT *, ROUND(`GO`/100*`GET`) as gxg FROM offers WHERE client='$id' OR finalClient='$id'");
    while ($row = $result->fetch_object()) {
        $conDate = "";
        if (($row->OV == "0" || empty($row->OV)) && $row->AX == "")
            $conDate = "<div class='circle-blue'></div>";
        else {
            if ($row->AX != "Order" && $row->AX != "Lost" && $row->AX != "Terminated") {
                $today = date("Y-m-d");
                if (isset($nextContactDateAX) && $nextContactDateAX == "0000-00-00") {
                    if (date("Y-m-d", strtotime("+3 days", strtotime($row->offer))) <= $today)
                        $conDate = "<div class='circle-red'></div>";
                    else
                        $conDate = "<div class='circle-green'></div>";
                } else {
                    if (isset($nextContactDateAX) && $nextContactDateAX >= $today)
                        $conDate = "<div class='circle-green'></div>";
                    else
                        $conDate = "<div class='circle-orenge'></div>";
                }
            } else
                $conDate = "<div class='circle-grey'></div>";
        }
        if ($row->AX != '') {
            switch ($row->AX) {
                case 'Order':
                    $conDate = "<i class='fa fa-check' style='color:#00FF00;margin:4px 3px;font-size:20px;'></i>";
                    break;
                case 'Terminated':
                    $conDate = "<i class='fa fa-times' style='color:yellow;margin:4px 5px;font-size:20px;'></i>";
                    break;
                case 'Lost':
                    $conDate = "<i class='fa fa-times' style='color:red;margin:4px 5px;font-size:20px;'></i>";
                    break;
            }
        }
        echo "<tr class='pointer' onclick='location.href=" . '"offer.php?id=' . ($row->id) . '"' . "'><td class='text-center'>" . ($conDate) . "</td><td>" . ($row->offerNo) . "</td><td class='text-right'>" . ($row->SOC) . "</td><td>" . ($row->OT) . "</td><td>" . ($row->scope) . "</td><td>" . ($row->InR) . "</td><td>" . ($row->requestedOrderDate) . "</td><td>" . ($row->order) . "</td><td class='text-right'>" . number_format(round($row->OVE), 0, ".", " ") . "</td><td class='text-right'>" . ($row->gxg) . "</td><td>" . ($row->nextContactDate) . "</td><td>" . ($row->lastComment) . "</td></tr>";
    }
    $link->close();
}

function reasonFilter($r = 'all', $showall = '')
{
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        if ($r == '')
            echo "<option value='' selected>Empty values</option>";
        else
            echo "<option value=''>Empty values</option>";
    } else {
        if ($r == 'empty')
            echo "<option value='empty' selected>Empty values</option>";
        else
            echo "<option value='empty'>Empty values</option>";
    }
    if ($r == 'Material')
        echo "<option value='Material' selected>Material</option>";
    else
        echo "<option value='Material'>Material</option>";
    if ($r == 'Investment terminated')
        echo "<option value='Delivery time' selected>Delivery time</option>";
    else
        echo "<option value='Delivery time'>Delivery time</option>";
    if ($r == 'Client lost order')
        echo "<option value='Client lost order' selected>Client lost order</option>";
    else
        echo "<option value='Client lost order'>Client lost order</option>";
    if ($r == 'Forwarded to cluster company')
        echo "<option value='Forwarded to cluster company' selected>Forwarded to cluster company</option>";
    else
        echo "<option value='Forwarded to cluster company'>Forwarded to cluster company</option>";
    if ($r == 'Delivery time')
        echo "<option value='Delivery time' selected>Delivery time</option>";
    else
        echo "<option value='Delivery time'>Delivery time</option>";
    if ($r == 'Price')
        echo "<option value='Price' selected>Price</option>";
    else
        echo "<option value='Price'>Price</option>";
    if ($r == 'Proposal capacity')
        echo "<option value='Proposal capacity' selected>Proposal capacity</option>";
    else
        echo "<option value='Proposal capacity'>Proposal capacity</option>";
    if ($r == 'Technology')
        echo "<option value='Technology' selected>Technology</option>";
    else
        echo "<option value='Technology'>Technology</option>";
}

function reasonList($r = '')
{
    echo "<option value='' selected>Select</option>";
    if ($r == 'Client lost order')
        echo "<option value='Client lost order' selected>Client lost order</option>";
    else
        echo "<option value='Client lost order'>Client lost order</option>";
    if ($r == 'Delivery time')
        echo "<option value='Delivery time' selected>Delivery time</option>";
    else
        echo "<option value='Delivery time'>Delivery time</option>";
    if ($r == 'Forwarded to PP')
        echo "<option value='Forwarded to PP' selected>Forwarded to PP</option>";
    else
        echo "<option value='Forwarded to PP'>Forwarded to PP</option>";
    if ($r == 'Investment terminated')
        echo "<option value='Investment terminated' selected>Investment terminated</option>";
    else
        echo "<option value='Investment terminated'>Investment terminated</option>";
    if ($r == 'Material')
        echo "<option value='Material' selected>Material</option>";
    else
        echo "<option value='Material'>Material</option>";
    if ($r == 'Other')
        echo "<option value='Other' selected>Other</option>";
    else
        echo "<option value='Other'>Other</option>";
    if ($r == 'Price')
        echo "<option value='Price' selected>Price</option>";
    else
        echo "<option value='Price'>Price</option>";
    if ($r == 'Proposal capacity')
        echo "<option value='Proposal capacity' selected>Proposal capacity</option>";
    else
        echo "<option value='Proposal capacity'>Proposal capacity</option>";
    if ($r == 'Technology')
        echo "<option value='Technology' selected>Technology</option>";
    else
        echo "<option value='Technology'>Technology</option>";
}

function reasonListConc($r = '')
{
    $output = "";
    $output .= "<option value='' selected>Select</option>";
    if ($r == 'Client lost order')
        $output .= "<option value='Client lost order' selected>Client lost order</option>";
    else
        $output .= "<option value='Client lost order'>Client lost order</option>";
    if ($r == 'Delivery time')
        $output .= "<option value='Delivery time' selected>Delivery time</option>";
    else
        $output .= "<option value='Delivery time'>Delivery time</option>";
    if ($r == 'Forwarded to PP')
        $output .= "<option value='Forwarded to PP' selected>Forwarded to PP</option>";
    else
        $output .= "<option value='Forwarded to PP'>Forwarded to PP</option>";
    if ($r == 'Investment terminated')
        $output .= "<option value='Investment terminated' selected>Investment terminated</option>";
    else
        $output .= "<option value='Investment terminated'>Investment terminated</option>";
    if ($r == 'Material')
        $output .= "<option value='Material' selected>Material</option>";
    else
        $output .= "<option value='Material'>Material</option>";
    if ($r == 'Other')
        $output .= "<option value='Other' selected>Other</option>";
    else
        $output .= "<option value='Other'>Other</option>";
    if ($r == 'Price')
        $output .= "<option value='Price' selected>Price</option>";
    else
        $output .= "<option value='Price'>Price</option>";
    if ($r == 'Proposal capacity')
        $output .= "<option value='Proposal capacity' selected>Proposal capacity</option>";
    else
        $output .= "<option value='Proposal capacity'>Proposal capacity</option>";
    if ($r == 'Technology')
        $output .= "<option value='Technology' selected>Technology</option>";
    else
        $output .= "<option value='Technology'>Technology</option>";
    return $output;
}

function islast($offer)
{
    $link = connect();
    if (strlen($offer) == 8)
        $result = $link->query("SELECT * FROM offers ORDER BY offerNo DESC LIMIT 1");
    else {
        $number = substr($offer, 0, 8) . "%";
        $result = $link->query("SELECT * FROM offers WHERE offerNo LIKE '$number' ORDER BY offerNo DESC LIMIT 1");
    }
    $row = $result->fetch_object();
    if ($row->offerNo == $offer)
        return true;
    return false;
}

function selectIDs($id = '')
{
    echo "<option value='' selected disabled>Select IS</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listID='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object())
        if ($row->id == $id)
            echo "<option value='" . ($row->id) . "' selected>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    $link->close();
}

function listUsers()
{
    $link = connectUsers();
    echo "<option value='0' selected>Select</option>";
    $result = $link->query("SELECT * FROM users WHERE isActive='1' ORDER BY imie ASC, nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "'>$imie $nazwisko</option>";
    }
    $link->close();
}

function listAllUsers()
{
    $link = connectUsers();
    echo "<option value='0' selected>Select user</option>";
    $result = $link->query("SELECT * FROM users WHERE isActive='1' ORDER BY imie ASC, nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "' selected>$imie $nazwisko</option>";
    }
    $link->close();
}

function listFollowUpUsersAdd()
{
    $link = connectUsers();
    echo "<option value='' selected>Select user</option>";
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' and c.listFollow='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "'>$imie $nazwisko</option>";
    }
    $link->close();
}

function listFollowUpUsers()
{
    $link = connectUsers();
    echo "<option value='' selected>Select user</option>";
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' and c.listFollow='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "'>$imie $nazwisko</option>";
    }
    $link->close();
}

function listFollowUpUsersFilters($showall = "")
{
    $link = connectUsers();
    if ($showall == "") {
        echo "<option value='all' selected>--Follow Up--</option>";
        echo "<option value=''>Empty values</option>";
    } else
        echo "<option value='empty'>Empty values</option>";
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' and c.listFollow='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "'>$imie $nazwisko</option>";
    }
    $link->close();
}

function listUsersFilters($showall = "")
{
    $link = connectUsers();
    if ($showall == "") {
        echo "<option value='all' selected>Show All</option>";
        echo "<option value=''>Empty values</option>";
    } else
        echo "<option value='empty'>Empty values</option>";
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE u.isActive='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $id = $row->id;
        $imie = $row->imie;
        $nazwisko = $row->nazwisko;
        echo "<option value='" . $id . "'>$imie $nazwisko</option>";
    }
    $link->close();
}

function getUsersShortLongNames()
{
    $link = connectUsers();
    $result = $link->query("SELECT id, imie, nazwisko FROM users");
    $tablica = [];
    while ($row = $result->fetch_object()) {
        $tablica[$row->id] = [$row->imie . " " . $row->nazwisko, substr($row->imie, 0, 1) . substr($row->nazwisko, 0, 1)];
    }
    $link->close();
    return $tablica;
}

function selectIDsF($id = '', $showall = "")
{
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        echo "<option value=''>Empty values</option>";
    } else
        echo "<option value='empty'>Empty values</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listID='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function selectKAMs($showall = "")
{
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        echo "<option value=''>Empty values</option>";
    } else
        echo "<option value='empty'>Empty values</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listKAM='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function listKAMs()
{
    echo "<option value='' selected>Select</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listKAM='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function selectASMs($showall = "")
{
    if ($showall == "") {
        echo "<option value='all' selected>Show all</option>";
        echo "<option value=''>Empty values</option>";
    } else
        echo "<option value='empty'>Empty values</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listASM='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function listASMs()
{
    echo "<option value='' selected>Select</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listASM='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function selectVs($id = '')
{
    echo "<option value='' selected disabled>Select R</option>";
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listV='1' ORDER BY u.imie ASC, u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        if ($row->id == $id)
            echo "<option value='" . ($row->id) . "' selected>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    $link->close();
}

function listContactsPerson($client)
{

    if (empty($client)) {
        return "";
    }

    $link = connect();

    $result = $link->query("SELECT cc.id, CONCAT(cc.name, ' ', cc.surname) AS name, c.clientShortName as company_name FROM clientsContacts cc LEFT JOIN clients c ON cc.clientId=c.id WHERE cc.clientId='$client' ORDER BY cc.name ASC, cc.surname ASC");

    $num_rows = mysqli_num_rows($result);

    if ($num_rows == 0) {
        return "";
    }

    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->name) . " - " . ($row->company_name) . "</option>";
    }
}


function translateMonths($m)
{
    switch ($m) {
        case '1':
            return 'January';
            break;
        case '2':
            return 'February';
            break;
        case '3':
            return 'March';
            break;
        case '4':
            return 'April';
            break;
        case '5':
            return 'May';
            break;
        case '6':
            return 'June';
            break;
        case '7':
            return 'July';
            break;
        case '8':
            return 'August';
            break;
        case '9':
            return 'September';
            break;
        case '10':
            return 'October';
            break;
        case '11':
            return 'November';
            break;
        case '12':
            return 'December';
            break;
    }
}

function valueChart()
{
    $m = intval(date("m"));
    $months = [];
    $months[0] = $m;
    for ($i = 1; $i < 6; $i++) {
        if ($m + 1 == 13) {
            $m = 1;
            $months[$i] = $m;
        } else {
            $m += 1;
            $months[$i] = $m;
        }
    }
    $link = connect();
    $values = [];
    foreach ($months as $month) {
        if ($month < $months[0])
            $year = intval(date("Y")) + 1;
        else
            $year = intval(date("Y"));
        $result = $link->query("SELECT SUM(((`OVE`)/100)*(`GO`/100)*`GET`) as ile FROM offers WHERE MONTH(`order`)='$month' AND YEAR(`order`)='$year'");
        $row = $result->fetch_object();
        array_push($values, $row->ile);
    }
    echo "var valueCh = new Chart(valueChart, {
			type: 'bar',
			data: {
				labels: [";
    for ($i = 0; $i < sizeof($months); $i++)
        echo "'" . translateMonths($months[$i]) . "',";
    echo "],
				datasets: [{
					label: 'Value (M€)',
					data: [";
    foreach ($values as $value)
        echo "'" . round($value / 1000, 2) . "',";
    echo "],
					backgroundColor: 'rgba(54, 162, 235, 0.5)',
					borderColor: 'rgba(54, 162, 235, 1)',
					borderWidth: 1
				}]
			},
			options: {
				legend: {
					display: false
				},
				scales: {
					yAxes: [{
						ticks: {
							beginAtZero:true
						}
					}]
				}
			}
		});";
}

function offersChart()
{
    $m = intval(date("m"));
    if ($m - 5 < 0)
        $m = 12 - (5 - $m);
    else
        $m -= 5;
    $months = [];
    $months[0] = $m;
    for ($i = 1; $i < 6; $i++) {
        if ($m + 1 == 13) {
            $m = 1;
            $months[$i] = $m;
        } else {
            $m += 1;
            $months[$i] = $m;
        }
    }
    $link = connect();
    $values = [];
    foreach ($months as $month) {
        if ($month < $months[0])
            $year = intval(date("Y")) + 1;
        else
            $year = intval(date("Y"));
        $result = $link->query("SELECT COUNT(*) as ile FROM offers WHERE MONTH(`offer`)='$month' AND YEAR(`offer`)='$year'");
        $row = $result->fetch_object();
        array_push($values, $row->ile);
    }
    echo "var offersChart = new Chart(offersChart, {
			type: 'bar',
			data: {
				labels: [";
    for ($i = 0; $i < sizeof($months); $i++)
        echo "'" . translateMonths($months[$i]) . "',";
    echo "],
				datasets: [{
					label: 'Amount',
					data: [";
    foreach ($values as $value)
        echo "'" . $value . "',";
    echo "],
					backgroundColor: 'rgba(54, 162, 235, 0.5)',
					borderColor: 'rgba(54, 162, 235, 1)',
					borderWidth: 1
				}]
			},
			options: {
				legend: {
					display: false
				},
				scales: {
					yAxes: [{
						ticks: {
							beginAtZero:true
						}
					}]
				}
			}
		});";
}

function randomPassword()
{
    $alphabet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!@#$';
    $pass = array();
    $alphaLength = strlen($alphabet) - 1;
    for ($i = 0; $i < 8; $i++) {
        $n = rand(0, $alphaLength);
        $pass[] = $alphabet[$n];
    }
    return implode($pass);
}

function getOfferIds($offerId)
{
    $link = connect();
    $offer = $link->query("SELECT * FROM offers WHERE id='$offerId'");
    $row = $offer->fetch_object();
    $offer = $row->offerNo;
    $offer = explode("-", $offer);
    $offer = "%" . $offer[0] . "%";
    $result = $link->query("SELECT * FROM offers WHERE offerNo LIKE '$offer'");
    $link->close();
    $ids = [];
    while ($row = $result->fetch_object())
        array_push($ids, $row->id);
    return $ids;
}

function getConIds($conId)
{
    $link = connect();
    $result = $link->query("SELECT * FROM contacts WHERE id='$conId'");
    $row = $result->fetch_object();
    $cp = $row->contact;
    $cd = $row->contactDate;
    $ncd = $row->nextContactDate;
    $n = $row->note;
    $result = $link->query("SELECT * FROM contacts WHERE contact='$cp' AND contactDate='$cd' AND nextContactDate='$ncd' AND note='$n'");
    $ids = [];
    while ($row = $result->fetch_object())
        array_push($ids, array(($row->id), ($row->offerId)));
    return $ids;
}

function getUserOnlineStatus($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    if (date("Y-m-d H:i:s") > date('Y-m-d H:i:s', strtotime('+10 minutes', strtotime($row->lastActivity))))
        return 'offline';
    return 'online';
}

function getUserLastActivity($id)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM users WHERE id='$id'");
    $row = $result->fetch_object();
    if (($row->lastActivity) != "0000-00-00 00:00:00")
        return $row->lastActivity;
    return 'This user has no activity.';
}

function listLogs()
{
    $link = connect();
    $result = $link->query("SELECT * FROM logs");
    while ($row = $result->fetch_object()) {
        echo "<tr><td>" . ($row->id) . "</td><td>" . getNameAndSurname($row->userId) . "</td><td>" . ($row->timestamp) . "</td><td>" . ($row->action) . "</td><td>" . ($row->details) . "</td><td>" . ($row->ip) . "</td></tr>";
    }
}

function duplicateOffer($of)
{
    $link = connect();
    $result = $link->query("SELECT MAX(position) as pos FROM components WHERE offerNo='$of'");
    if ($result->num_rows == 0)
        $position = 1;
    else {
        $row = $result->fetch_object();
        $position = ($row->pos) + 1;
    }
    $offer = getOfferInfo(getOfferId($of));
    $query = sprintf(
        "INSERT INTO `components`(`offerNo`, `position`, `scope`) VALUES ('%s','%s','%s')",
        mysqli_real_escape_string($link, $of),
        mysqli_real_escape_string($link, $position),
        mysqli_real_escape_string($link, $offer['scope'])
    );
    $link->query($query);
    $cmpId = $link->insert_id;
    $query = sprintf(
        "INSERT INTO `stats`(`idOferty`, `IdCmp`, `offerNo`, `OT`, `clientShortName`, `scope`, `GO`, `GET`, `GxG`, `orderDate`, `inquiryDate`, `inR`, `inIS`, `company`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, getOfferId($of)),
        mysqli_real_escape_string($link, $cmpId),
        mysqli_real_escape_string($link, $offer['offerNo']),
        mysqli_real_escape_string($link, $offer['OT']),
        mysqli_real_escape_string($link, getClientName($offer['client']) . " / " . getClientName($offer['endClient'])),
        mysqli_real_escape_string($link, $offer['scope']),
        mysqli_real_escape_string($link, $offer['GO']),
        mysqli_real_escape_string($link, $offer['GET']),
        mysqli_real_escape_string($link, $offer['GxG']),
        mysqli_real_escape_string($link, $offer['order']),
        mysqli_real_escape_string($link, $offer['inquiry']),
        mysqli_real_escape_string($link, $offer['InR']),
        mysqli_real_escape_string($link, $offer['InID']),
        mysqli_real_escape_string($link, $offer['company'])
    );
    $link->query($query);
    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new component','Added component with position $position to offer number $of','$ip')");
    $link->close();
}

function countSummaryOffer($offerNo)
{
    $link = connect();
    $cms = [];
    $ovs = [];
    $cmSUM = 0;
    $ovSUM = 0;
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offerNo' AND counts='1'");
    while ($row = $result->fetch_object()) {
        $ov = $row->OV;
        $cm = $row->CMp;
        $ovSUM += round($ov, 2);
        $cmSUM += $cm * $ov / 100;
    }
    $cmp = 0;
    if ($ovSUM > 0)
        $cmp = round($cmSUM / $ovSUM * 100);
    $result = $link->query("SELECT ROUND(SUM(OV),1) as ov FROM components WHERE offerNo='$offerNo' AND counts='1'");
    $row = $result->fetch_object();
    $ov = $row->ov;
    $link->query("UPDATE offers SET OV='$ov', CMp='$cmp' WHERE offerNo='$offerNo'");
    $result = $link->query("SELECT DISTINCT(segment) as segment FROM components WHERE offerNo='$offerNo' AND counts='1'");
    $segment = "";
    while ($row = $result->fetch_object()) {
        $segment .= ($row->segment) . ";";
    }
    $link->query("UPDATE offers SET segment='$segment' WHERE offerNo='$offerNo'");
    $link->query("UPDATE offers SET WHP=(SELECT SUM(WHP) FROM components WHERE offerNo='$offerNo' AND counts='1'), WHS=(SELECT SUM(WHS) FROM components WHERE offerNo='$offerNo' AND counts='1') WHERE offerNo='$offerNo'");
    $link->close();
}

function createRev($id, $cmp)
{
    $link = connect();
    $offer = $cmp['offerNo'];
    $position = $cmp['position'];
    $oldRev = $cmp['revision'];
    $result = $link->query("SELECT MAX(revision) as rev FROM components WHERE offerNo='$offer' AND position='$position'");
    $row = $result->fetch_object();
    $rev = ($row->rev) + 1;
    $query = sprintf(
        "INSERT INTO `components`(`offerNo`, `position`, `revision`, `scope`, `segment`, `stat`, `prot`, `DN`, `m3`, `medium`, `componentType`, `GO`, `GET`, `AX`, `reason`, `orderNo`, `costPrice`, `PM`, `counts`, `technicalDone`, `comercialDone`, `orderValue`, `orderNoProduction`, `delivery`, `calcPerson`, `productionLocation`, `ORVCM`, `productionOrderValue`, `prodValCM`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, $cmp['offerNo']),
        mysqli_real_escape_string($link, $cmp['position']),
        mysqli_real_escape_string($link, $rev),
        mysqli_real_escape_string($link, $cmp['scope']),
        mysqli_real_escape_string($link, $cmp['segment']),
        mysqli_real_escape_string($link, $cmp['stat']),
        mysqli_real_escape_string($link, $cmp['prot']),
        mysqli_real_escape_string($link, $cmp['DN']),
        mysqli_real_escape_string($link, $cmp['m3']),
        mysqli_real_escape_string($link, $cmp['medium']),
        mysqli_real_escape_string($link, $cmp['componentType']),
        mysqli_real_escape_string($link, $cmp['GO']),
        mysqli_real_escape_string($link, $cmp['GET']),
        mysqli_real_escape_string($link, ''),
        mysqli_real_escape_string($link, $cmp['reason']),
        mysqli_real_escape_string($link, $cmp['orderNo']),
        mysqli_real_escape_string($link, $cmp['costPrice']),
        mysqli_real_escape_string($link, $cmp['PM']),
        mysqli_real_escape_string($link, $cmp['counts']),
        mysqli_real_escape_string($link, 0),
        mysqli_real_escape_string($link, 0),
        mysqli_real_escape_string($link, $cmp['orderValue']),
        mysqli_real_escape_string($link, $cmp['orderNoProduction']),
        mysqli_real_escape_string($link, $cmp['delivery']),
        mysqli_real_escape_string($link, $cmp['calcPerson']),
        mysqli_real_escape_string($link, $cmp['productionLocation']),
        mysqli_real_escape_string($link, $cmp['ORVCM']),
        mysqli_real_escape_string($link, $cmp['productionOrderValue']),
        mysqli_real_escape_string($link, $cmp['prodValCM'])
    );
    $link->query("UPDATE components SET AX='Lost', `GO`='0', `GET`='0', counts='0' WHERE offerNo='$offer' AND position='$position'");
    $link->query($query);
    $offerId = getOfferId($cmp['offerNo']);
    $newid = $link->insert_id;
    $result = $link->query("SELECT * FROM stats WHERE idOferty='$offerId' AND idCmp='$id'");
    $row = $result->fetch_object();
    $query = sprintf(
        "INSERT INTO `stats`(`idOferty`, `IdCmp`, `offerNo`, `OT`, `clientShortName`, `scope`, `OV`, `GO`, `GET`, `GxG`, `OVgg`, `orderDate`, `inquiryDate`, `inR`, `inIS`, `company`, `status`, `productionLocation`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, $row->idOferty),
        mysqli_real_escape_string($link, $newid),
        mysqli_real_escape_string($link, $row->offerNo),
        mysqli_real_escape_string($link, $row->OT),
        mysqli_real_escape_string($link, $row->clientShortName),
        mysqli_real_escape_string($link, $row->scope),
        mysqli_real_escape_string($link, $row->OV),
        mysqli_real_escape_string($link, $row->GO),
        mysqli_real_escape_string($link, $row->GET),
        mysqli_real_escape_string($link, $row->GxG),
        mysqli_real_escape_string($link, $row->OVgg),
        mysqli_real_escape_string($link, $row->orderDate),
        mysqli_real_escape_string($link, $row->inquiryDate),
        mysqli_real_escape_string($link, $row->inR),
        mysqli_real_escape_string($link, $row->inIS),
        mysqli_real_escape_string($link, $row->company),
        mysqli_real_escape_string($link, $row->status),
        mysqli_real_escape_string($link, $row->productionLocation)
    );
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offer' AND position='$position'");
    while ($row = $result->fetch_object()) {
        $idCm = $row->id;
        $link->query("UPDATE stats SET status='Lost', `GO`='0', `GET`='0' WHERE idCmp='$idCm' AND idOferty='$offerId'");
    }
    $link->query($query);
    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Created component revision','Created revision of $position-rev/$oldRev with position $position-rev/$rev within offer number $offer','$ip')");

    $result = $link->query("SELECT * FROM subcomponents WHERE componentId='$id'");
    while ($row = $result->fetch_object()) {
        $link->query(sprintf(
            "INSERT INTO `subcomponents`(`componentId`, `offerNo`, `scope`, `productionLocation`, `stat`, `prot`, `medium`, `diameter`, `pressure`, `WHP`, `weight`, `volume`, `offerValue`, `offerValueCM`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
            mysqli_real_escape_string($link, $newid),
            mysqli_real_escape_string($link, $row->offerNo),
            mysqli_real_escape_string($link, $row->scope),
            mysqli_real_escape_string($link, $row->productionLocation),
            mysqli_real_escape_string($link, $row->stat),
            mysqli_real_escape_string($link, $row->prot),
            mysqli_real_escape_string($link, $row->medium),
            mysqli_real_escape_string($link, $row->diameter),
            mysqli_real_escape_string($link, $row->pressure),
            mysqli_real_escape_string($link, $row->WHP),
            mysqli_real_escape_string($link, $row->weight),
            mysqli_real_escape_string($link, $row->volume),
            mysqli_real_escape_string($link, $row->offerValue),
            mysqli_real_escape_string($link, $row->offerValueCM)
        ));
    }
    $link->close();
    countSummaryOffer($offer);
    countStep(getOfferId($offer));
}

function createCmpCopy($id, $cmp)
{
    $link = connect();
    $offer = $cmp['offerNo'];
    $oldPos = $cmp['position'];
    $oldRev = $cmp['revision'];
    $oldPos = $oldPos . "-rev/" . $oldRev;
    $position = $cmp['position'];
    $result = $link->query("SELECT MAX(position) as pos FROM components WHERE offerNo='$offer'");
    $row = $result->fetch_object();
    $pos = ($row->pos) + 1;
    $query = sprintf(
        "INSERT INTO `components`(`offerNo`, `position`, `revision`, `scope`, `segment`, `stat`, `prot`, `DN`, `m3`, `kg`, `pressure`, `medium`, `componentType`, `OV`, `GO`, `GET`, `AX`, `reason`, `orderNo`, `WHP`, `WHS`, `costPrice`, `CMp`, `PM`, `counts`, `technicalDone`, `comercialDone`, `orderValue`, `orderNoProduction`, `delivery`, `calcPerson`, `productionLocation`, `ORVCM`, `productionOrderValue`, `prodValCM`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, $cmp['offerNo']),
        mysqli_real_escape_string($link, $pos),
        mysqli_real_escape_string($link, 0),
        mysqli_real_escape_string($link, $cmp['scope']),
        mysqli_real_escape_string($link, $cmp['segment']),
        mysqli_real_escape_string($link, $cmp['stat']),
        mysqli_real_escape_string($link, $cmp['prot']),
        mysqli_real_escape_string($link, $cmp['DN']),
        mysqli_real_escape_string($link, $cmp['m3']),
        mysqli_real_escape_string($link, $cmp['kg']),
        mysqli_real_escape_string($link, $cmp['pressure']),
        mysqli_real_escape_string($link, $cmp['medium']),
        mysqli_real_escape_string($link, $cmp['componentType']),
        mysqli_real_escape_string($link, $cmp['OV']),
        mysqli_real_escape_string($link, $cmp['GO']),
        mysqli_real_escape_string($link, $cmp['GET']),
        mysqli_real_escape_string($link, ''),
        mysqli_real_escape_string($link, $cmp['reason']),
        mysqli_real_escape_string($link, $cmp['orderNo']),
        mysqli_real_escape_string($link, $cmp['WHP']),
        mysqli_real_escape_string($link, $cmp['WHS']),
        mysqli_real_escape_string($link, $cmp['costPrice']),
        mysqli_real_escape_string($link, $cmp['CMp']),
        mysqli_real_escape_string($link, $cmp['PM']),
        mysqli_real_escape_string($link, $cmp['counts']),
        mysqli_real_escape_string($link, 0),
        mysqli_real_escape_string($link, 0),
        mysqli_real_escape_string($link, $cmp['orderValue']),
        mysqli_real_escape_string($link, $cmp['orderNoProduction']),
        mysqli_real_escape_string($link, $cmp['delivery']),
        mysqli_real_escape_string($link, $cmp['calcPerson']),
        mysqli_real_escape_string($link, $cmp['productionLocation']),
        mysqli_real_escape_string($link, $cmp['ORVCM']),
        mysqli_real_escape_string($link, $cmp['productionOrderValue']),
        mysqli_real_escape_string($link, $cmp['prodValCM'])
    );
    $link->query($query);
    $offerId = getOfferId($cmp['offerNo']);
    $newid = $link->insert_id;
    $result = $link->query("SELECT * FROM stats WHERE idOferty='$offerId' AND idCmp='$id'");
    $row = $result->fetch_object();
    $query = sprintf(
        "INSERT INTO `stats`(`idOferty`, `IdCmp`, `offerNo`, `OT`, `clientShortName`, `scope`, `OV`, `GO`, `GET`, `GxG`, `OVgg`, `orderDate`, `inquiryDate`, `inR`, `inIS`, `company`, `status`, `productionLocation`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, $row->idOferty),
        mysqli_real_escape_string($link, $newid),
        mysqli_real_escape_string($link, $row->offerNo),
        mysqli_real_escape_string($link, $row->OT),
        mysqli_real_escape_string($link, $row->clientShortName),
        mysqli_real_escape_string($link, $row->scope),
        mysqli_real_escape_string($link, $row->OV),
        mysqli_real_escape_string($link, $row->GO),
        mysqli_real_escape_string($link, $row->GET),
        mysqli_real_escape_string($link, $row->GxG),
        mysqli_real_escape_string($link, $row->OVgg),
        mysqli_real_escape_string($link, $row->orderDate),
        mysqli_real_escape_string($link, $row->inquiryDate),
        mysqli_real_escape_string($link, $row->inR),
        mysqli_real_escape_string($link, $row->inIS),
        mysqli_real_escape_string($link, $row->company),
        mysqli_real_escape_string($link, $row->status),
        mysqli_real_escape_string($link, $row->productionLocation)
    );
    $link->query($query);
    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Copied component','Copied component position $oldPos to new component with position $pos-rev/0 within offer number $offer','$ip')");
    $link->close();
}

function getLastContactId($offerId)
{
    $link = connect();
    $result = $link->query("SELECT * FROM contacts WHERE offerId='$offerId' ORDER BY id DESC");
    $row = $result->fetch_object();
    return $row->id;
}

function getLastContact($offerId)
{
    $link = connect();
    $output = [];
    $result = $link->query("SELECT * FROM contacts WHERE offerId='$offerId' ORDER BY id DESC");
    $row = $result->fetch_object();
    $output["contact"] = "";
    $output["date"] = "";
    $output["note"] = "";
    $output["contactWith"] = "";
    if ($row) {
        $output["contact"] = $row->contact;
        $output["date"] = $row->contactDate;
        $output["note"] = $row->note;
        $output["contactWith"] = $row->contactWith;
    }
    $link->close();
    return $output;
}

function getLastContactProject($id)
{
    $link = connect();
    /*
     * DEFINE DEFAULT
     */
    $output = [
        'contact' => '',
        'date' => '',
        'note' => '',
        'contactWith' => ''
    ];
    $result = $link->query("SELECT `contact`,`contactDate` as `date`,`note`,`contactWith` FROM `contactsProject` WHERE `projectId`='$id' ORDER BY id DESC");
    $row = $result->fetch_array();
    if ($row !== null) {
        $output = $row[0];
    }
    $link->close();
    return $output;
}

function isV($user)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM crm WHERE userId='$user'");
    $row = $result->fetch_object();
    if ($row->listV == 1)
        return true;
    return false;
}

function isID($user)
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM crm WHERE userId='$user'");
    $row = $result->fetch_object();
    if ($row->listID == 1)
        return true;
    return false;
}

function countContactButton()
{
    $id = $_SESSION['plasticonDigitalUser']['id'];
    $cpn = $_SESSION['plasticonDigitalUser']['company'];
    $d1 = date("Y-m-d");
    $d2 = date('Y-m-d', strtotime(date("Y-m-d") . " - 3 day"));
    $searchQuery = "";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 0)
        $searchQuery = " AND o.company='$cpn'";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1)
        $searchQuery = " AND o.company='$cpn' AND o.V='$id'";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1)
        $searchQuery = " AND o.V='$id'";
    $query = "SELECT COUNT(*) as ile FROM offers o LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id WHERE ((AX='' AND (OV!='0') AND (o.nextContactDate!='0000-00-00' AND o.nextContactDate<'$d1')) OR ((OV!='0') AND AX='' AND o.nextContactDate='0000-00-00' AND offer<='$d2')) AND V='$id'";
    $link = connect();
    $result = $link->query($query);
    $row = $result->fetch_object();
    return $row->ile;
}

function countTodoButton()
{
    $id = $_SESSION['plasticonDigitalUser']['id'];
    $cpn = $_SESSION['plasticonDigitalUser']['company'];
    $searchQuery = "";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 0)
        $searchQuery = " AND o.company='$cpn'";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1)
        $searchQuery = " AND o.company='$cpn' AND (c.KAM='$id' OR c.ASM='$id' OR c.TL='$id' OR o.V='$id' OR o.oID='$id' OR o.F='$id')";
    if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1)
        $searchQuery = " AND (c.KAM='$id' OR c.ASM='$id' OR c.TL='$id' OR o.V='$id' OR o.oID='$id' OR o.F='$id')";
    $query = "SELECT COUNT(*) as ile FROM offers o LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id WHERE ((OV='0') AND AX='' AND oID='$id') ";
    $query .= " AND CHAR_LENGTH(offerNo)=8";
    $link = connect();
    $result = $link->query($query . $searchQuery);
    $row = $result->fetch_object();
    return $row->ile;
}

function hitrate($client)
{
    $link = connect();
    $result = $link->query("SELECT SUM(OVE) as ile FROM offers WHERE AX='Order' AND client='$client'");
    $row = $result->fetch_object();
    $A = $row->ile;
    $result = $link->query("SELECT SUM(OVE) as ile FROM offers WHERE client='$client'");
    $row = $result->fetch_object();
    $X = $row->ile;
    if ($A != 0 || $X != 0)
        return round($A * 100 / ($X));
    else
        return 0;
}

function hitrateValues($client)
{
    if ($client != "") {
        $link = connect();
        $result = $link->query("SELECT SUM(OVE) as ile FROM offers WHERE AX='Order' AND client=$client");
        $row = $result->fetch_object();
        $A = $row->ile;
        $result = $link->query("SELECT SUM(OVE) as ile FROM offers WHERE client=$client");
        $row = $result->fetch_object();
        $X = $row->ile;
        return round($A) . " | " . round($X) . " k&euro;";
    } else
        return "";
}

function hitrateCount($client)
{
    if ($client != "") {
        $link = connect();
        $result = $link->query("SELECT COUNT(*) as ile FROM offers WHERE AX='Order' AND client=$client");
        $row = $result->fetch_object();
        $A = $row->ile;
        $result = $link->query("SELECT COUNT(*) as ile FROM offers WHERE client=$client");
        $row = $result->fetch_object();
        $X = $row->ile;
        return round($A) . " | " . round($X);
    } else
        return "";
}

function getContactNameAndSurname($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clientsContacts WHERE id='$id'");
    $row = $result->fetch_object();
    $link->close();
    if ($row)
        return ($row->name) . " " . ($row->surname);
    return false;
}

function cleanData(&$str)
{
    $str = strip_tags($str);
    //$str = preg_replace("/\t/", " ", $str);
    //$str = preg_replace("/\n/", " ", $str);
    //$str = preg_replace("/\r?\n/", " ", $str);
    //if(strstr($str, '"')) $str = '"' . str_replace('"', '""', $str) . '"';
    //$str = str_replace("\t", " ", $str);
    //$str = str_replace("\n", " ", $str);
    $str = preg_replace('/(\v|\s)+/', ' ', $str);
    $str = bez_pl($str);
    $str = iconv('UTF-8', 'ISO-8859-1//TRANSLIT//IGNORE', $str);
}

function downloadAll($cols)
{
    $cols = $_SESSION['plasticonDigitalUser']['crm']['offerCols'];
    //array_push($cols, "market");
    //array_push($cols, "marketE");
    //array_push($cols, "segment");
    $colsQuery = "";
    $colLetter = "";

    foreach ($cols as $col) {
        switch ($col) {
            case 'conDate':
                $colsQuery .= " AX as status,";
                break;
            case 'gxg':
                $colsQuery .= " ROUND(`GO`/100*`GET`) as gxg,";
                break;
            case 'OVgg':
                $colsQuery .= " ROUND(((`OVE`)/100)*(`GO`/100)*`GET`) as OVgg,";
                break;
            case 'InR':
                $colsQuery .= " `$col` AS R,";
                break;
            case 'gCMp':
                $colsQuery .= " ROUND((((o.orderValue*o.ORVCM)/100)+((o.productionValue*o.prodValCM)/100))*100/o.orderValue) as gCMp,";
                break;
            case 'gCMe':
                $colsQuery .= " ROUND(((o.orderValue*o.ORVCM)/100)+((o.productionValue*o.prodValCM)/100)) as gCMe,";
                break;
            case 'oi':
                $colsQuery .= " DATEDIFF(o.offer, o.inquiry) as oi,";
                break;
            case 'oneClient':
                $colsQuery .= " COALESCE(NULLIF(c.clientLongName,''), k.clientLongName) as oneClient,";
                break;
            case 'InTL':
                $colsQuery .= " cre.`$col` AS TL,";
                break;
            case 'InKAM':
                $colsQuery .= " cre.`$col` AS KAM,";
                break;
            case 'InASM':
                $colsQuery .= " cre.`$col` AS ASM,";
                break;
            case 'InID':
                $colsQuery .= " `$col` AS ID,";
                break;
            case 'inPM':
                $colsQuery .= " `$col` AS PM,";
                break;
            case 'SOC':
                $colsQuery .= " `$col` as `AoC`,";
                break;
            case 'OVE':
                $colsQuery .= " ROUND(`$col`) AS OVE,";
                break;
            case 'OV':
                $colsQuery .= " ROUND(`$col`) AS OfV,";
                break;
            case 'GO':
                $colsQuery .= " `$col`,";
                break;
            case 'GET':
                $colsQuery .= " `$col`,";
                break;
            case 'orderValue':
                $colsQuery .= " ROUND(`$col`) AS OrV,";
                break;
            case 'order':
                $colsQuery .= " `$col` AS `orderDate`,";
                break;
            case 'WHP':
                $colsQuery .= " `$col` AS `PHP`,";
                break;
            case 'WHS':
                $colsQuery .= " `$col` AS `PHS`,";
                break;
            case 'offer':
                $colsQuery .= " `$col` AS `offerDate`,";
                break;
            case 'deliveryDate':
                $colsQuery .= " `$col` AS `deliveryDate`,";
                break;
            case 'request':
                $colsQuery .= " `$col` AS `Requested offer date`,";
                break;
            case 'requestedDeliveryDate':
                $colsQuery .= " `$col` AS `Requested delivery date`,";
                break;
            case 'inquiry':
                $colsQuery .= " `$col` AS `inquityDate`,";
                break;
            case 'nextContactDate':
                $colsQuery .= " o.`$col`,";
                break;
            case 'client':
                $colsQuery .= " c.`clientShortName` as Reseller,";
                break;
            case 'Company':
                $colsQuery .= " o.`$col`,";
                break;
            case 'shortNameEnd':
                $colsQuery .= " k.`clientShortName` AS `End client`,";
                break;
            case 'city':
                $colsQuery .= " c.`city` AS `Reseller city`,";
                break;
            case 'cityEnd':
                $colsQuery .= " k.`city` AS `End client city`,";
                break;
            case 'zip':
                $colsQuery .= " c.`zip` AS `ZIP R`,";
                break;
            case 'market':
                $colsQuery .= " c.market AS `Res. market`,";
                break;
            case 'marketE':
                $colsQuery .= " k.market AS `End. market`,";
                break;
            case 'Ezip':
                $colsQuery .= " k.`zip` AS `ZIP E`,";
                break;
            case 'segment':
                $colsQuery .= " REPLACE(REPLACE(REPLACE(segment, '{mniej}', 'S-'), '{wiecej}', 'B-'), '{srednica}', 'DN') as segment,";
                //$colsQuery.=" segment,";
                break;
            case 'oneClient':
                $colsQuery .= " COALESCE(NULLIF(c.clientLongName,''), k.clientLongName) as oneClient,";
                break;
            case 'oi':
                $colsQuery .= " DATEDIFF(o.offer, o.inquiry) as oi,";
                break;
            case 'offerNo':
                $colsQuery .= " o.offerNo,";
                break;
            case 'oldOfferNo':
                $colsQuery .= " o.oldOfferNo,";
                break;
            case 'projectName':
                $colsQuery .= " p.projectName,";
                break;
            case 'projectNo':
                $colsQuery .= " p.projectNo,";
                break;
            case 'InF':
                $colsQuery .= " o.InF,";
                break;
            case 'OT':
                $colsQuery .= " o.OT,";
                break;
            case 'reason':
                $colsQuery .= " o.reason,";
                break;
            case 'marketR':
                $colsQuery .= " c.market as `Reseller market`,";
                break;
            case 'country':
                $colsQuery .= " c.country as `Reseller country`,";
                break;
            case 'countryE':
                $colsQuery .= " k.country as `Endclient country`,";
                break;
            case 'scope':
                $colsQuery .= " o.scope,";
                break;
            case 'productionLocation':
                $colsQuery .= " o.productionLocation,";
                break;
            case 'calcPersons':
                $colsQuery .= " o.calcPersons,";
                break;
            case 'requestedOrderDate':
                $colsQuery .= " o.requestedOrderDate,";
                break;
            case 'lastComment':
                $colsQuery .= " o.lastComment,";
                break;
            case 'orderNo':
                $colsQuery .= " o.orderNo,";
                break;
            case 'productionOrderNo':
                $colsQuery .= " o.productionOrderNo,";
                break;
            case 'orderCompany':
                $colsQuery .= " o.orderCompany,";
                break;
            case 'comments':
                $colsQuery .= " comt.note AS comments,";
                break;
            default:
                $colsQuery .= " `$col`,";
                break;
        }
        $colLetter++;
    }
    $colsQuery = rtrim($colsQuery, ",");
    $query = "SELECT $colsQuery FROM offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id LEFT JOIN clientsresponsibility crr ON c.company=crr.company AND c.id=crr.clientId LEFT JOIN clientsresponsibility cre ON k.company=cre.company AND k.id=cre.clientId LEFT JOIN (SELECT DISTINCT(offerNo) as offerNo, MAX(revision) as revNo FROM `components` WHERE revision!=0 GROUP BY offerNo) com ON com.offerNo=o.offerNo LEFT JOIN comments comt ON o.id=comt.offerId WHERE 1 " . $_SESSION['searchQueryOffersCondition'];
    //echo $query;
    $link = connect();
    $result = $link->query($query);
    $filename = "offers" . date('Ymd') . ".xlsx";
    require("../libs/SimpleExcel/simpleExcel.php");
    $table = $result->fetch_all();
    $result = $link->query($query);
    $row = $result->fetch_assoc();
    array_unshift($table, array_keys($row));
    $xlsx = SimpleXLSXGen::fromArray($table);
    $xlsx->downloadAs($filename);
    exit;
}

function downloadClients($cols)
{
    $colsQuery = "";
    $colFormat = [];
    $colLetter = "A";
    foreach ($cols as $col) {
        switch ($col) {
            case 'id':
                $colsQuery .= " `$col`, ";
                break;
            case 'hitrate':
                $colsQuery .= " `$col`, ";
                break;
            case 'orders':
                $colsQuery .= " `$col`,";
                break;
            case 'offers':
                $colsQuery .= " `$col`,";
                break;
            case 'InTL':
                $colsQuery .= " (SELECT GROUP_CONCAT(inTL SEPARATOR ', ') FROM clientsresponsibility WHERE clientid=c.id) as InTL, ";
                break;
            case 'InKAM':
                $colsQuery .= " (SELECT GROUP_CONCAT(inKAM SEPARATOR ', ') FROM clientsresponsibility WHERE clientid=c.id) as InKAM, ";
                break;
            case 'InASM':
                $colsQuery .= " (SELECT GROUP_CONCAT(inASM SEPARATOR ', ') FROM clientsresponsibility WHERE clientid=c.id) as InASM, ";
                break;
            case 'ordersValue':
                $colsQuery .= " ROUND(`$col`) AS ordersValue,";
                break;
            case 'rei':
                $colsQuery .= " `$col` as CT,";
                break;
            case 'type':
                $colsQuery .= " `$col` as CC,";
                break;
            case 'offersValue':
                $colsQuery .= " ROUND(`$col`) AS offersValue,";
                break;
            case 'street':
                $colsQuery .= " `c`.`adres` as `street`, ";
                break;
            case 'street':
                $colsQuery .= " `c`.`adres` as `street`, ";
                break;
            default:
                $colsQuery .= " `$col`,";
                break;
        }
        $colLetter++;
    }
    $colsQuery = rtrim($colsQuery, ",");
    $query = "SELECT $colsQuery FROM clients c WHERE 1 " . $_SESSION['searchQueryClientsCondition'];
    $link = connect();
    $result = $link->query($query);
    $table = $result->fetch_all();
    /*
     * CHECK MySQLi error
     */
    if ($link->errno > 0) {
        //echo $link->error."<br/>";
        Session::showAdminMessage();
        echo "<p style=\"color:rgb(255,0,0);\">Application error occurring. <span style=\"color:rgb(0,0,0);\">Contact with administrators.</span></p>";
        exit;
    }
    $filename = "clients" . date('Ymd') . ".xlsx";
    require("../libs/SimpleExcel/simpleExcel.php");
    $result = $link->query($query);
    $row = $result->fetch_assoc();
    array_unshift($table, array_keys($row));
    $xlsx = SimpleXLSXGen::fromArray($table);
    $xlsx->downloadAs($filename);
    exit;
}

function downloadCompetitors($cols)
{
    $colsQuery = "";
    $colFormat = [];
    foreach ($cols as $col) {
        switch ($col) {
            case 'location':
                $colsQuery .= " REPLACE(location, ';', ' ') as locations, ";
                break;
            case 'VA41':
                $colsQuery .= " `$col` as `V&A (<DN4000)`, ";
                break;
            case 'VA42':
                $colsQuery .= " `$col` as `V&A (>DN4000)`,";
                break;
            case 'VAPTP':
                $colsQuery .= " `$col` as `V&A (Pure TP) `,";
                break;
            case 'VAFF':
                $colsQuery .= " `$col` as `V&A (FF) `,";
                break;
            case 'piping25600':
                $colsQuery .= " `$col` as `Piping (DN25-600)`,";
                break;
            case 'piping6001500':
                $colsQuery .= " `$col` as `Piping (DN600-1500)`,";
                break;
            case 'LLining':
                $colsQuery .= " `$col` as `L-Lining`,";
                break;
            case 'SLining':
                $colsQuery .= " `$col` as `S-Lining`,";
                break;
            case 'FPLining':
                $colsQuery .= " `$col` as `FP-Lining`,";
                break;
            case 'wetESP':
                $colsQuery .= " `$col` as `Wet-ESP`,";
                break;
            case 'ducts':
                $colsQuery .= " `$col` as `Ducts`,";
                break;
            case 'chimneyStacks':
                $colsQuery .= " `$col` as `Chimney & Stacks`,";
                break;
            case 'other':
                $colsQuery .= " `$col` as `Other`,";
                break;
            case 'installation':
                $colsQuery .= " `$col` as `Installation`,";
                break;
            case 'dailyService':
                $colsQuery .= " `$col` as `Daily service`,";
                break;
            case 'revamping':
                $colsQuery .= " `$col` as `Revamping`,";
                break;
            default:
                $colsQuery .= " `$col`,";
                break;
        }
    }
    $colsQuery = rtrim($colsQuery, ",");
    $query = "SELECT $colsQuery FROM competitors WHERE 1 "; //.$_SESSION['searchQueryCompetitorsCondition']; 
    $link = connect();
    $result = $link->query($query);
    $filename = "competitors" . date('Ymd') . ".xlsx";
    require("../libs/SimpleExcel/simpleExcel.php");
    $table = $result->fetch_all();
    $result = $link->query($query);
    $row = $result->fetch_assoc();
    array_unshift($table, array_keys($row));
    $xlsx = SimpleXLSXGen::fromArray($table);
    $xlsx->downloadAs($filename);
    exit;
}

function downloadProjects($cols)
{
    $colsQuery = "";
    $colFormat = [];
    foreach ($cols as $col) {
        switch ($col) {
            case 'creatorFull':
                $colsQuery .= " p.`$col` as `creator`, ";
                break;
            case 'clientLongName':
                $colsQuery .= " c.`$col`, ";
                break;
            default:
                $colsQuery .= " p.`$col`,";
                break;
        }
    }
    $colsQuery = rtrim($colsQuery, ",");
    $query = "SELECT $colsQuery FROM projects p LEFT JOIN clients c ON p.endClient=c.id WHERE 1 "; //.$_SESSION['searchQueryCompetitorsCondition']; 
    $link = connect();
    $result = $link->query($query);
    $filename = "projects" . date('Ymd') . ".xlsx";
    require("../libs/SimpleExcel/simpleExcel.php");
    $table = $result->fetch_all();
    $result = $link->query($query);
    $row = $result->fetch_assoc();
    array_unshift($table, array_keys($row));
    $xlsx = SimpleXLSXGen::fromArray($table);
    $xlsx->downloadAs($filename);
    exit;
}

function countSummary($col)
{
    if ($col == 'OVE') {
        $link = connect();
        $query = $_SESSION['searchQueryOffers'];
        $result = $link->query("SELECT SUM(OVE) as ile FROM (" . $query . ") t");
        $row = $result->fetch_object();
        return $row->ile;
    } else if ($col == "OV") {
        $link = connect();
        $query = $_SESSION['searchQueryOffers'];
        $result = $link->query("SELECT SUM(OV) as ile FROM (" . $query . ") t");
        $row = $result->fetch_object();
        return $row->ile;
    } else {
        $link = connect();
        $query = $_SESSION['searchQueryOffers'];
        $result = $link->query("SELECT SUM(OVgg) as ile FROM (" . $query . ") t");
        $row = $result->fetch_object();
        return $row->ile;
    }
}

function createOfferCopyFolders(int $id)
{
    $link = connect();

    $offer = getOfferInfo($id);

    $dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", str_replace("\\", "-", $offer['offerNo'] . "_" . getClientName($offer['client']) . "-" . getClientName($offer['endClient']) . "_" . $offer['scope']))))));

    $dirname = preg_replace('!\s+!', ' ', $dirname);

    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    $ip = $_SERVER['REMOTE_ADDR'];

    // Create folders for offer
    $folder_error = false;
    $parent_folder_created = mkdir("../../offers/$dirname");

    if (!$parent_folder_created) {
        $folder_error = true;
        $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
    }

    if (!$folder_error) {
        $companies = ["PP", "PG", "PTN", "TP", "PT"];

        foreach ($companies as $com) {
            $company_folder_created = mkdir("../../offers/$dirname/$com");

            if (!$company_folder_created) {
                $folder_error = true;
                $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
                continue;
            }

            $nested_folders = ["1 Inquiry", "2 Subsuplier", "3 Calculation", "4 Offer", "5 Order", "6 Others"];

            foreach ($nested_folders as $nested) {
                $nested_folder_created = mkdir("../../offers/$dirname/$com/$nested");

                if (!$nested_folder_created) {
                    $folder_error = true;
                    $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo " . $offer['offerNo'] . "', '$ip')");
                    continue;
                }
            }
        }
    }

    if ($parent_folder_created) {
        $link->query(sprintf(
            "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
            mysqli_real_escape_string($link, $dirname),
            mysqli_real_escape_string($link, $offer['offerNo'])
        ));
        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Created offer folder','Created offer folder $dirname','$ip')");
    }

    $link->close();

    if ($folder_error) {
        return false;
    }

    return true;
}

function createOfferCopy($offer)
{
    $link = connect();
    $lastOfferNo = lastOfferNo();
    $offerNo = "";
    if (date("m") >= 7) {
        $offerNoYear = substr($lastOfferNo, 0, 2);
        $sumThisYear = substr(date("Y") + 1, 2, 2);
        if ($offerNoYear != $sumThisYear)
            $offerNo = $sumThisYear . "700001";
        else {
            $offerNo = explode("-", $lastOfferNo);
            $offerNo = $offerNo[0] + 1;
        }
    } else {
        $offerNo = explode("-", $lastOfferNo);
        $offerNo = $offerNo[0] + 1;
    }


    $offerInfo = getOfferInfo(getOfferId($offer));
    $query = sprintf(
        "INSERT INTO `offers`(`offerNo`, `OT`, `inquiryNo`, `client`, `finalClient`, `orderLocation`, `productionLocation`, `plantLocationCountry`, `plantLocationCity`, `scope`, `oID`, `V`, `inquiry`, `OVE`, `OV`, `GO`, `GET`, `WHP`, `WHS`, `costPrice`, `BT`, `projectId`, `company`, `InR`, `InID`, `SOC`, `segment`, `step`, `requestedOrderDate`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
        mysqli_real_escape_string($link, $offerNo),
        mysqli_real_escape_string($link, $offerInfo['OT']),
        mysqli_real_escape_string($link, $offerInfo['inquiryNo']),
        mysqli_real_escape_string($link, $offerInfo['client']),
        mysqli_real_escape_string($link, $offerInfo['finalClient']),
        mysqli_real_escape_string($link, $offerInfo['orderLocation']),
        mysqli_real_escape_string($link, $offerInfo['productionLocation']),
        mysqli_real_escape_string($link, $offerInfo['plantLocationCountry']),
        mysqli_real_escape_string($link, $offerInfo['plantLocationCity']),
        mysqli_real_escape_string($link, $offerInfo['scope']),
        mysqli_real_escape_string($link, $offerInfo['oID']),
        mysqli_real_escape_string($link, $offerInfo['V']),
        mysqli_real_escape_string($link, date("Y-m-d")),
        mysqli_real_escape_string($link, $offerInfo['OVE']),
        mysqli_real_escape_string($link, $offerInfo['OV']),
        mysqli_real_escape_string($link, $offerInfo['GO']),
        mysqli_real_escape_string($link, $offerInfo['GET']),
        mysqli_real_escape_string($link, $offerInfo['WHP']),
        mysqli_real_escape_string($link, $offerInfo['WHS']),
        mysqli_real_escape_string($link, $offerInfo['costPrice']),
        mysqli_real_escape_string($link, $offerInfo['BT']),
        mysqli_real_escape_string($link, $offerInfo['projectId']),
        mysqli_real_escape_string($link, $offerInfo['company']),
        mysqli_real_escape_string($link, $offerInfo['InR']),
        mysqli_real_escape_string($link, $offerInfo['InID']),
        mysqli_real_escape_string($link, $offerInfo['SOC']),
        mysqli_real_escape_string($link, $offerInfo['segment']),
        mysqli_real_escape_string($link, 1),
        mysqli_real_escape_string($link, $offerInfo['requestedOrderDate']),
    );
    $link->query($query);
    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
    $ip = $_SERVER['REMOTE_ADDR'];
    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new offer','offerNo=" . $offerNo . ", OT=" . $offerInfo['OT'] . ", description=" . $offerInfo['scope'] . ", OVE=" . $offerInfo['OVE'] . ", IS=" . $offerInfo['oID'] . ", InIS=" . $offerInfo['InID'] . ", R=" . $offerInfo['V'] . ", InR=" . $offerInfo['InR'] . ", company=" . $offerInfo['company'] . ", inquiry=" . date("Y-m-d") . ",  requestedOrderDate=" . $offerInfo['requestedOrderDate'] . ", step=1, AoC=" . $offerInfo['SOC'] . "','$ip')");
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offer'");
    while ($row = $result->fetch_object()) {
        $cmp = getComponentInfo($row->id);
        $query = sprintf(
            "INSERT INTO `components`(`offerNo`, `position`, `revision`, `scope`, `segment`, `stat`, `prot`, `DN`, `m3`, `kg`, `pressure`, `medium`, `componentType`, `OV`, `GO`, `GET`, `WHP`, `WHS`, `costPrice`, `counts`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
            mysqli_real_escape_string($link, $offerNo),
            mysqli_real_escape_string($link, $cmp['position']),
            mysqli_real_escape_string($link, $cmp['revision']),
            mysqli_real_escape_string($link, $cmp['scope']),
            mysqli_real_escape_string($link, $cmp['segment']),
            mysqli_real_escape_string($link, $cmp['stat']),
            mysqli_real_escape_string($link, $cmp['prot']),
            mysqli_real_escape_string($link, $cmp['DN']),
            mysqli_real_escape_string($link, $cmp['m3']),
            mysqli_real_escape_string($link, $cmp['kg']),
            mysqli_real_escape_string($link, $cmp['pressure']),
            mysqli_real_escape_string($link, $cmp['medium']),
            mysqli_real_escape_string($link, $cmp['componentType']),
            mysqli_real_escape_string($link, $cmp['OV']),
            mysqli_real_escape_string($link, $cmp['GO']),
            mysqli_real_escape_string($link, $cmp['GET']),
            mysqli_real_escape_string($link, $cmp['WHP']),
            mysqli_real_escape_string($link, $cmp['WHS']),
            mysqli_real_escape_string($link, $cmp['costPrice']),
            mysqli_real_escape_string($link, 0)
        );
        $link->query($query);
        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new component','Added component with position " . $cmp['position'] . "-rev/" . $cmp['revision'] . " to offer number $offerNo','$ip')");
    }
    $tresc = "
New offer has been added to offer list.<br>
Offer number: $offerNo <br>
Inquiry number: " . date("Y-m-d") . " <br>
Responsibility (R): " . getNameAndSurname($offerInfo['V']) . "<br>
Inside Sales (IS): " . getNameAndSurname($offerInfo['oID']) . "<br><br>
Scope: " . $offerInfo['scope'] . "<br>
Client: " . getClientName($offerInfo['clientLocation']) . "<br>
City: " . getClientCity($offerInfo['clientLocation']) . "<br>";
    include('assets/libs/PHPMailer/mail.php');
    echo "<div class='hidden'>";
    if (!empty(getUserEmail($offerInfo['V'])))
        wyslijMaila(getUserEmail($offerInfo['V']), $tresc, 'New offer No ' . $offerNo . ' has been added.');
    if (!empty(getUserEmail($offerInfo['oID'])))
        wyslijMaila(getUserEmail($offerInfo['oID']), $tresc, 'New offer No ' . $offerNo . ' has been added.');
    echo "</div>";
    $link->close();
    if ($offerInfo['client'] != 0)
        countHitrates($offerInfo['client']);
    if ($offerInfo['finalClient'] != 0)
        countHitrates($offerInfo['finalClient']);
    return $offerNo;
}

function translateMarket($market)
{
    $output = "";
    if (strlen($market) != 0) {
        $market = explode(",", $market);
        foreach ($market as $m) {
            switch ($m) {
                case 'CH':
                    $output .= 'Chemical, ';
                    break;
                case 'SC':
                    $output .= 'Semiconductor, ';
                    break;
                case 'PW':
                    $output .= 'Power, ';
                    break;
                case 'ME':
                    $output .= 'Metal, ';
                    break;
                case 'PA':
                    $output .= 'Paper, ';
                    break;
                case 'RE':
                    $output .= 'Refinery, ';
                    break;
                case 'EW':
                    $output .= 'Energy from waste, ';
                    break;
                case 'WA':
                    $output .= 'Water, ';
                    break;
                case 'NN':
                    $output .= 'Not defined, ';
                    break;
                case 'FO':
                    $output .= 'Food, ';
                    break;
                case 'PR':
                    $output .= 'Projects, ';
                    break;
                case 'CN':
                    $output .= 'Construction, ';
                    break;
                case 'AD':
                    $output .= 'Administration, ';
                    break;
                case 'PD':
                    $output .= 'Production, ';
                    break;
            }
        }
        return substr($output, 0, -2);
    }
    return "";
}

function getProjectInfo($id)
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $query = sprintf(
        "SELECT * FROM projects WHERE id='%s'",
        mysqli_real_escape_string($link, strip_tags($id))
    );
    $result = $link->query($query);
    $row = $result->fetch_object();
    if ($row) {
        $project = array(
            'id' => ($row->id),
            'creator' => ($row->creator),
            'creatorFull' => ($row->creatorFull),
            'projectName' => ($row->projectName),
            'projectNo' => ($row->projectNo),
            'oldProjectNumber' => ($row->oldProjectNumber),
            'plantLocation' => ($row->plantLocation),
            'plantCountry' => ($row->plantCountry),
            'description' => ($row->description),
            'status' => ($row->status),
            'endClient' => ($row->endClient),
            'followUp' => ($row->followUp),
            'inF' => ($row->inF),
            'nextContactDate' => ($row->nextContactDate),
            'comment' => ($row->comment),
            'market' => ($row->market)
        );
        return $project;
    }
    return false;
}

function selectProjects()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM projects ORDER BY projectNo DESC");
    echo "<option value='' selected>Select project</option>";
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->projectNo) . " " . ($row->projectName) . "</option>";
    }
}

function listProjects()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM projects");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->projectName) . "</option>";
    }
}

function listProjectsNo()
{
    $link = connect();
    $link->query('SET NAMES utf8');
    $link->query('SET CHARACTER_SET utf8_unicode_ci');
    $result = $link->query("SELECT * FROM projects");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->projectNo) . "'>" . ($row->projectNo) . "</option>";
    }
}

function getCmpOffer($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM components WHERE id='$id'");
    $row = $result->fetch_object();
    return $row->offerNo;
}

function countSummaryOfferProdLoc($offerNo)
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(productionLocation) as productionLocation FROM components WHERE offerNo='$offerNo' AND counts=1");
    $locs = "";
    while ($row = $result->fetch_object()) {
        $locs .= ($row->productionLocation) . ",";
    }
    $locs = substr($locs, 0, -1);
    $link->query("UPDATE offers SET productionLocation='$locs' WHERE offerNo='$offerNo'");
    $link->close();
}

function summarySonPon($offer)
{
    $link = connect();
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offer' AND AX='Order'");
    $orderNs = "";
    $prodOrds = "";
    while ($row = $result->fetch_object()) {
        $orderNs .= $row->orderNo . ",";
        $prodOrds .= $row->orderNoProduction . ",";
    }
    $orderNs = substr($orderNs, 0, -1);
    $prodOrds = substr($prodOrds, 0, -1);
    $link->query(sprintf(
        "UPDATE IGNORE offer SET orderNo='%s', productionOrderNo='%s' WHERE offerNo='%s'",
        mysqli_real_escape_string($link, $orderNs),
        mysqli_real_escape_string($link, $prodOrds),
        mysqli_real_escape_string($link, $offer)
    ));
    $link->close();
}

function checkOfferToSave($offer)
{
    $link = connect();
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offer' AND AX=''");
    $done = 1;
    while ($row = $result->fetch_object()) {
        if ($row->technicalDone == 0)
            $done = 0;
        if ($row->comercialDone == 0)
            $done = 0;
    }
    if ($done == 0)
        return $done;
    else {
        $result = $link->query("SELECT * FROM offers WHERE offerNo='$offer'");
        $row = $result->fetch_object();
        if ($row->step != 1)
            return 0;
        else {
            $result = $link->query("SELECT COUNT(*) as ile FROM components WHERE offerNo='$offer'");
            $row = $result->fetch_object();
            if ($row->ile == 0)
                return 0;
            else
                return 1;
        }
    }
}

/**
 * Generates HTML table rows for offer components.
 *
 * This function fetches components for a given offer from the database
 * and generates the corresponding HTML table rows with controls for updating
 * their status.
 *
 * @param mysqli $dbConnection The database connection object.
 * @param string $offerNumber The offer number to look up.
 * @return void This function outputs HTML directly.
 */
function conclusion(string $offerNumber): void
{
    // Prepare the SQL statement to prevent SQL injection
    $query = "SELECT id, position, revision, scope, orderNo, AX, reason FROM components WHERE offerNo = ? AND counts = '1'";

    $dbConnection = connect();
    $stmt = $dbConnection->prepare($query);

    // Check if statement was prepared successfully
    if ($stmt === false) {
        // Handle error, e.g., log it or display a generic message
        error_log("Failed to prepare statement: " . $dbConnection->error);
        echo '<tr><td colspan="5">An error occurred. Please try again later.</td></tr>';
        return;
    }

    $stmt->bind_param("s", $offerNumber);
    $stmt->execute();
    $result = $stmt->get_result();

    $axStatusOptions = ["Order", "Lost", "Terminated"];

    while ($row = $result->fetch_object()) {
        // Sanitize output to prevent XSS
        $position = htmlspecialchars($row->position, ENT_QUOTES, 'UTF-8');
        $scope = htmlspecialchars($row->scope, ENT_QUOTES, 'UTF-8');
        $orderNo = htmlspecialchars($row->orderNo, ENT_QUOTES, 'UTF-8');
        $componentId = (int) $row->id;

        // Build revision string if applicable
        $revision = ($row->revision != 0) ? "-rev/" . htmlspecialchars($row->revision, ENT_QUOTES, 'UTF-8') : "";

        // Build the AX status dropdown
        $axSelect = sprintf('<select class="form-control select2 AXselect" name="%dax" id="%dax">', $componentId, $componentId);
        $axSelect .= '<option value="">Open</option>'; // "Open" is the default, not selected
        foreach ($axStatusOptions as $status) {
            $selected = ($row->AX === $status) ? ' selected' : '';
            $axSelect .= sprintf('<option value="%s"%s>%s</option>', $status, $selected, $status);
        }
        $axSelect .= '</select>';

        // Build the reason dropdown
        $reasonValue = $row->reason;
        $reasonOptions = reasonListConc($reasonValue); // Assuming reasonListConc is safe and returns sanitized HTML
        $disabled = empty($reasonValue) ? 'disabled' : '';
        $reasonSelect = sprintf('<select %s class="form-control select2" name="%dreason">%s</select>', $disabled, $componentId, $reasonOptions);

        // Output the final table row
        echo "<tr>";
        echo "<td>{$position}{$revision}</td>";
        echo "<td>{$scope}</td>";
        echo "<td>{$orderNo}</td>";
        echo '<td><input type="hidden" value="' . $componentId . '" name="cmpIds[]">' . $axSelect . '</td>';
        echo "<td>{$reasonSelect}</td>";
        echo "</tr>";
    }

    $stmt->close();
}

/**
 * Example of how to call the improved function.
 *
 * Note: The connect() function should be called outside and the
 * connection object passed into the conclusion() function.
 */
function exampleUsage()
{
    $link = connect(); // Assuming connect() returns a mysqli object
    $offerId = $_GET['offer']; // Example offer ID from a request
    conclusion($link, $offerId);
    $link->close();
}



function listPMs($pm = '')
{
    $link = connectUsers();
    $output = '<option value="" selected>Select PM</option>';
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listPM='1' AND u.isActive='1' ORDER BY u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        if ($pm == $row->id)
            $output .= "<option value='" . ($row->id) . "' selected>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
        else
            $output .= "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    return $output;
}

function listPMsFilter($showall = "")
{
    $output = "";
    if ($showall == "") {
        $output = '<option value="all" selected>Show all</option>';
        $output .= '<option value="empty">Empty values</option>';
    } else
        $output .= '<option value="empty">Empty values</option>';
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listPM='1' AND u.isActive='1' ORDER BY u.nazwisko ASC");
    while ($row = $result->fetch_object()) {
        $output .= "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    }
    return $output;
}

function activeComponents($offer)
{
    $link = connect();
    $result = $link->query("SELECT COUNT(*) as suma FROM components WHERE offerNo='$offer' AND AX=''");
    $row = $result->fetch_object();
    return $row->suma;
}

function statsChart1($query)
{
    $link = connect();
    $ov = [];
    $ovgg = [];
    $Sov = [];
    $Sovgg = [];
    $dates = [];
    $m = date("m");
    $y = date("Y");
    $fyd1 = "";
    $fyd2 = "";
    $firstM = 1;
    if ($m <= 6) {
        $y--;
        $m = 1;
        $fyd1 = "Jul-" . substr($y, 2, 2);
        $fyd2 = "Jul-" . substr($y + 1, 2, 2);
    } else {
        $m = 1;
        $fyd1 = "Jul-" . substr($y, 2, 2);
        $fyd2 = "Jul-" . substr($y + 1, 2, 2);
    }
    for ($i = 1; $i <= 24; $i++) {
        if ($m == 13) {
            $m = 1;
            $y++;
        }
        $pomOV = 0;
        $pomOVgg = 0;
        if ($firstM == 1) {
            $result = $link->query("SELECT ROUND(SUM(OV)) as OV, ROUND(SUM(OVgg)) as OVgg FROM stats WHERE (MONTH(`orderDate`)='$m' AND YEAR(`orderDate`)='$y') $query");
            $d1 = ($y - 1) . "-07-01";
            $d2 = ($y - 1) . "-12-31";
            $pomResult = $link->query("SELECT ROUND(SUM(OV)) as OV, ROUND(SUM(OVgg)) as OVgg FROM stats WHERE orderDate BETWEEN '$d1' AND '$d2' $query");
            $pomRow = $pomResult->fetch_object();
            $pomOV = $pomRow->OV;
            $pomOVgg = $pomRow->OVgg;
            $firstM = 0;
        } else
            $result = $link->query("SELECT ROUND(SUM(OV)) as OV, ROUND(SUM(OVgg)) as OVgg FROM stats WHERE (MONTH(`orderDate`)='$m' AND YEAR(`orderDate`)='$y') $query");
        $row = $result->fetch_object();
        array_push($ov, $row->OV);
        array_push($ovgg, $row->OVgg);
        if ($m == 7) {
            array_push($Sov, ($row->OV));
            array_push($Sovgg, ($row->OVgg));
        } else {
            array_push($Sov, end($Sov) + ($row->OV) + $pomOV);
            array_push($Sovgg, end($Sovgg) + ($row->OVgg) + $pomOVgg);
        }
        array_push($dates, substr(translateMonths($m), 0, 3) . "-" . substr($y, 2, 2));
        $m++;
    }
    echo "
		var canvas = document.getElementById('statsChart1');
		var data = {
			labels: [";
    foreach ($dates as $date) {
        echo "'" . $date . "',";
    }
    echo "],
			datasets: [
				{
					label: 'OVE',
					backgroundColor: 'rgba(68, 114, 196, 0.2)',
					borderColor: 'rgb(68, 114, 196)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(68, 114, 196, 0.4)',
					hoverBorderColor: 'rgb(68, 114, 196)',
					data: [";
    foreach ($ov as $ove) {
        echo "'" . $ove . "',";
    }
    echo "],
				},
				{
					hidden: true,
					label: 'OVEgg',
					backgroundColor: 'rgba(127, 127, 127, 0.2)',
					borderColor: 'rgb(127, 127, 127)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(127, 127, 127, 0.4)',
					hoverBorderColor: 'rgb(127, 127, 127)',
					data: [";
    foreach ($ovgg as $ovegg) {
        echo "'" . $ovegg . "',";
    }
    echo "],
				},
				{
					hidden: true,
					label: '&Sigma; OVE',
					backgroundColor: 'rgba(68, 114, 196, 0.2)',
					borderColor: 'rgb(68, 114, 196)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(68, 114, 196, 0.4)',
					hoverBorderColor: 'rgb(68, 114, 196)',
					data: [";
    foreach ($Sov as $Sove) {
        echo "'" . $Sove . "',";
    }
    echo "],
				},
				{
					hidden: true,
					label: '&Sigma; OVEgg',
					backgroundColor: 'rgba(127, 127, 127, 0.2)',
					borderColor: 'rgb(127, 127, 127)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(127, 127, 127, 0.4)',
					hoverBorderColor: 'rgb(127, 127, 127)',
					data: [";
    foreach ($Sovgg as $Sovegg) {
        echo "'" . $Sovegg . "',";
    }
    echo "],
				}
			]
		};
		var option = {
			'onClick' : function (evt, item) {
				try{
					var month = item[0]['_model'].label
					chart1showtablefunction(month);
					$.session.set('ch1miesiac', month);
					$.session.set('case', '1');
				}
				catch(error){}
			},
			tooltips: {
				callbacks: {
					label: function(tooltipItem) {
						return tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')+' k€';
					}
				}
			},
			scales: {
				xAxes: [ {
					scaleLabel: {
						display: true,
						labelString: 'Order date'
					}
				} ],
				yAxes: [
					{
						ticks: { 
							beginAtZero: true,
							callback: function(label, index, labels) {
								return (Math.round(label*100)/100).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')+' k€';
							}
						},
						scaleLabel: {
							display: true,
							labelString: 'Offer / Order value'
						},
					}
				]
			}, 
			animation: 
			{
				duration:1000
			},
			annotation: {
				annotations: 
				[
					{
						type: 'line',
						border: [10,10],
						mode: 'vertical',
						scaleID: 'x-axis-0',
						value: '" . $fyd1 . "',
						borderColor: 'red', 
						borderWidth: 1,
					},
					{
						type: 'line',
						border: [10,10],
						mode: 'vertical',
						scaleID: 'x-axis-0',
						value: '" . $fyd2 . "',
						borderColor: 'red', 
						borderWidth: 1,
					}
				]
			},
			title: {
				display: false,
				text: 'Orders value'
			},
			legend: {
				display: false
			 },
		};


		var statsChart1 = Chart.Bar(canvas,{
			data:data,
			options:option
		});
	";
}

function statsChart2($query)
{
    $link = connect();
    $count = [];
    $ov = [];
    $Scount = [];
    $Sov = [];
    $dates = [];
    $m = date("m");
    $y = date("Y");
    $fyd1 = "";
    $fyd2 = "";
    $firstM = 1;
    if ($m <= 6) {
        $y--;
        $m = 1;
        $fyd1 = "Jul-" . substr($y, 2, 2);
        $fyd2 = "Jul-" . substr($y + 1, 2, 2);
    } else {
        $m = 1;
        $fyd1 = "Jul-" . substr($y, 2, 2);
        $fyd2 = "Jul-" . substr($y + 1, 2, 2);
    }
    for ($i = 1; $i <= 24; $i++) {
        if ($m == 13) {
            $m = 1;
            $y++;
        }
        $pomCount = 0;
        $pomOV = 0;
        if ($firstM == 1) {
            $result = $link->query("SELECT ROUND(SUM(OV)) as OV, COUNT(DISTINCT(offerNo)) as ile FROM stats WHERE (MONTH(inquiryDate)='$m' AND YEAR(inquiryDate)='$y') $query");
            $d1 = ($y - 1) . "-07-01";
            $d2 = ($y - 1) . "-12-31";
            $pomResult = $link->query("SELECT ROUND(SUM(OV)) as OV, COUNT(DISTINCT(offerNo)) as ile FROM stats WHERE inquiryDate BETWEEN '$d1' AND '$d2' $query");
            $pomRow = $pomResult->fetch_object();
            $pomCount = $pomRow->ile;
            $pomOV = $pomRow->OV;
            $firstM = 0;
        } else
            $result = $link->query("SELECT ROUND(SUM(OV)) as OV, COUNT(DISTINCT(offerNo)) as ile FROM stats WHERE (MONTH(inquiryDate)='$m' AND YEAR(inquiryDate)='$y') $query");
        $row = $result->fetch_object();
        array_push($ov, $row->OV);
        array_push($count, $row->ile);
        if ($m == 7) {
            array_push($Scount, ($row->ile));
            array_push($Sov, ($row->OV));
        } else {
            array_push($Scount, end($Scount) + ($row->ile) + $pomCount);
            array_push($Sov, end($Sov) + ($row->OV) + $pomOV);
        }
        array_push($dates, substr(translateMonths($m), 0, 3) . "-" . substr($y, 2, 2));
        $m++;
    }
    echo "
		var canvas = document.getElementById('statsChart2');
		var data = {
			labels: [";
    foreach ($dates as $date) {
        echo "'" . $date . "',";
    }
    echo "],
			datasets: [
				{
					label: 'Amount',
					backgroundColor: 'rgba(68, 114, 196, 0.2)',
					borderColor: 'rgb(68, 114, 196)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(68, 114, 196, 0.4)',
					hoverBorderColor: 'rgb(68, 114, 196)',
					data: [";
    foreach ($count as $ct) {
        echo "'" . $ct . "',";
    }
    echo "],
				},
				{
					hidden:true,
					label: 'OVE',
					backgroundColor: 'rgba(127, 127, 127, 0.2)',
					borderColor: 'rgb(127, 127, 127)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(127, 127, 127, 0.4)',
					hoverBorderColor: 'rgb(127, 127, 127)',
					data: [";
    foreach ($ov as $ove) {
        echo "'" . $ove . "',";
    }
    echo "],
				},
				{
					hidden:true,
					label: 'Amount',
					backgroundColor: 'rgba(68, 114, 196, 0.2)',
					borderColor: 'rgb(68, 114, 196)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(68, 114, 196, 0.4)',
					hoverBorderColor: 'rgb(68, 114, 196)',
					data: [";
    foreach ($Scount as $Sct) {
        echo "'" . $Sct . "',";
    }
    echo "],
				},
				{
					hidden:true,
					label: 'OVE',
					backgroundColor: 'rgba(127, 127, 127, 0.2)',
					borderColor: 'rgb(127, 127, 127)',
					borderWidth: 2,
					hoverBackgroundColor: 'rgba(127, 127, 127, 0.4)',
					hoverBorderColor: 'rgb(127, 127, 127)',
					data: [";
    foreach ($Sov as $Sove) {
        echo "'" . $Sove . "',";
    }
    echo "],
				}
			]
		};
		var option = {
			'onClick' : function (evt, item) {
				try{
					var month = item[0]['_model'].label
					chart2showtablefunction(month);
					$.session.set('ch2miesiac', month);
					$.session.set('case', '2');
				}
				catch(error){}
			},
			tooltips: {
				callbacks: {
					label: function(tooltipItem) {
						if(data.datasets[tooltipItem.datasetIndex].label=='OVE')
							return tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ')+' k€';
						else
							return 'Amount: '+tooltipItem.yLabel.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
					}
				}
			},
			animation: 
			{
				duration:1000
			},
			annotation: {
				annotations: 
				[
					{
						type: 'line',
						border: [10,10],
						mode: 'vertical',
						scaleID: 'x-axis-0',
						value: '" . $fyd1 . "',
						borderColor: 'red', 
						borderWidth: 1,
					},
					{
						type: 'line',
						border: [10,10],
						mode: 'vertical',
						scaleID: 'x-axis-0',
						value: '" . $fyd2 . "',
						borderColor: 'red', 
						borderWidth: 1,
					}
				]
			},
			title: {
				display: false,
				text: 'Inquiries per month'
			},
			legend: {
				display: false
			},
			scales: {
				xAxes: [ {
					scaleLabel: {
						display: true,
						labelString: 'Inquiry date'
					}
				} ],
			  yAxes: [{
				type: 'linear',
				position: 'left',
				scaleLabel: {
					display: true,
					labelString: 'Amount'
				},
				ticks: { 
					beginAtZero: true,
					callback: function(label, index, labels) {
						return (Math.round(label*100)/100).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
					}
				}
			  }]
			}
		};


		var statsChart2 = Chart.Bar(canvas,{
			data:data,
			options:option
		});
	";
}

function sumCalcPersons($cmp)
{
    $link = connect();
    $cmp = getComponentInfo($cmp);
    $offer = $cmp['offerNo'];
    $result = $link->query("SELECT * FROM components WHERE offerNo='$offer' AND AX=''");
    $persons = ";";
    while ($row = $result->fetch_object()) {
        $persons .= ($row->calcPerson) . ";";
    }
    $link->query("UPDATE offers SET calcPersons='$persons' WHERE offerNo='$offer'");
    $link->close();
}

function listDistinctOffers()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(offerNo) as offerNo FROM offers");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->offerNo) . "'>" . ($row->offerNo) . "</option>";
    }
}

function getProjectName($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM projects WHERE id='$id'");
    $row = $result->fetch_object();
    if ($row) {
        return $row->projectName;
    }
    return false;
}

function listClientsWithCities()
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE isDeleted='0'");
    while ($row = $result->fetch_object()) {
        echo "<option value='" . ($row->id) . "'>" . ($row->clientShortName) . "/" . ($row->city) . "</option>";
    }
}

function getOpenComponentsNumber($offerNo)
{
    $link = connect();
    $result = $link->query("SELECT COUNT(*) as suma FROM components WHERE AX='' AND offerNo='$offerNo'");
    $row = $result->fetch_object();
    return $row->suma;
}

function listComments($id, $show_removed = true)
{
    $link = connect();

    $query = "SELECT * FROM comments WHERE offerId='$id' ORDER BY removed ASC, id DESC";

    if ($show_removed === false) {
        $query = "SELECT * FROM comments WHERE offerId='$id' AND removed=0 ORDER BY id DESC";
    }

    $result = $link->query($query);
    $output = "";

    while ($row = $result->fetch_object()) {
        $comment_id = $row->id; // Use the comment ID for actions
        $timestamp = substr($row->timestamp, 0, 10);
        $name = htmlspecialchars($row->name, ENT_QUOTES, 'UTF-8');
        $surname = htmlspecialchars($row->surname, ENT_QUOTES, 'UTF-8');
        $note = nl2br(htmlspecialchars($row->note, ENT_QUOTES, 'UTF-8'));
        $is_removed = $row->removed == 1; // Check if the comment is removed

        // If the comment is removed, add a strike-through style
        $note_style = $is_removed ? "text-decoration: line-through;" : "";
        $buttons = "";

        if (!$is_removed) {
            $buttons = "<button class='btn btn-danger btn-xxs float-end ml-1 comment-del' data-id='" . $comment_id . "' data-offer_id='" . $id . "' title='Delete Comment'>
                            <i class='fas fa-trash'></i>
                        </button>
                        <button class='btn btn-primary btn-xxs float-end ml-1 comment-edit' data-id='" . $comment_id . "' data-offer_id='" . $id . "' title='Edit Comment'>
                            <i class='fas fa-edit'></i>
                        </button>";
        }

        $output .= "<div class='row' style='margin:0;'>
                        <strong>$timestamp | $name $surname</strong>
                        $buttons
                    </div>";
        $output .= "<div class='' style='margin:0; $note_style' id='note-$comment_id'>$note</div><hr>";
    }

    echo $output;

    $link->close(); // Close the database connection
}



function listCommentsClient($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM commentsClient WHERE clientId='$id'");
    while ($row = $result->fetch_object()) {
        echo "<div class='row' style='margin:0;'><strong>" . substr(($row->timestamp), 0, 10) . " | " . ($row->name) . " " . ($row->surname) . "</strong></div>";
        echo "<div class='row' style='margin:0;'>" . ($row->note) . "</div><hr>";
    }
    $link->close();
}

function listCommentsProject($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM commentsProject WHERE projectId='$id'");
    while ($row = $result->fetch_object()) {
        echo "<div class='row' style='margin:0;'><strong>" . substr(($row->timestamp), 0, 10) . " | " . ($row->name) . " " . ($row->surname) . "</strong></div>";
        echo "<div class='row' style='margin:0;'>" . ($row->note) . "</div><hr>";
    }
    $link->close();
}

function listCommentsCompetitor($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM commentsCompetitor WHERE competitorId='$id'");
    while ($row = $result->fetch_object()) {
        echo "<div class='row' style='margin:0;'><strong>" . substr(($row->timestamp), 0, 10) . " | " . ($row->name) . " " . ($row->surname) . "</strong></div>";
        echo "<div class='row' style='margin:0;'>" . ($row->note) . "</div><hr>";
    }
    $link->close();
}

function countHitrates($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM clients WHERE id='$id'");
    $row = $result->fetch_object();
    $rei = $row->rei;
    if ($rei != "E")
        $hitrates = $link->query("SELECT (SELECT (SELECT SUM(OVE) FROM offers WHERE client='$id' AND AX='Order')*100/((SELECT SUM(OVE) FROM offers WHERE client='$id'))) as hitrate, (SELECT COUNT(*) FROM offers WHERE client='$id' AND AX='Order') as iloscA, (SELECT SUM(OVE) FROM offers WHERE AX='Order' AND client='$id') as wartoscA, (SELECT COUNT(*) FROM offers WHERE client='$id') as ilosc, (SELECT SUM(OVE) FROM offers WHERE client='$id') as wartosc FROM clients WHERE id='$id'");
    else
        $hitrates = $link->query("SELECT (SELECT (SELECT SUM(OVE) FROM offers WHERE finalClient='$id' AND AX='Order')*100/((SELECT SUM(OVE) FROM offers WHERE finalClient='$id'))) as hitrate, (SELECT COUNT(*) FROM offers WHERE finalClient='$id' AND AX='Order') as iloscA, (SELECT SUM(OVE) FROM offers WHERE AX='Order' AND finalClient='$id') as wartoscA, (SELECT COUNT(*) FROM offers WHERE finalClient='$id') as ilosc, (SELECT SUM(OVE) FROM offers WHERE finalClient='$id') as wartosc FROM clients WHERE id='$id'");
    $resrow = $hitrates->fetch_object();
    $hitrate = round($resrow->hitrate);
    $orders = round($resrow->iloscA);
    $offers = round($resrow->ilosc);
    $ordersValue = round(($resrow->wartoscA), 2);
    $offersValue = round(($resrow->wartosc), 2);
    $link->query("UPDATE clients SET hitrate='$hitrate', orders='$orders', offers='$offers', ordersValue='$ordersValue', offersValue='$offersValue' WHERE id='$id'");
    $link->close();
}

function exportKontakty()
{
    $link = connect();
    $filename = "clientContacts.xlsx";
    require_once "../libs/PHPExcel/PHPExcel.php";
    $excel = new PHPExcel();
    $file = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
    $excel->createSheet(1);
    $query = "SELECT c.*, k.* FROM clientsContacts c JOIN clients k ON c.clientId=k.id ORDER BY c.clientId ASC";
    $result = $link->query($query);
    $excel->setActiveSheetIndex(1)
        ->setCellValue("A1", "ID")
        ->setCellValue("B1", "Team leader")
        ->setCellValue("C1", "Client short name")
        ->setCellValue("D1", "Client category")
        ->setCellValue("E1", "Cantact Name")
        ->setCellValue("F1", "Contact Surname")
        ->setCellValue("G1", "Contact e-mail")
        ->setCellValue("H1", "Contact phone");
    $i = 2;
    while ($row = $result->fetch_object()) {
        $excel->setActiveSheetIndex(1)
            ->setCellValue("A" . $i, $row->id)
            ->setCellValue("B" . $i, $row->InTL)
            ->setCellValue("C" . $i, $row->clientShortName)
            ->setCellValue("D" . $i, $row->type)
            ->setCellValue("E" . $i, $row->name)
            ->setCellValue("F" . $i, $row->surname)
            ->setCellValue("G" . $i, $row->email)
            ->setCellValue("H" . $i, $row->phone1);
        $i++;
    }
    $file = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
    $file->save($filename);
    $link->close();
    header("Location: download.php?name=$filename");
}

function circleStatus($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM offers WHERE id='$id'");
    $row = $result->fetch_object();
    $step = 1;
    if (!empty($row->offer)) {
        $step = 2;
        if (!empty($row->nextContactDate)) {
            $step = 3;
            if (!empty($row->order)) {
                $step = 4;
                if (!empty($row->deliveryDate))
                    $step = 5;
            }
        }
    }
    $link->query("UPDATE offers SET step='$step' WHERE id='$id'");
    $link->close();
}

function countStep($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM offers WHERE id='$id'");
    $row = $result->fetch_object();
    $offerNo = getOfferNo($id);
    $res = $link->query("SELECT COUNT(*) as ile FROM components WHERE AX!='' AND offerNo='$offerNo'");
    $resrow = $res->fetch_object();
    $step = 1;
    if ($row->offer != '0000-00-00') {
        $step = 2;
        if ($row->nextContactDate != '0000-00-00') {
            $step = 3;
            if ($row->order != '0000-00-00' && $resrow->ile != 0) {
                $step = 4;
                if ($row->deliveryDate != '0000-00-00') {
                    $step = 5;
                    if ($row->serviceStart != '0000-00-00' && $row->serviceEnd != '0000-00-00')
                        $step = 6;
                }
            }
        }
    }
    $link->query("UPDATE offers SET step='$step' WHERE id='$id'");
    $link->close();
}

function ifOneOrderStatus($offer)
{
    $link = connect();
    $result = $link->query("SELECT COUNT(*) as ile FROM components WHERE offerNo='$offer' AND AX='Order'");
    $row = $result->fetch_object();
    if ($row->ile != 0)
        return true;
    else
        return false;
}

function listClientCntactsOffer($id)
{
    $link = connect();
    $result = $link->query("SELECT * FROM contacts WHERE offerId='$id' ORDER BY id DESC");
    if ($result->num_rows != 0) {
        $i = 1;
        while ($row = $result->fetch_object()) {
            $last = 0;
            if ($i == 1)
                $last = 1;
            $output = "<hr><div class='row'><div class='col-lg-3'><strong>" . getNameAndSurname($row->contact) . "</strong>&nbsp;<button class='btn btn-success btn-edit' onclick='edit(" . ($row->id) . ", " . $last . ")' data-toggle='modal' data-target='#edit'>Edit</button></div><div class='col-lg-3'><strong>" . ($row->contactDate) . "</strong></div></div>";
            $output .= "<div class='row'><div class='col-lg-12' style='padding:10px !important;'>Contact with <strong>" . getContactNameAndSurname($row->contactWith) . "</strong></div></div>";
            $output .= "<div class='row'><div class='col-lg-12' style='padding:10px !important;'>" . str_replace("\n", "<br>", $row->note) . "</div></div>";
            echo $output;
            $i++;
        }
    } else
        echo "<div class='row justify-content-center'>No contacts with client done yet.</div>";
}

function formatSizeUnits($bytes)
{
    if ($bytes >= 1073741824) {
        $bytes = number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        $bytes = number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        $bytes = number_format($bytes / 1024, 2) . ' KB';
    } elseif ($bytes > 1) {
        $bytes = $bytes . ' bytes';
    } elseif ($bytes == 1) {
        $bytes = $bytes . ' byte';
    } else {
        $bytes = '0 bytes';
    }
    return $bytes;
}

function getFolderSize($directory)
{
    $totalSize = 0;
    $directoryArray = scandir($directory);

    foreach ($directoryArray as $key => $fileName) {
        if ($fileName != ".." && $fileName != ".") {
            if (is_dir($directory . "/" . $fileName)) {
                $totalSize = $totalSize + getFolderSize($directory . "/" . $fileName);
            } else if (is_file($directory . "/" . $fileName)) {
                $totalSize = $totalSize + filesize($directory . "/" . $fileName);
            }
        }
    }
    return $totalSize;
}

function getCmpPos($id)
{
    $link = connect();
    $result = $link->query("SELECT position, revision FROM components WHERE id='$id'");
    $row = $result->fetch_object();
    return ($row->position) . "-rev/" . ($row->revision);
}

function deleteDir($dirPath)
{
    if (!is_dir($dirPath)) {
        throw new InvalidArgumentException("$dirPath must be a directory");
    }
    if (substr($dirPath, strlen($dirPath) - 1, 1) != '/') {
        $dirPath .= '/';
    }
    $files = glob($dirPath . '*', GLOB_MARK);
    foreach ($files as $file) {
        if (is_dir($file)) {
            deleteDir($file);
        } else {
            unlink($file);
        }
    }
    rmdir($dirPath);
}

function dateFormat($date)
{
    $dateFormat = $_SESSION['plasticonDigitalUser']['dateFormat'];
    if ($dateFormat == "")
        $dateFormat = "Y-m-d";
    if ($date == "---")
        return $date;
    if (!empty($date) && $date != "0000-00-00" && strlen($date) > 10) {
        $date = date_create($date);
        return date_format($date, $dateFormat . " H:i:s");
    } else if (!empty($date) && $date != "0000-00-00") {
        $date = date_create($date);
        return date_format($date, $dateFormat);
    } else
        return "";
}

function hasRightsForProject($project, $user)
{
    $user = getUserInfo($user);
    $project = getProjectInfo($project);
    if ($user['projectManagement'] == 1) {
        if ($user['externalCompany'] == 1) {
            if ($user['id'] == $project['creator'] || $user['id'] == $project['followUp'])
                return true;
            else
                return false;
        } else
            return true;
    } else
        return false;
}

function hasRightsForOffer($offer, $user)
{
    $user = getUserInfo($user);
    $offer = getOfferInfo($offer);
    if ($user['externalCompany'] == 1) {
        if ($user['id'] == $offer['oID'] || $user['id'] == $offer['V'] || $user['id'] == $offer['pm'] || $user['id'] == $offer['F'] || strpos($offer['calcPersons'], ";" . $user['id'] . ";") != FALSE)
            return true;
        else
            return false;
    } else
        return true;
}

function hasRightsForClient($client, $user)
{
    $user = getUserInfo($user);
    $client = getClientInfo($client);
    if ($user['clientManagement'] == 1) {
        if ($user['externalCompany'] == 1) {
            if ($user['id'] == $client['kam'] || $user['id'] == $client['asm'] || $user['id'] == $client['tl'] || $user['id'] == $client['followUp'])
                return true;
            else
                return false;
        } else
            return true;
    } else
        return false;
}

function translateSegment2($seg)
{
    switch ($seg) {
        case 'V-Vessles/Apparatus ({mniej}{srednica}4000mm)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">v</span>';
            break;
        case 'V-Vessles/Apparatus ({wiecej}{srednica}4000mm)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">V</span>';
            break;
        case 'V-Vessles/Apparatus (Pure TP)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">TP</span>';
            break;
        case 'V-Vessles/Apparatus (FF)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">F</span>';
            break;
        case 'P-Process Piping ({srednica}25-600mm)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">p</span>';
            break;
        case 'P-Pipelines ({srednica}600mm-1500mm)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">P</span>';
            break;
        case 'L-Loose lining':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">L</span>';
            break;
        case 'L-Sheet lining':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">S</span>';
            break;
        case 'L-Fixpoint lining':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">X</span>';
            break;
        case 'Sp-Wet ESP':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">W</span>';
            break;
        case 'Sp-Ducts':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">D</span>';
            break;
        case 'Sp-Chimney & Stacks':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">C</span>';
            break;
        case 'Sp-Other':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">O</span>';
            break;
        case 'Si-Installation':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">SI</span>';
            break;
        case 'Si-Daily service (Inspection, repairs)':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">SD</span>';
            break;
        case 'Si-Revamping':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">SR</span>';
            break;
        case 'Si-Shutdowns & Turnarounds':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">SS</span>';
            break;
        case 'OM-Spare parts':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">OS</span>';
            break;
        case 'OM-Trade components':
            return '<span title="' . str_replace("{wiecej}", ">", str_replace("{mniej}", "<", str_replace("{srednica}", "&#8960;", ($seg)))) . '">OT</span>';
            break;
    }
}
function translateSegment($seg)
{
    $url = "https://gce.plasticon.app/public/img/gce/segments/";
    $segTab = [
        'Tanks/Apparatus ≤4000mm' => ['Tanks/Apparatus ≤4000mm', '1.png'],
        'Tanks/Apparatus >4000mm GRP' => ['Tanks/Apparatus >4000mm GRP', '2.png'],
        'Tanks/Apparatus >4000mm Dual' => ['Tanks/Apparatus >4000mm Dual', '3.png'],
        'Pure TP'  => ['Pure TP', '4.png'],
        'Fully fluorinated liner/GRP' => ['Fully fluorinated liner/GRP', '5.png'],
        'Process Piping 25-600mm GRP' => ['Process Piping 25-600mm GRP', '6.png'],
        'Pipelines 601mm-1500mm GRP' => ['Pipelines 601mm-1500mm GRP', '7.png'],
        'Loose lining' => ['Loose lining', '8.png'],
        'Sheet lining' => ['Sheet lining', '9.png'],
        'Fixpoint lining' => ['Fixpoint lining', '10.png'],
        'Wet ESP' => ['Wet ESP', '11.png'],
        'Ducts round' => ['Ducts round', '12.png'],
        'Chimney & Stacks ≤4000mm' => ['Chimney & Stacks ≤4000mm', '13.png'],
        'Other' => ['Other', '14.png'],
        'Installation' => ['Installation', '15.png'],
        'Daily service' => ['Daily service', '16.png'],
        'Framework Contracts' => ['Framework Contracts', '17.png'],
        'Revamping' => ['Revamping', '18.png'],
        'Shutdowns & Turnarounds' => ['Shutdowns & Turnarounds', '19.png'],
        'Spare parts' => ['Spare parts', '20.png'],
        'External Trade Components' => ['External Trade Components', '21.png'],
        'Change order' => ['Change order', '22.png'],
        'Storage tank GRP ≤4000mm' => ['Storage tank GRP ≤4000mm', '23.png'],
        'Storage tank GRP 3D ≤4000mm' => ['Storage tank GRP 3D ≤4000mm', '24.png'],
        'Storage tank Dual ≤4000mm' => ['Storage tank Dual ≤4000mm', '25.png'],
        'Pressure vessel ≤4000mm' => ['Pressure vessel ≤4000mm', '26.png'],
        'Scrubber GRP ≤4000mm' => ['Scrubber GRP ≤4000mm', '27.png'],
        'Scrubber Dual ≤4000mm' => ['Scrubber Dual ≤4000mm', '28.png'],
        'Rectangular vessels' => ['Rectangular vessels', '29.png'],
        'Tanks/Apparatus >4000mm hand lay-up and assembly on site GRP' => ['Tanks/Apparatus >4000mm hand lay-up and assembly on site GRP', '30.png'],
        'Tanks/Apparatus >4000mm hand lay-up and assembly on site Dual' => ['Tanks/Apparatus >4000mm hand lay-up and assembly on site Dual', '31.png'],
        'Ducts rectangular' => ['Ducts rectangular', '33.png'],
        'Chimney & Stacks >4000mm' => ['Chimney & Stacks >4000mm', '34.png'],
        'Process Piping 25-600mm Dual' => ['Process Piping 25-600mm Dual', '35.png'],
        'Pipelines 601mm-1500mm Dual' => ['Pipelines 601mm-1500mm Dual', '36.png'],
        'Tanks/Apparatus >4000mm GRP  3D' => ['Tanks/Apparatus >4000mm GRP  3D', '37.png']
    ];


    if (isset($segTab[$seg][0])) {
        return '<span title="' . $segTab[$seg][0] . '"><img style="width: 15px;" src="' . $url . $segTab[$seg][1] . '"></span>';
    }

    if ($seg == "") {
        return "";
    }

    return '<span title="Unknown segmnet(' . $seg . ')">?</span>';
}

function getFYyears()
{
    $link = connect();
    $result = $link->query("SELECT DISTINCT(YEAR(offer)) as fy FROM `offers` WHERE offer!='0000-00-00' HAVING fy<=(YEAR(CURDATE())+1) AND fy>2000 ORDER BY fy DESC");
    $pom = 0;
    $output = "";
    while ($row = $result->fetch_object()) {
        if ($pom == 0) {
            if (date("m") >= 7)
                $output .= "<option value='" . ($row->fy + 1) . "'>FY" . substr(($row->fy + 1), 2, 2) . "</option>";
            $output .= "<option value='" . ($row->fy) . "'>FY" . substr(($row->fy), 2, 2) . "</option>";
            $pom = 1;
        } else
            $output .= "<option value='" . ($row->fy) . "'>FY" . substr(($row->fy), 2, 2) . "</option>";
    }
    $link->close();
    return $output;
}

function selectResponsible()
{
    $link = connectUsers();
    $result = $link->query("SELECT u.* FROM crm c JOIN users u ON c.userId=u.id WHERE c.listV='1' ORDER BY u.nazwisko ASC");
    while ($row = $result->fetch_object())
        echo "<option value='" . ($row->id) . "'>" . ($row->imie) . " " . ($row->nazwisko) . "</option>";
    $link->close();
}

function getCompanies()
{
    $link = connectUsers();
    $result = $link->query("SELECT * FROM companies");
    $link->close();
    $output = "";
    while ($row = $result->fetch_object())
        $output .= "<option value='" . $row->shortcut . "'>" . $row->name . "</option>";
    return $output;
}

function recalculateFinancialOMS($id, $OV)
{
    $crm = connect();
    $result = $crm->query(sprintf(
        "SELECT * FROM paymentconditions WHERE crmArticleId='%s'",
        mysqli_real_escape_string($crm, $id)
    ));
    while ($row = $result->fetch_object()) {
        $percent = $row->percentage;
        $value = ROUND($OV / 100 * $percent) * 1000;
        $crm->query(sprintf(
            "UPDATE paymentconditions SET value='%s' WHERE id='%s'",
            mysqli_real_escape_string($crm, $value),
            mysqli_real_escape_string($crm, $row->id)
        ));
    }
    $crm->close();
}

function kosztaOrders($zlec)
{
    if ($zlec == 22222)
        return '*201';
    if ($zlec == 10000)
        return '*108';
    if ($zlec == 66666)
        return '*108';
    if ($zlec == 88880)
        return '*108';
    if ($zlec == 88888)
        return '*205';
    if ($zlec == 33333)
        return '*305';
    if ($zlec == '00156')
        return '*103';
    if ($zlec == "200000/1")
        return '*200';
    if ($zlec == "K200001")
        return '*100';
    $spec = substr($zlec, 0, 2);
    if ($spec == '00')
        return '*102';
    if (strlen($zlec) == 6) {
        $zlec = intval(substr($zlec, 2, 4));
        if ($zlec >= 3000 && $zlec <= 3300)
            return '*103';
        if ($zlec >= 3301 && $zlec <= 3700)
            return '*104';
        if ($zlec >= 3701 && $zlec <= 3999)
            return '*103';
        if ($zlec >= 7001 && $zlec <= 7300)
            return '*103';
        if ($zlec >= 7301 && $zlec <= 7700)
            return '*104';
        if ($zlec >= 7701 && $zlec <= 7999)
            return '*103';
        if ($zlec >= 0 && $zlec <= 300)
            return '*101';
        if ($zlec >= 301 && $zlec <= 700)
            return '*102';
        if ($zlec >= 701 && $zlec <= 999)
            return '*101';
        if ($zlec >= 6001 && $zlec <= 6300)
            return '*106';
        if ($zlec >= 6301 && $zlec <= 6700)
            return '*106';
        if ($zlec >= 6701 && $zlec <= 6999)
            return '*106';
        if ($zlec >= 9000 && $zlec <= 9200)
            return '*105';
        if ($zlec >= 9201 && $zlec <= 9300)
            return '*105';
        if ($zlec >= 9301 && $zlec <= 9500)
            return '*105';
        if ($zlec >= 9601 && $zlec <= 9700)
            return '*109';
        if ($zlec >= 9701 && $zlec <= 9800)
            return '*109';
        if ($zlec >= 9801 && $zlec <= 9900)
            return '*110';
        if ($zlec >= 9901 && $zlec <= 9999)
            return '*110';
        return 'blad';
    } else {
        return 'blad';
    }
}

function checkIfOfferNoExistsInOms($offer_no, $production_company, $sales_company = "")
{

    $num_rows = 0;

    $oms = connectOMS();

    $query = sprintf(
        "SELECT sa.id FROM salesarticles sa WHERE sa.offerNo='%s' AND sa.PC = '%s'",
        mysqli_real_escape_string($oms, $offer_no),
        mysqli_real_escape_string($oms, $production_company)
    );

    if (!empty($sales_company)) {
        $query = sprintf(
            "SELECT sa.id FROM salesarticles sa 
            LEFT JOIN projects p ON sa.offerNo = p.offerNo 
            WHERE sa.offerNo='%s' 
            AND sa.PC = '%s' 
            AND p.SC = '%s'",
            mysqli_real_escape_string($oms, $offer_no),
            mysqli_real_escape_string($oms, $production_company),
            mysqli_real_escape_string($oms, $sales_company)
        );
    }

    $result = $oms->query($query);

    if ($result) {
        $num_rows = mysqli_num_rows($result);
        $result->free();
    }

    $oms->close();

    return $num_rows > 0;
}


function checkIfOfferSonExistsInOms($son, $production_company, $sales_company = "")
{

    // Exclude NV00 from the check as per Katarzyna request
    $excluded_sons = ["NV00"];
    if (in_array($son, $excluded_sons)) {
        return false;
    }

    $num_rows = 0;

    $oms = connectOMS();

    $query = sprintf(
        "SELECT sa.id FROM salesarticles sa WHERE sa.SON='%s' AND sa.PC = '%s'",
        mysqli_real_escape_string($oms, $son),
        mysqli_real_escape_string($oms, $production_company)
    );

    if (!empty($sales_company)) {
        $query = sprintf(
            "SELECT sa.id FROM salesarticles sa 
            LEFT JOIN projects p ON sa.offerNo = p.offerNo 
            WHERE sa.SON='%s' 
            AND sa.PC = '%s' 
            AND p.SC = '%s'",
            mysqli_real_escape_string($oms, $son),
            mysqli_real_escape_string($oms, $production_company),
            mysqli_real_escape_string($oms, $sales_company)
        );
    }

    $result = $oms->query($query);

    $num_rows = mysqli_num_rows($result);

    $oms->close();

    if ($num_rows > 0) {
        return true;
    }

    return false;
}

function checkIfOfferPonExistsInOms($pon, $production_company, $sales_company = "")
{

    $num_rows = 0;

    $oms = connectOMS();

    $query = sprintf(
        "SELECT sa.id FROM salesarticles sa WHERE sa.PON='%s' AND sa.PC = '%s'",
        mysqli_real_escape_string($oms, $pon),
        mysqli_real_escape_string($oms, $production_company)
    );

    if (!empty($sales_company)) {
        $query = sprintf(
            "SELECT sa.id FROM salesarticles sa 
            LEFT JOIN projects p ON sa.offerNo = p.offerNo 
            WHERE sa.PON='%s' 
            AND sa.PC = '%s' 
            AND p.SC = '%s'",
            mysqli_real_escape_string($oms, $pon),
            mysqli_real_escape_string($oms, $production_company),
            mysqli_real_escape_string($oms, $sales_company)
        );
    }

    $result = $oms->query($query);

    $num_rows = mysqli_num_rows($result);

    $oms->close();

    if ($num_rows > 0) {
        return true;
    }

    return false;
}

function getOmsOfferNoUsingPonSon($pon = "", $son = "", $production_company = "", $sales_company = "")
{

    $oms = connectOMS();

    $query = "SELECT sa.offerNo, sa.id FROM salesarticles sa";
    $conditions = [];

    if (!empty($sales_company)) {
        $query .= " LEFT JOIN projects p ON sa.offerNo = p.offerNo";
    }

    if (!empty($pon)) {
        $conditions[] = sprintf("sa.PON='%s'", mysqli_real_escape_string($oms, $pon));
    }

    if (!empty($son)) {
        $conditions[] = sprintf("sa.SON='%s'", mysqli_real_escape_string($oms, $son));
    }

    if (!empty($production_company)) {
        $conditions[] = sprintf("sa.PC = '%s'", mysqli_real_escape_string($oms, $production_company));
    }

    if (!empty($sales_company)) {
        $conditions[] = sprintf("p.SC = '%s'", mysqli_real_escape_string($oms, $sales_company));
    }

    if (!empty($conditions)) {
        $query .= " WHERE " . implode(' AND ', $conditions);
    }

    $result = $oms->query($query);

    $response = [];

    while ($row = $result->fetch_assoc()) {
        $id = $row['id'];
        $response[$id] = $row['offerNo'];
    }

    $oms->close();

    return $response;
}


function checkIfOfferPonExistsInCrm($pon, $production_company, $sales_company = "")
{

    $num_rows = 0;

    $link = connect();

    $query = sprintf(
        "SELECT o.id FROM offers o WHERE o.productionOrderNo LIKE '%%%s%%' AND o.productionLocation LIKE '%%%s%%'",
        mysqli_real_escape_string($link, $pon),
        mysqli_real_escape_string($link, $production_company)
    );

    if (!empty($sales_company)) {
        $query .= sprintf(" AND o.company = '%s'", mysqli_real_escape_string($link, $sales_company));
    }

    $result = $link->query($query);

    if ($result) {
        $num_rows = mysqli_num_rows($result);
        $result->free();
    }

    $link->close();

    return $num_rows > 0;
}


function checkIfOfferSonExistsInCrm($son, $production_company, $sales_company = "")
{

    $num_rows = 0;

    $link = connect();

    $query = sprintf(
        "SELECT o.id FROM offers o WHERE o.orderNo LIKE '%%%s%%' AND o.productionLocation LIKE '%%%s%%'",
        mysqli_real_escape_string($link, $son),
        mysqli_real_escape_string($link, $production_company)
    );

    if (!empty($sales_company)) {
        $query .= sprintf(" AND o.company = '%s'", mysqli_real_escape_string($link, $sales_company));
    }

    $result = $link->query($query);

    if ($result) {
        $num_rows = mysqli_num_rows($result);
        $result->free();
    }

    $link->close();

    return $num_rows > 0;
}


function getCrmOfferNoUsingPonSon($pon = "", $son = "", $production_company, $sales_company = "")
{

    $link = connect();

    $query = "SELECT o.offerNo FROM offers o WHERE ";

    $conditions = [];

    if (!empty($pon)) {
        $conditions[] = sprintf("o.productionOrderNo LIKE '%%%s%%'", mysqli_real_escape_string($link, $pon));
    }

    if (!empty($son)) {
        $conditions[] = sprintf("o.orderNo LIKE '%%%s%%'", mysqli_real_escape_string($link, $son));
    }

    $conditions[] = sprintf("o.productionLocation LIKE '%%%s%%'", mysqli_real_escape_string($link, $production_company));

    // Add condition for sales_company only if it's provided
    if (!empty($sales_company)) {
        $conditions[] = sprintf("o.company = '%s'", mysqli_real_escape_string($link, $sales_company));
    }

    // Construct the final query
    if (!empty($conditions)) {
        $query .= implode(' AND ', $conditions);
    }

    $result = $link->query($query);

    $response = [];

    while ($row = $result->fetch_assoc()) {
        $id = getOfferId($row['offerNo']);
        $response[$id] = $row['offerNo'];
    }

    $link->close();

    return $response;
}

function array_insert(&$array, $position, $insert)
{
    if (is_int($position)) {
        array_splice($array, $position, 0, $insert);
    } else {
        $pos = array_search($position, array_keys($array));
        $array = array_merge(
            array_slice($array, 0, $pos),
            $insert,
            array_slice($array, $pos)
        );
    }
}

function productsExportQueryFrom($query)
{

    return $query . " FROM components com LEFT JOIN offers o ON com.offerNo=o.offerNo LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id LEFT JOIN projects p ON o.projectid = p.id ";
}

function productsExportQueryConditions($query)
{
    $query .= "WHERE 1 ";
    $query .= $_SESSION['searchQueryOffersCondition'];
    return $query;
}

// Function to build the entire query
function buildProductsExportQuery()
{

    $query_columns = [
        "com.offerNo",
        "o.orderNo",
        "com.segment",
        "com.componentType",
        "com.scope",
        "com.m3",
        "com.DN",
        "com.kg",
        "com.stat",
        "com.prot",
        "com.pressure",
        "com.medium",
        "com.productionLocation",
        "c.market",
        "c.clientShortName as client_name",
        "k.clientShortName as final_client_name ",
        "o.AX",
        "o.order",
        "o.offer",
        "o.OV",
        "o.OVE"
    ];

    $query = "SELECT " . implode(", ", $query_columns) . " ";

    $query = productsExportQueryFrom($query);

    $query = productsExportQueryConditions($query);

    return $query;
}

function productsExportSpreadsheetDef()
{

    $headers_def = [
        "A" => "Product",
        "B" => "Description",
        "C" => "DN [mm]",
        "D" => "Volume [m3]",
        "E" => "Static material",
        "F" => "Protection layer",
        "G" => "Weight [kg]",
        "H" => "Pressure",
        "I" => "Medium",
        "J" => "Market",
        "K" => "Production location",
        "L" => "Assembly",
        "M" => "Client",
        "N" => "Final client"
    ];

    $merge_range_def = [
        "A1:N1",
        "A2:B2",
        "C2:D2",
        "E2:G2",
    ];

    $width_def = [
        "A" => 40,
        "B" => 40,
        "C" => 10,
        "D" => 10,
        "E" => 10,
        "F" => 15,
        "G" => 15,
        "H" => 15,
        "I" => 15,
        "J" => 15,
        "K" => 15,
        "L" => 10,
        "M" => 20,
        "N" => 20
    ];


    return [
        "heders_def" => $headers_def,
        "merge_range_def" => $merge_range_def,
        "width_def" => $width_def
    ];
}

function marketOptionsMap()
{
    return [
        "CH" => "Chemical",
        "EW" => "Energy from waste",
        "ME" => "Metal",
        "NN" => "Not defined",
        "PA" => "Paper",
        "PW" => "Power",
        "RE" => "Refinery",
        "SC" => "Semiconductor",
        "WA" => "Water",
        "FO" => "Food",
        "PR" => "Projects",
        "CN" => "Construction",
        "AD" => "Administration",
        "PD" => "Production"
    ];
}

function productionLocationMap()
{
    return [
        "PTN" => "Plasticon the Netherlands",
        "TP" => "Thermopol",
        "PP" => "Plasticon Poland",
        "PG" => "Plasticon Germany",
        "PT" => "Plasto-Tec",
        "TBV" => "Thermopol Belgium",
        "TNV" => "Thermopol the Netherlands",
        "PSS" => "Plasticon Site Services",
    ];
}

function getProductsExportAssemblyData($offerNo, $oms_conn)
{
    return $oms_conn->query("SELECT ps.stage, ps.start, ps.end FROM salesarticles sa  LEFT JOIN productionstages ps ON sa.id=ps.articleId WHERE sa.offerNo='" . $offerNo . "'");
}

function downloadProductsCsv()
{
    $query = buildProductsExportQuery();
    $link = connect();
    $result = $link->query($query);
    $num_rows = $result->num_rows;

    if ($num_rows > 0) {
        $filename = "products" . date('Ymd') . ".csv";

        $tmp_dir = __DIR__ . '/../../tmp/';
        $full_path = $tmp_dir . $filename;

        $file = fopen($full_path, 'w');

        // Column headers

        $headers = [
            "CRM offer number",
            "Segment",
            "Description",
            "DN [mm]",
            "Volume [m3]",
            "Static material",
            "Protection layer",
            "Weight [kg]",
            "Pressure",
            "Medium",
            "Market",
            "Production location",
            "Client",
            "Final client",
            "Order number",
            "Status",
            "Order date",
            "Offer date ",
            "Quote value [k€]",
            "Order value estimated [k€]",
        ];

        fputcsv($file, $headers);

        $market_options = marketOptionsMap();
        $production_location_options = productionLocationMap();

        $oms = connectOMS();

        while ($row = $result->fetch_assoc()) {

            $segment = str_replace(["{wiecej}", "{mniej}", "{srednica}"], [">", "<", "Ø"], $row['segment']);
            $pressure = str_replace(["{wiecej}", "{mniej}", "{wiecejrowne}"], [">", "<", ">="], $row['pressure']);

            $market = isset($market_options[$row['market']]) ? $market_options[$row['market']] : "--";
            $production_location = isset($production_location_options[$row['productionLocation']]) ? $production_location_options[$row['productionLocation']] : $row['productionLocation'];


            $product = $segment;
            if (!empty($row['componentType'])) {
                $product .= " - " . $row['componentType'];
            }

            $data = [
                $row['offerNo'],
                !empty($product) ? $product : '--',
                !empty($row['scope']) ? $row['scope'] : '--',
                !empty($row['DN']) ? $row['DN'] : '--',
                !empty($row['m3']) ? $row['m3'] : '--',
                !empty($row['stat']) ? $row['stat'] : '--',
                !empty($row['prot']) ? $row['prot'] : '--',
                !empty($row['kg']) ? $row['kg'] : '--',
                !empty($pressure) ? $pressure : '--',
                !empty($row['medium']) ? $row['medium'] : '--',
                !empty($market) ? $market : '--',
                !empty($production_location) ? $production_location : '--',
                !empty($row['client_name']) ? $row['client_name'] : '--',
                !empty($row['final_client_name']) ? $row['final_client_name'] : '--',
                !empty($row['orderNo']) ? $row['orderNo'] : '--',
                !empty($row['AX']) ? $row['AX'] : 'Open',
                $row['order'] != "0000-00-00" ? $row['order'] : '--',
                $row['offer'] != "0000-00-00" ? $row['offer'] : '--',
                !empty($row['OV']) ? $row['OV'] : '--',
                !empty($row['OVE']) ? $row['OVE'] : '--'
            ];

            fputcsv($file, $data);
        }

        fclose($file);

        $oms->close();

        header("Location: download.php?name=" . $full_path);
    } else {
        header("Location: " . CRM_LINK . "?products_export=0");
    }
}

function downloadProducts()
{
    $query = buildProductsExportQuery();

    $link = connect();
    $result = $link->query($query);
    $link->close();

    $num_rows = $result->num_rows;

    if ($num_rows > 0) {

        require_once(__DIR__ . "/../libs/phpspreadsheet/autoload.php");

        $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial')->setSize(12);

        $spreadsheet->setActiveSheetIndex(0);
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->getSheetView()->setZoomScale(80);

        $sheet->setTitle('Products');

        $spreadsheet_def = productsExportSpreadsheetDef();

        $market_options = marketOptionsMap();
        $production_location_options = productionLocationMap();

        $oms = connectOMS();

        $data_row = 4;

        while ($row = $result->fetch_assoc()) {

            $oms_data = getProductsExportAssemblyData($row['offerNo'], $oms);

            // Apply replacements to the segment column
            $segment = str_replace(["{wiecej}", "{mniej}", "{srednica}"], [">", "<", "Ø"], $row['segment']);
            $pressure = str_replace(["{wiecej}", "{mniej}", "{wiecejrowne}"], [">", "<", ">="], $row['pressure']);

            $market = "--";
            if (isset($market_options[$row['market']])) {
                $market = $market_options[$row['market']];
            }

            $production_location = $row['productionLocation'];
            if (isset($production_location_options[$row['productionLocation']])) {
                $production_location = $production_location_options[$row['productionLocation']];
            }

            $assembly = "--";
            if ($oms_data->num_rows) {
                foreach ($oms_data as $item) {

                    if ($assembly != "--") {
                        break;
                    }

                    if (!empty($item['stage']) && $item['stage'] == "Assembly") {
                        if ($item['start'] && $item['end'] != "0000-00-00") {
                            $today = date("Y-m-d");
                            if ($item['start'] <= $today && $item['end'] <= $today) {
                                $assembly = "Yes";
                            } else {
                                $assembly = "No";
                            }
                        } else {
                            $assembly = "No";
                        }
                    } else {
                        $assembly = "No";
                    }
                }
            }

            // Construct the product column
            $product = $segment;
            if (!empty($row['componentType'])) {
                $product .= " - " . $row['componentType'];
            }

            $sheet->setCellValue('A' . $data_row, !empty($product) ? $product : '--');
            $sheet->getStyle("A" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('B' . $data_row, !empty($row['scope']) ? $row['scope'] : '--');
            $sheet->getStyle("B" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('C' . $data_row, !empty($row['DN']) ? $row['DN'] : '--');
            $sheet->getStyle("C" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle("C" . $data_row)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER);

            $sheet->setCellValue('D' . $data_row, !empty($row['m3']) ? $row['m3'] : '--');
            $sheet->getStyle("D" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle("D" . $data_row)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_00);

            $sheet->setCellValue('E' . $data_row, !empty($row['stat']) ? $row['stat'] : '--');
            $sheet->getStyle("E" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('F' . $data_row, !empty($row['prot']) ? $row['prot'] : '--');
            $sheet->getStyle("F" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('G' . $data_row, !empty($row['kg']) ? $row['kg'] : '--');
            $sheet->getStyle("G" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
            $sheet->getStyle("G" . $data_row)->getNumberFormat()->setFormatCode(PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_NUMBER_00);

            $sheet->setCellValue('H' . $data_row, !empty($pressure) ? $pressure : '--');
            $sheet->getStyle("H" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('I' . $data_row, !empty($row['medium']) ? $row['medium'] : '--');
            $sheet->getStyle("I" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('J' . $data_row, !empty($market) ? $market : '--');
            $sheet->getStyle("J" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('K' . $data_row,  !empty($production_location) ? $production_location : '--');
            $sheet->getStyle("K" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('L' . $data_row,  !empty($assembly) ? $assembly : '--');
            $sheet->getStyle("L" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('M' . $data_row,  !empty($row['client_name']) ? $row['client_name'] : '--');
            $sheet->getStyle("M" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $sheet->setCellValue('N' . $data_row,  !empty($row['final_client_name']) ? $row['final_client_name'] : '--');
            $sheet->getStyle("N" . $data_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);

            $data_row++;
        }

        $oms->close();

        // Column headers
        $headers_colums = $spreadsheet_def["heders_def"];

        $headers_row = 3;
        foreach ($headers_colums as $k => $val) {
            $sheet->setCellValue($k . $headers_row, $val);
        }

        // First level headers
        $sheet->setCellValue("C2", "Dimensions");
        $sheet->getStyle("C2")->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        $sheet->setCellValue("E2", "Materials and weight");
        $sheet->getStyle("E2")->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

        // Alignment
        $sheet->getStyle("A" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle("B" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle("C" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("D" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("E" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_RIGHT);
        $sheet->getStyle("F" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);
        $sheet->getStyle("G" . $headers_row)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);


        // Dimensions
        $spreadsheet->getActiveSheet()->getRowDimension(1)->setRowHeight(120);
        $width_def = $spreadsheet_def["width_def"];
        foreach ($width_def as $k => $val) {
            $spreadsheet->getActiveSheet()->getColumnDimension($k)->setWidth($val);
        }

        // Styles 
        $fill = $sheet->getStyle('A' . $headers_row . ':N' . $headers_row)->getFill();
        $fill->setFillType(PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('427FB9');
        $sheet->getStyle('A' . $headers_row . ':N' . $headers_row)->getFont()->setSize(12)->setBold(true)->getColor()->setRGB('FFFFFF');

        $fill = $sheet->getStyle('C2:G2')->getFill();
        $fill->setFillType(PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('427FB9');
        $sheet->getStyle('C2:G2')->getFont()->setSize(12)->setBold(true)->getColor()->setRGB('FFFFFF');

        $range = 'A1:N' . $sheet->getHighestRow();
        $sheet->getStyle($range)->getBorders()->getAllBorders()->setBorderStyle(PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);

        $sheet->getStyle("A")->getAlignment()->setWrapText(true);
        $sheet->getStyle("B")->getAlignment()->setWrapText(true);
        $sheet->getStyle("K")->getAlignment()->setWrapText(true);
        $sheet->getStyle("M")->getAlignment()->setWrapText(true);
        $sheet->getStyle("N")->getAlignment()->setWrapText(true);

        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        $sheet->getStyle("A4:" . $highestColumn . $highestRow)->getAlignment()->setVertical(PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        $merge_range_def = $spreadsheet_def["merge_range_def"];
        foreach ($merge_range_def as $range) {
            $sheet->mergeCells($range);
        }

        // Logo

        $logo_path = __DIR__ . "/../../files/lp.jpg";
        if (file_exists($logo_path)) {
            $drawing = new PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
            $drawing->setName('Plasticon Poland Logo');
            $drawing->setDescription('Plasticon Poland Logo');
            $drawing->setPath($logo_path);
            $drawing->setCoordinates('A1');
            $drawing->setWorksheet($spreadsheet->getActiveSheet());
        }

        $filename = "products" . date('Ymd') . ".xlsx";
        $tmp_dir = __DIR__ . '/../../tmp/';
        $full_path = $tmp_dir . $filename;

        $writer = PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');

        $writer->save($full_path);

        header("Location: download_products.php?p_xlsx_name=" . $filename);
    } else {
        header("Location: " . CRM_LINK . "?products_export=0");
    }
}

function offersInvalidSegment()
{

    return false;

    $link = connect();

    $query = "SELECT o.offerNo, o.segment AS offer_segment, GROUP_CONCAT(com.segment ORDER BY com.segment SEPARATOR ';') AS component_segment
    FROM offers o
    LEFT JOIN components com ON o.offerNo = com.offerNo AND com.counts = 1
    GROUP BY o.offerNo, o.segment
    ORDER BY o.offerNo DESC";

    $result = $link->query($query);

    $invalid_offers = array();

    while ($row = $result->fetch_assoc()) {
        $offerNo = $row['offerNo'];
        $offer_segment = trim($row['offer_segment'], ';');
        $component_segment = trim($row['component_segment'], ';');

        $offer_segments = explode(';', $offer_segment);
        $component_segments = explode(';', $component_segment);

        sort($offer_segments);
        sort($component_segments);

        /* 
        * Exclude cases like this:
        * Offer segment: V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({wiecej}{srednica}4000mm GRP);Sp-Ducts;Sp-Other;
        * Component segment: Sp-Ducts;Sp-Other;Sp-Other;V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({mniej}{srednica}4000mm);V-Vessles/Apparatus ({wiecej}{srednica}4000mm GRP) 
        * Several components with same segment, in offer there should be unique from each component
        */
        if (count(array_unique($offer_segments)) === count(array_unique($component_segments)) && array_diff($offer_segments, $component_segments) === array_diff($component_segments, $offer_segments)) {
            continue;
        }

        $invalid_offers[] = array(
            'offerNo' => $offerNo,
            'offer_segment' => $row['offer_segment'],
            'component_segment' => $row['component_segment']
        );
    }

    $link->close();

    require_once(__DIR__ . "/../libs/phpspreadsheet/autoload.php");

    $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();
    $spreadsheet->getDefaultStyle()->getFont()->setName('Arial')->setSize(12);

    $spreadsheet->setActiveSheetIndex(0);
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->getSheetView()->setZoomScale(80);

    $sheet->setTitle('Offers');

    $sheet->setCellValue('A2', 'Offer No');
    $sheet->setCellValue('B2', 'Offer Segment');
    $sheet->setCellValue('C2', 'Component Segment');
    $sheet->setCellValue('A1', 'Count invalid');
    $sheet->setCellValue('B1', count($invalid_offers));


    $data_row = 3;

    foreach ($invalid_offers as $offer) {
        $sheet->setCellValue('A' . $data_row, trim($offer['offerNo']));
        $sheet->setCellValue('B' . $data_row, $offer['offer_segment']);
        $sheet->setCellValue('C' . $data_row, $offer['component_segment']);
        $data_row++;
    }

    $sheet->getColumnDimension('A')->setAutoSize(true);
    $sheet->getColumnDimension('B')->setAutoSize(true);
    $sheet->getColumnDimension('C')->setAutoSize(true);

    $highestRow = $sheet->getHighestRow();
    $highestColumn = $sheet->getHighestColumn();

    $sheet->getStyle("A1:" . $highestColumn . $highestRow)->getAlignment()->setHorizontal(PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT);


    $filename = "offers_invalid_segment" . date('Ymd') . ".xlsx";

    $writer = PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');

    $writer->save($filename);

    header("Location: download.php?name=" . $filename . "");
}

// Transform clients data to be used in offers filters list select as id as option value and name as option text.
function clientIdNameForSelect(&$data)
{
    if (isset($data['client']) && !empty($data['client'])) {
        $client_id = $data['client'];
        $client_name = getClientName($client_id);
        $data['client_name'] = $client_id . " | " . $client_name;
    }

    if (isset($data['endClient']) && !empty($data['endClient'])) {
        $end_client_id = $data['endClient'];
        $end_client_name = getClientName($end_client_id);
        $data['end_client_name'] = $end_client_id . " | " . $end_client_name;
    }
}
function removeFoldersOmsExport($folders)
{

    if (!empty($folders)) {

        foreach (array_reverse($folders) as $item_path) {

            if (!empty($item_path)) {

                if (is_dir($item_path)) {

                    $items = array_diff(scandir($item_path), ['.', '..']);

                    foreach ($items as $item) {

                        $sub_item_path = "$item_path/$item";

                        if (is_dir($sub_item_path)) {
                            removeFoldersOmsExport([$sub_item_path]);
                        } else {
                            unlink($sub_item_path);
                        }
                    }

                    rmdir($item_path);
                }
            }
        }
    }
}


function createFoldersOmsExport($dirname)
{

    if (empty($dirname)) {
        return false;
    }

    $created_folders = [];

    $folder_ok = true;

    $orders_folder_path = "../../orders";

    if (!is_dir($orders_folder_path)) {
        if (!mkdir($orders_folder_path)) {
            $folder_ok = false;
        } else {
            $created_folders[] = $orders_folder_path;
        }
    }

    $dir_path = "$orders_folder_path/$dirname";

    if ($folder_ok && !is_dir($dir_path)) {
        if (!mkdir($dir_path)) {
            $folder_ok = false;
        } else {
            $created_folders[] = $dir_path;
        }
    }


    $folders = [
        "1_Sales-Commercial" => [
            "11_Order",
            "12_Finale Offer",
            "13_GCE_P0 and bank guarantee, company guarantee" => [
                "1 - old"
            ],
            "14_Order confirmation",
            "15_Additional costs and order updates"
        ],
        "2_Specifications" => [
            "21_Specification",
            "22_Specification drawing client"
        ],
        "3_Project time schedule and reports" => [],
        "4_Engineering" => [
            "41_Static calculation",
            "42_Drawings",
            "43_Detailed lists of components",
            "44_Work preparation" => [
                "0510 CNC",
                "0511 Blaue Mappe (Behalter-Rohre)",
                "0512 Grune Mappe (Boden)" => [
                    "QS Protokolle"
                ],
                "0513 Rote Mappe (Service)",
                "0514 ausgefult"
            ],
            "45_Chemical resistance check",
            "46_PED"
        ],
        "5_Purchasing" => [
            "51_PL Internal",
            "52_External"
        ],
        "6_Production" => [],
        "7_Shipment" => [
            "71_Transport sketch",
            "72_Packing lists",
            "73_Pictures",
            "74_Additional labels",
            "75_Additional documents"
        ],
        "8_Installation-Service" => [
            "81_Work preparation",
            "82_Construction site documents",
            "83_Pictures",
            "84_TimeSheet registration"
        ],
        "9_QC" => [
            "91_QIP-ITP",
            "92_MR",
            "93_Production documents, internal reports",
            "94_Pictures",
            "95_Other requested files",
            "96_TÜV"
        ],
        "10_Correspondence" => [],
        "11_Accounting-Finance" => [
            "111_Invoices",
            "112_Installation-Service"
        ],
        "12_Other" => [
            "121_Manual",
            "122_Spare parts lists",
            "123_Documents lists",
            "124_Other requested files"
        ],
        "13_Project summary" => [],
        "14_After sales service" => [
            "141_Inspection",
            "142_Follow-up actions",
            "143_Warranty expiration"
        ]
    ];

    if (!createNestedExportFolders($dir_path, $folders, $created_folders)) {
        removeFoldersOmsExport($created_folders);
        return false;
    }

    return $created_folders;
}

function createNestedExportFolders($parent_path, $folders, &$created_folders)
{
    foreach ($folders as $key => $value) {
        $folder_name = is_array($value) ? $key : $value;
        $folder_path = "$parent_path/$folder_name";

        if (!is_dir($folder_path) && !mkdir($folder_path)) {
            return false;
        }

        $created_folders[] = $folder_path;

        if (is_array($value)) {
            if (!createNestedExportFolders($folder_path, $value, $created_folders)) {
                return false;
            }
        }
    }
    return true;
}

function getOmsId($offer_no, $production_company, $sales_company = "")
{

    $oms = connectOMS();

    $query = "SELECT sa.id FROM salesarticles sa";
    $conditions = [];

    $conditions[] = sprintf("sa.offerNo='%s'", mysqli_real_escape_string($oms, $offer_no));

    if (!empty($sales_company)) {
        $query .= " LEFT JOIN projects p ON sa.offerNo = p.offerNo";
    }

    $conditions[] = sprintf("sa.PC = '%s'", mysqli_real_escape_string($oms, $production_company));

    if (!empty($sales_company)) {
        $conditions[] = sprintf("p.SC = '%s'", mysqli_real_escape_string($oms, $sales_company));
    }

    if (!empty($conditions)) {
        $query .= " WHERE " . implode(' AND ', $conditions);
    }

    $result = $oms->query($query);

    $oms->close();


    return $result->fetch_assoc()['id'];
}

function uploadDisposalFile($dirname, $tmp_name, $file_name_to_use)
{
    if ($tmp_name != "") {
        $disp_path = "../../orders/$dirname/" . $file_name_to_use;
        if (!move_uploaded_file($tmp_name, $disp_path)) {
            return false;
        }
    }
    return true;
}

function uploadOrderFile($dirname, $tmp_name, $name, $pon, $production_company)
{
    if ($tmp_name != "") {
        $order = explode(".", $name);
        $extO = end($order);
        $file_name = $production_company . "_" . $pon . "_" . date("Y-m-d His") . "_order." . $extO;
        $order_path = "../../orders/$dirname/" . $file_name;
        if (!move_uploaded_file($tmp_name, $order_path)) {
            return false;
        }
    }
    return true;
}

function uploadGceFile($dirname, $tmp_name, $name, $pon, $production_company)
{
    if ($tmp_name != "") {
        $gce = explode(".", $name);
        $extG = end($gce);
        $file_name = $production_company . "_" . $pon . "_" . date("Y-m-d His") . "_gce." . $extG;
        $gce_path = "../../orders/$dirname/" . $file_name;
        if (!move_uploaded_file($tmp_name, $gce_path)) {
            return false;
        }
    }
    return true;
}

function copyLopList($dirname)
{
    $lop_list_path = "../../orders/$dirname/Lop-List.xlsx";
    if (!file_exists($lop_list_path) && file_exists("./files/Lop-List_EN_v2.xlsx")) {
        if (!copy("files/Lop-List_EN_v2.xlsx", $lop_list_path)) {
            return false;
        }
    }
    return true;
}

function getCompaniesDataForSelect()
{
    $link = connectUsers();

    $query_result = $link->query("SELECT shortcut, name FROM companies");

    if ($query_result === false) {
        return [];
    }

    $data = $query_result->fetch_all(MYSQLI_ASSOC);
    $link->close();

    if (empty($data)) {
        return [];
    }

    $res = [];

    foreach ($data as $item) {
        $res[$item['shortcut']] = $item['name'];
    }

    return $res;
}

function getTableIdsToUpdateFilters()
{
    /*
     * SET name to update `filters`.`tableUniqueId`
     * ex.
     * ['CRM-clients-management']
     */
    return ['CRM-offers-management'];
}

function getColumnsToAddInFiltersArray()
{
    /*
     * SET columns data to insert
     * ex.
        $columns = [
           [
               'visible' => "false",
               'search' => [
                   'search' => '',
                   'smart' => "true",
                   'regex' => "false",
                   'caseInsensitive' => "true"
               ],
               'name' => 'comments',
               'index' => 18
           ]
       ];
     */
    $columns = [
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'inquiryNoFilter',
            'index' => 8
        ],
        [
            'visible' => "false",
            'search' => [
                'search' => '',
                'smart' => "true",
                'regex' => "false",
                'caseInsensitive' => "true"
            ],
            'name' => 'endClientInquiryNoFilter',
            'index' => 14
        ]
    ];
    return $columns;
}

function increaseIndexOfFiltersColumns($columns, $index)
{
    // Increase indexes of all columns that are higher than index of the new column
    for ($i = count($columns); $i >= $index; $i--) {
        if (isset($columns[$i])) {
            $columns[$i + 1] = $columns[$i];
        }
    }
    return $columns;
}

function addNewColumnsToSavedFiltersArray($filters)
{

    if (empty($filters)) {
        return $filters;
    }


    $filters_columns = [];

    if (isset($filters['columns'])) {
        $filters_columns = $filters['columns'];
    }

    $columns_to_add = getColumnsToAddInFiltersArray();

    if (empty($columns_to_add)) {
        return $filters;
    }

    $new_filters_columns = $filters_columns;

    // Just in case sort new columns by index
    usort($columns_to_add, function ($a, $b) {
        return $a['index'] <=> $b['index'];
    });

    $existing_columns = [];
    foreach ($new_filters_columns as $key => $value) {
        if (isset($value['name'])) {
            $existing_columns[$value['name']] = $key;
        }
    }

    foreach ($columns_to_add as $column) {
        $index = $column['index'];
        $name = $column['name'];
        unset($column['index']);
        if (!isset($existing_columns[$name])) {
            $new_filters_columns = increaseIndexOfFiltersColumns($new_filters_columns, $index);
            $new_filters_columns[$index] = $column;
            $existing_columns[$name] = $index;
        }
    }

    // Sort just in case
    ksort($new_filters_columns);

    $filters['columns'] = $new_filters_columns;

    $filters['updated'] = true;

    return $filters;
}
function getSalesArticleInfo(string $offerNo = ''): array
{
    $oms = new Modul\Oms();
    $salesArticlesList = new Modul\SalesArticleList();
    foreach ($oms->getSalesArticleInfo($offerNo) as $salesArtice) {
        $salesArticlesList->add($salesArtice['id'], $salesArtice['PC'], $salesArtice['PON']);
    }
    return $salesArticlesList->get();
}
