<?php
include("functions.php");

session_name("plasticonapp");
session_start([
	"cookie_domain" => COOKIE_DOMAIN_CFG,
	"cookie_path" => "/"
]);

$link = connect();
## Read value
$draw = $_POST['draw'];
$row = $_POST['start'];
$rowperpage = $_POST['length']; // Rows display per page
$columnIndex = $_POST['order']; // Column index
$empQuery = '';
//$columnSortOrder = $_POST['order']; // asc or desc

if ($_POST['initPerm'] == 1) {
	$searchValue = str_replace("'", "`", $_POST['search']['value']); // Search value
	$client = $_POST['client'];
	$end_client = $_POST['end_client'];
	$startOrderDateFilter = $_POST['startOrderDateFilter'];
	$endOrderDateFilter = $_POST['endOrderDateFilter'];
	$vFilter = $_POST['vFilter'];
	$tlFilter = $_POST['tlFilter'];
	$axFilter = $_POST['axFilter'];
	$reasonFilter = $_POST['reasonFilter'];
	$startInquiryDateFilter = $_POST['startInquiryDateFilter'];
	$endInquiryDateFilter = $_POST['endInquiryDateFilter'];
	$startORDFilter = $_POST['startORDFilter'];
	$endORDFilter = $_POST['endORDFilter'];
	$startOfferFilter = $_POST['startOfferFilter'];
	$endOfferFilter = $_POST['endOfferFilter'];
	$startNCDFilter = $_POST['startNCDFilter'];
	$endNCDFilter = $_POST['endNCDFilter'];
	$startDRDFilter = $_POST['startDRDFilter'];
	$endDRDFilter = $_POST['endDRDFilter'];
	$startDeliveryFilter = $_POST['startDeliveryFilter'];
	$endDeliveryFilter = $_POST['endDeliveryFilter'];
	$startReqOrderDate = $_POST['startReqOrderDateFilter'];
	$endReqOrderDate = $_POST['endReqOrderDateFilter'];
	$cA = $_POST['clientA'];
	$cAEND = $_POST['clientAEnd'];
	$cB = $_POST['clientB'];
	$cBEND = $_POST['clientBEnd'];
	$cC = $_POST['clientC'];
	$cCEND = $_POST['clientCEnd'];
	$cD = $_POST['clientD'];
	$cDEND = $_POST['clientDEnd'];
	$sGreen = $_POST['statusGreen'];
	$sYellow = $_POST['statusYellow'];
	$sRed = $_POST['statusRed'];
	$sBlue = $_POST['statusBlue'];
	$sGrey = $_POST['statusGrey'];
	$isFilter = $_POST['isFilter'];
	$cpFilter = $_POST['cpFilter'];
	$kamFilter = $_POST['kamFilter'];
	$asmFilter = $_POST['asmFilter'];
	$otFilter = $_POST['otFilter'];
	$pmFilter = $_POST['pmFilter'];
	$countryFilter = $_POST['countryFilter'];
	$countryEFilter = $_POST['countryEFilter'];
	$orderNoFilter = $_POST['orderNoFilter'];
	$prodOrderNoFilter = $_POST['prodOrderNoFilter'];
	$projectFilter = $_POST['projectFilter'];
	$projectNoFilter = $_POST['projectNoFilter'];
	$segmentFilter = $_POST['segmentFilter'];
	$followupFilter = $_POST['followupFilter'];
	$minOVE = $_POST['minOVE'];
	$maxOVE = $_POST['maxOVE'];
	$minGO = $_POST['minGO'];
	$maxGO = $_POST['maxGO'];
	$minGET = $_POST['minGET'];
	$maxGET = $_POST['maxGET'];
	$minGxG = "";
	$maxGxG = "";
	$minOVgg = $_POST['minOVgg'];
	$maxOVgg = $_POST['maxOVgg'];
	$offerNoFilter = $_POST['offerNoFilter'];
	$aocFilter = $_POST['aocFilter'];
	$oldOfferNoFilter = $_POST['oldOfferNoFilter'];
	$scopeFilter = $_POST['scopeFilter'];
	$commentsFilter = $_POST['commentsFilter'];
	$lastContactFilter = $_POST['lastContactFilter'];
	$clientCityFilter = $_POST['clientCityFilter'];
	$clientZipFilter = $_POST['clientZipFilter'];
	$endClientCityFilter = $_POST['endClientCityFilter'];
	$endClientZipFilter = $_POST['endClientZipFilter'];
	$minOFV = $_POST['minOFV'];
	$maxOFV = $_POST['maxOFV'];
	$minORV = $_POST['minORV'];
	$maxORV = $_POST['maxORV'];
	$ocFilter = $_POST['ocFilter'];
	$marketRFilter = $_POST['marketRFilter'];
	$marketEFilter = $_POST['marketEFilter'];

	$inquiryNoFilter = filter_input(INPUT_POST, 'inquiryNoFilter', FILTER_DEFAULT);
	$endClientInquiryNoFilter = filter_input(INPUT_POST, 'endClientInquiryNoFilter', FILTER_DEFAULT);

	if (isset($_SESSION['plasticonDigitalUser'])) {
		if (isset($_SESSION['plasticonDigitalUser']['crm'])) {
			if (isset($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'])) {
				if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1) {
					$cmpFilter = $_POST['cmpFilter'];
					$prodFilter = $_POST['prodFilter'];
				} else {
					$cmpFilter = 'all';
					$prodFilter = 'all';
				}
			}
		}
	}

	$minOFVCM = isset($_POST['minOFVCM']) ? $_POST['minOFVCM'] : '';
	$maxOFVCM =	isset($_POST['maxOFVCM']) ? $_POST['maxOFVCM'] : '';
	$minORVCM = isset($_POST['minORVCM']) ? $_POST['minORVCM'] : '';
	$maxORVCM = isset($_POST['maxORVCM']) ? $_POST['maxORVCM'] : '';
	$minProdValCM = "";
	$maxProdValCM = "";
	$minWHP = "";
	$maxWHP = "";
	$minWHS = "";
	$maxWHS = "";

	## Search 
	$searchQuery = " AND o.glassOffer=0 ";

	if ($searchValue != '') {

		$static_like_clauses = " 
		o.finalClient LIKE '%" . $searchValue . "%'
			OR o.OT LIKE '%" . $searchValue . "%'
			OR c.enterprise LIKE '%" . $searchValue . "%'
			OR c.clientShortName LIKE '%" . $searchValue . "%'
			OR c.clientLongName LIKE '%" . $searchValue . "%'
			OR c.previousName LIKE '%" . $searchValue . "%'
			OR k.previousName LIKE '%" . $searchValue . "%'
			OR k.clientShortName LIKE '%" . $searchValue . "%'
			OR k.clientLongName LIKE '%" . $searchValue . "%'
			OR k.enterprise LIKE '%" . $searchValue . "%'
			OR c.city LIKE '%" . $searchValue . "%'
			OR c.zip LIKE '%" . $searchValue . "%'
			OR o.scope LIKE '%" . $searchValue . "%'
			OR o.segment LIKE '%" . $searchValue . "%'
			OR o.projectName LIKE '%" . $searchValue . "%'
			OR c.InTL LIKE '%" . $searchValue . "%'
			OR c.InKAM LIKE '%" . $searchValue . "%'
			OR c.InASM LIKE '%" . $searchValue . "%'
			OR o.InID LIKE '%" . $searchValue . "%'
			OR o.InR LIKE '%" . $searchValue . "%'
			OR o.inquiry LIKE '%" . $searchValue . "%'
			OR o.request LIKE '%" . $searchValue . "%'
			OR o.offer LIKE '%" . $searchValue . "%'
			OR o.order LIKE '%" . $searchValue . "%'
			OR o.CMp LIKE '%" . $searchValue . "%'
			OR o.note LIKE '%" . $searchValue . "%'
			OR o.lastComment LIKE '%" . $searchValue . "%'
			OR o.InPM LIKE '%" . $searchValue . "%'
			OR o.InProdPM LIKE '%" . $searchValue . "%'
			OR o.InF LIKE '%" . $searchValue . "%'
                        OR `o`.`inquiryNo` LIKE '%" . $searchValue . "%'
                        OR `o`.`endclientInquiryNo` LIKE '%" . $searchValue . "%'
		";

		// Split the input by commas, semicolons, or spaces and trim the values
		$search_values = preg_split('/[;, ]+/', $searchValue);
		$search_values = array_map('trim', $search_values);

		// Build the LIKE clauses
		$like_clauses = [];
		$special_fields = ['o.id', 'o.offerNo', 'o.oldOfferNo', 'o.inquiryNo', 'p.projectNo', 'o.orderNo', 'o.productionOrderNo'];

		foreach ($search_values as $value) {
			if (!empty($value)) {
				foreach ($special_fields as $field) {
					$like_clauses[] = "$field LIKE '%" . $value . "%'";
				}
			}
		}

		if (!empty($like_clauses)) {
			$searchQuery .= "AND (";
			$searchQuery .= implode(' OR ', $like_clauses);
			$searchQuery .= " OR " . $static_like_clauses;
			$searchQuery .= ") ";
		} else {
			$searchQuery .= "AND (" . $static_like_clauses . ") ";
		}
	}
	if ($client != "")
		$searchQuery .= "AND client='$client' ";
	if ($end_client != "")
		$searchQuery .= "AND finalClient='$end_client' ";
	if ($minOVE == "" && $maxOVE == "")
		$searchQuery .= "";
	else {
		if ($minOVE == "")
			$searchQuery .= "AND (`OVE` BETWEEN '0' AND '$maxOVE') ";
		else {
			if ($maxOVE == "")
				$searchQuery .= "AND (`OVE` BETWEEN '$minOVE' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`OVE` BETWEEN '$minOVE' AND '$maxOVE') ";
		}
	}

	if ($minOFV == "" && $maxOFV == "")
		$searchQuery .= "";
	else {
		if ($minOFV == "")
			$searchQuery .= "AND (`OV` BETWEEN '0' AND '$maxOFV') ";
		else {
			if ($maxOFV == "")
				$searchQuery .= "AND (`OV` BETWEEN '$minOFV' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`OV` BETWEEN '$minOFV' AND '$maxOFV') ";
		}
	}

	if ($minGO == "" && $maxGO == "")
		$searchQuery .= "";
	else {
		if ($minGO == "")
			$searchQuery .= "AND (`GO` BETWEEN '0' AND '$maxGO') ";
		else {
			if ($maxGO == "")
				$searchQuery .= "AND (`GO` BETWEEN '$minGO' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`GO` BETWEEN '$minGO' AND '$maxGO') ";
		}
	}

	if ($minGET == "" && $maxGET == "")
		$searchQuery .= "";
	else {
		if ($minGET == "")
			$searchQuery .= "AND (`GET` BETWEEN '0' AND '$maxGET') ";
		else {
			if ($maxGET == "")
				$searchQuery .= "AND (`GET` BETWEEN '$minGET' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`GET` BETWEEN '$minGET' AND '$maxGET') ";
		}
	}

	if ($minGxG == "" && $maxGxG == "")
		$searchQuery .= "";
	else {
		if ($minGxG == "")
			$searchQuery .= "AND (`GO`/100*`GET` BETWEEN '0' AND '$maxGxG') ";
		else {
			if ($maxGxG == "")
				$searchQuery .= "AND (`GO`/100*`GET` BETWEEN '$minGxG' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`GO`/100*`GET` BETWEEN '$minGxG' AND '$maxGxG') ";
		}
	}

	if ($minOVgg == "" && $maxOVgg == "")
		$searchQuery .= "";
	else {
		if ($minOVgg == "")
			$searchQuery .= "AND (((`OVE`)/100)*(`GO`/100)*`GET` BETWEEN '0' AND '$maxOVgg') ";
		else {
			if ($maxOVgg == "")
				$searchQuery .= "AND (((`OVE`)/100)*(`GO`/100)*`GET` BETWEEN '$minOVgg' AND '9999999999999') ";
			else
				$searchQuery .= "AND (((`OVE`)/100)*(`GO`/100)*`GET` BETWEEN '$minOVgg' AND '$maxOVgg') ";
		}
	}

	if ($minOFVCM == "" && $maxOFVCM == "")
		$searchQuery .= "";
	else {
		if ($minOFVCM == "")
			$searchQuery .= "AND (((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100) BETWEEN '0' AND '$maxOFVCM') ";
		else {
			if ($maxOFVCM == "")
				$searchQuery .= "AND (((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100) BETWEEN '$minOFVCM' AND '9999999999999') ";
			else
				$searchQuery .= "AND (((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100) BETWEEN '$minOFVCM' AND '$maxOFVCM') ";
		}
	}

	if ($minORVCM == "" && $maxORVCM == "")
		$searchQuery .= "";
	else {
		if ($minORVCM == "")
			$searchQuery .= "AND ((((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100))*100/orderValue BETWEEN '0' AND '$maxORVCM') ";
		else {
			if ($maxORVCM == "")
				$searchQuery .= "AND ((((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100))*100/orderValue BETWEEN '$minORVCM' AND '9999999999999') ";
			else
				$searchQuery .= "AND ((((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100))*100/orderValue BETWEEN '$minORVCM' AND '$maxORVCM') ";
		}
	}

	if ($minWHP == "" && $maxWHP == "")
		$searchQuery .= "";
	else {
		if ($minWHP == "")
			$searchQuery .= "AND (`WHP` BETWEEN '0' AND '$maxWHP') ";
		else {
			if ($maxWHP == "")
				$searchQuery .= "AND (`WHP` BETWEEN '$minWHP' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`WHP` BETWEEN '$minWHP' AND '$maxWHP') ";
		}
	}

	if ($minWHS == "" && $maxWHS == "")
		$searchQuery .= "";
	else {
		if ($minWHS == "")
			$searchQuery .= "AND (`WHS` BETWEEN '0' AND '$maxWHS') ";
		else {
			if ($maxWHS == "")
				$searchQuery .= "AND (`WHS` BETWEEN '$minWHS' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`WHS` BETWEEN '$minWHS' AND '$maxWHS') ";
		}
	}

	if ($minProdValCM == "" && $maxProdValCM == "")
		$searchQuery .= "";
	else {
		if ($minProdValCM == "")
			$searchQuery .= "AND (`prodValCM` BETWEEN '0' AND '$maxProdValCM') ";
		else {
			if ($maxProdValCM == "")
				$searchQuery .= "AND (`prodValCM` BETWEEN '$minProdValCM' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`prodValCM` BETWEEN '$minProdValCM' AND '$maxProdValCM') ";
		}
	}

	if ($minORV == "" && $maxORV == "")
		$searchQuery .= "";
	else {
		if ($minORV == "")
			$searchQuery .= "AND (`orderValue` BETWEEN '0' AND '$maxORV') ";
		else {
			if ($maxORV == "")
				$searchQuery .= "AND (`orderValue` BETWEEN '$minORV' AND '9999999999999') ";
			else
				$searchQuery .= "AND (`orderValue` BETWEEN '$minORV' AND '$maxORV') ";
		}
	}

	if ($startOrderDateFilter == "" && $endOrderDateFilter == "")
		$searchQuery .= "";
	else {
		if ($startOrderDateFilter == "")
			$searchQuery .= "AND (`order` BETWEEN '0000-00-00' AND '$endOrderDateFilter') ";
		else {
			if ($endOrderDateFilter == "")
				$searchQuery .= "AND (`order` BETWEEN '$startOrderDateFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`order` BETWEEN '$startOrderDateFilter' AND '$endOrderDateFilter') ";
		}
	}

	if ($startReqOrderDate == "" && $endReqOrderDate == "")
		$searchQuery .= "";
	else {
		if ($startReqOrderDate == "")
			$searchQuery .= "AND (`requestedOrderDate` BETWEEN '0000-00-00' AND '$endReqOrderDate') ";
		else {
			if ($endReqOrderDate == "")
				$searchQuery .= "AND (`requestedOrderDate` BETWEEN '$startReqOrderDate' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`requestedOrderDate` BETWEEN '$startReqOrderDate' AND '$endReqOrderDate') ";
		}
	}

	if ($startInquiryDateFilter == "" && $endInquiryDateFilter == "")
		$searchQuery .= "";
	else {
		if ($startInquiryDateFilter == "")
			$searchQuery .= "AND (`inquiry` BETWEEN '0000-00-00' AND '$endInquiryDateFilter') ";
		else {
			if ($endInquiryDateFilter == "")
				$searchQuery .= "AND (`inquiry` BETWEEN '$startInquiryDateFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`inquiry` BETWEEN '$startInquiryDateFilter' AND '$endInquiryDateFilter') ";
		}
	}

	if ($startORDFilter == "" && $endORDFilter == "")
		$searchQuery .= "";
	else {
		if ($startORDFilter == "")
			$searchQuery .= "AND (`request` BETWEEN '0000-00-00' AND '$endORDFilter') ";
		else {
			if ($endORDFilter == "")
				$searchQuery .= "AND (`request` BETWEEN '$startORDFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`request` BETWEEN '$startORDFilter' AND '$endORDFilter') ";
		}
	}

	if ($startOfferFilter == "" && $endOfferFilter == "")
		$searchQuery .= "";
	else {
		if ($startOfferFilter == "")
			$searchQuery .= "AND (`offer` BETWEEN '0000-00-00' AND '$endOfferFilter') ";
		else {
			if ($endOfferFilter == "")
				$searchQuery .= "AND (`offer` BETWEEN '$startOfferFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`offer` BETWEEN '$startOfferFilter' AND '$endOfferFilter') ";
		}
	}

	if ($startNCDFilter == "" && $endNCDFilter == "")
		$searchQuery .= "";
	else {
		if ($startNCDFilter == "")
			$searchQuery .= "AND (o.`nextContactDate` BETWEEN '0000-00-00' AND '$endNCDFilter') ";
		else {
			if ($endNCDFilter == "")
				$searchQuery .= "AND (o.`nextContactDate` BETWEEN '$startNCDFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (o.`nextContactDate` BETWEEN '$startNCDFilter' AND '$endNCDFilter') ";
		}
	}

	if ($startDRDFilter == "" && $endDRDFilter == "")
		$searchQuery .= "";
	else {
		if ($startDRDFilter == "")
			$searchQuery .= "AND (`requestedDeliveryDate` BETWEEN '0000-00-00' AND '$endDRDFilter') ";
		else {
			if ($endDRDFilter == "")
				$searchQuery .= "AND (`requestedDeliveryDate` BETWEEN '$startDRDFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`requestedDeliveryDate` BETWEEN '$startDRDFilter' AND '$endDRDFilter') ";
		}
	}

	if ($startDeliveryFilter == "" && $endDeliveryFilter == "")
		$searchQuery .= "";
	else {
		if ($startDeliveryFilter == "")
			$searchQuery .= "AND (`deliveryDate` BETWEEN '0000-00-00' AND '$endDeliveryFilter') ";
		else {
			if ($endDeliveryFilter == "")
				$searchQuery .= "AND (`deliveryDate` BETWEEN '$startDeliveryFilter' AND '2100-01-01') ";
			else
				$searchQuery .= "AND (`deliveryDate` BETWEEN '$startDeliveryFilter' AND '$endDeliveryFilter') ";
		}
	}

	if ($vFilter != "null") {
		$searchQuery .= "AND ( ";
		$v = explode(",", $vFilter);
		$pom = 1;
		foreach ($v as $vi)
			if ($pom == 1) {
				if ($vi == "empty")
					$searchQuery .= " V='' ";
				else
					$searchQuery .= " V='$vi' ";
				$pom++;
			} else
				if ($vi == "empty")
				$searchQuery .= "OR V='' ";
			else
				$searchQuery .= "OR V='$vi' ";
		$searchQuery .= ") ";
	}
	if ($followupFilter != "null") {
		$searchQuery .= "AND ( ";
		$f = explode(",", $followupFilter);
		$pom = 1;
		foreach ($f as $fu)
			if ($pom == 1) {
				if ($fu == "empty")
					$searchQuery .= " F='' ";
				else
					$searchQuery .= " F='$fu' ";
				$pom++;
			} else
				if ($fu == "empty")
				$searchQuery .= "OR F='' ";
			else
				$searchQuery .= "OR F='$fu' ";
		$searchQuery .= ") ";
	}
	if ($tlFilter != "null") {
		$searchQuery .= "AND ( ";
		$tls = explode(",", $tlFilter);
		$pom = 1;
		foreach ($tls as $tl)
			if ($pom == 1) {
				if ($tl == "empty")
					$searchQuery .= " (c.tl='' OR k.tl='') ";
				else
					$searchQuery .= " (c.tl='$tl' OR k.tl='$tl') ";
				$pom++;
			} else
				if ($tl == "empty")
				$searchQuery .= "OR (c.tl='' OR k.tl='') ";
			else
				$searchQuery .= "OR (c.tl='$tl' OR k.tl='$tl') ";
		$searchQuery .= ") ";
	}
	if ($segmentFilter != "null") {
		if ($segmentFilter == "empty") {
			$searchQuery .= "AND (o.segment='' OR o.segment=';') ";
		} else {
			$searchQuery .= "AND ( ";
			$segments = explode(",", $segmentFilter);
			$pom = 1;
			foreach ($segments as $segm)
				if ($pom == 1) {
					if ($segm == 'empty')
						$searchQuery .= " o.segment='' ";
					else
						$searchQuery .= " o.segment LIKE CONCAT('%','$segm','%') ";
					$pom++;
				} else
					if ($segm == 'empty')
					$searchQuery .= " OR o.segment='' ";
				else
					$searchQuery .= " OR o.segment LIKE CONCAT('%','$segm','%') ";
			$searchQuery .= ") ";
		}
	}
	if ($kamFilter != "null") {
		$searchQuery .= "AND ( ";
		$kams = explode(",", $kamFilter);
		$pom = 1;
		foreach ($kams as $kam)
			if ($pom == 1) {
				if ($kam == 'empty')
					$searchQuery .= " (c.KAM='' OR k.KAM='') ";
				else
					$searchQuery .= " (c.KAM='$kam' OR k.KAM='$kam') ";
				$pom++;
			} else
				if ($kam == 'empty')
				$searchQuery .= "OR (c.KAM='' OR k.KAM='') ";
			else
				$searchQuery .= "OR (c.KAM='$kam' OR k.KAM='$kam') ";
		$searchQuery .= ") ";
	}
	if ($asmFilter != "null") {
		$searchQuery .= "AND ( ";
		$asms = explode(",", $asmFilter);
		$pom = 1;
		foreach ($asms as $asm)
			if ($pom == 1) {
				if ($asm == 'empty')
					$searchQuery .= " (c.ASM='' OR k.ASM='') ";
				else
					$searchQuery .= " (c.ASM='$asm' OR k.ASM='$asm') ";
				$pom++;
			} else
				if ($asm == 'empty')
				$searchQuery .= "OR (c.ASM='' OR k.ASM='')";
			else
				$searchQuery .= "OR (c.ASM='$asm' OR k.ASM='$asm')";
		$searchQuery .= ") ";
	}
	if ($isFilter != "null") {
		$searchQuery .= "AND ( ";
		$isf = explode(",", $isFilter);
		$pom = 1;
		foreach ($isf as $is)
			if ($pom == 1) {
				if ($is == 'empty')
					$searchQuery .= " o.oID='' ";
				else
					$searchQuery .= " o.oID='$is' ";
				$pom++;
			} else
				if ($is == 'empty')
				$searchQuery .= "OR o.oID='' ";
			else
				$searchQuery .= "OR o.oID='$is' ";
		$searchQuery .= ") ";
	}
	if ($cpFilter != "null") {
		$searchQuery .= "AND ( ";
		$cpf = explode(",", $cpFilter);
		$pom = 1;
		foreach ($cpf as $cp)
			if ($pom == 1) {
				if ($cp == 'empty')
					$searchQuery .= " o.calcPersons LIKE CONCAT('%;','',';%')";
				else
					$searchQuery .= " o.calcPersons LIKE CONCAT('%;','$cp',';%')";
				$pom++;
			} else
				if ($cp == 'empty')
				$searchQuery .= "OR o.calcPersons LIKE CONCAT('%;','',';%')";
			else
				$searchQuery .= "OR o.calcPersons LIKE CONCAT('%;','$cp',';%')";
		$searchQuery .= ") ";
	}
	if ($countryFilter != "null") {
		$searchQuery .= "AND ( ";
		$countryf = explode(",", $countryFilter);
		$pom = 1;
		foreach ($countryf as $country)
			if ($pom == 1) {
				$searchQuery .= " c.country LIKE CONCAT('%','$country','%')";
				$pom++;
			} else
				$searchQuery .= "OR c.country LIKE CONCAT('%','$country','%')";
		$searchQuery .= ") ";
	}
	if ($countryEFilter != "null") {
		$searchQuery .= "AND ( ";
		$countryEf = explode(",", $countryEFilter);
		$pom = 1;
		foreach ($countryEf as $countryE)
			if ($pom == 1) {
				$searchQuery .= " k.country LIKE CONCAT('%','$countryE','%')";
				$pom++;
			} else
				$searchQuery .= "OR k.country LIKE CONCAT('%','$countryE','%')";
		$searchQuery .= ") ";
	}
	if ($axFilter != "null") {
		$searchQuery .= "AND ( ";
		$axs = explode(",", $axFilter);
		$pom = 1;
		foreach ($axs as $ax)
			if ($pom == 1) {
				if ($ax == "open")
					$searchQuery .= " o.AX='' ";
				else
					$searchQuery .= " o.AX='$ax' ";
				$pom++;
			} else
				if ($ax == "open")
				$searchQuery .= "OR o.AX='' ";
			else
				$searchQuery .= "OR o.AX='$ax' ";
		$searchQuery .= ") ";
	}
	if ($reasonFilter != "null") {
		$searchQuery .= "AND ( ";
		$reasons = explode(",", $reasonFilter);
		$pom = 1;
		foreach ($reasons as $reason)
			if ($pom == 1) {
				if ($reason == 'empty')
					$searchQuery .= " o.reason='' ";
				else
					$searchQuery .= " o.reason='$reason' ";
				$pom++;
			} else
				if ($reason == 'empty')
				$searchQuery .= "OR o.reason='' ";
			else
				$searchQuery .= "OR o.reason='$reason' ";
		$searchQuery .= ") ";
	}
	if ($otFilter != "null") {
		$searchQuery .= "AND ( ";
		$otf = explode(",", $otFilter);
		$pom = 1;
		foreach ($otf as $ot)
			if ($pom == 1) {
				$searchQuery .= " o.OT='$ot' ";
				$pom++;
			} else
				$searchQuery .= "OR o.OT='$ot' ";
		$searchQuery .= ") ";
	}
	if ($pmFilter != "null") {
		$searchQuery .= "AND ( ";
		$pms = explode(",", $pmFilter);
		$pom = 1;
		foreach ($pms as $pm)
			if ($pom == 1) {
				if ($pm == "empty")
					$searchQuery .= " o.PM='' ";
				else
					$searchQuery .= " o.PM='$pm' ";
				$pom++;
			} else
				if ($pm == "empty")
				$searchQuery .= "OR o.PM='' ";
			else
				$searchQuery .= "OR o.PM='$pm' ";
		$searchQuery .= ") ";
	}
	if ($orderNoFilter != "") {
		$order_numbers = preg_split('/[;, ]+/', $orderNoFilter);
		$order_numbers = array_map('trim', $order_numbers);

		$like_clauses = [];
		foreach ($order_numbers as $order_no) {
			if (!empty($order_no)) {
				$like_clauses[] = "o.orderNo LIKE '%" . $order_no . "%'";
			}
		}
		if (!empty($like_clauses)) {
			$searchQuery .= "AND (" . implode(' OR ', $like_clauses) . ") ";
		}
	}
	if ($prodOrderNoFilter != "") {
		$prod_order_numbers = preg_split('/[;, ]+/', $prodOrderNoFilter);
		$prod_order_numbers = array_map('trim', $prod_order_numbers);

		$like_clauses = [];
		foreach ($prod_order_numbers as $prod_order_no) {
			if (!empty($prod_order_no)) {
				$like_clauses[] = "o.productionOrderNo LIKE '%" . $prod_order_no . "%'";
			}
		}
		if (!empty($like_clauses)) {
			$searchQuery .= "AND (" . implode(' OR ', $like_clauses) . ") ";
		}
	}

	if ($offerNoFilter != "") {
		$offer_numbers = preg_split('/[;, ]+/', $offerNoFilter);
		$offer_numbers = array_map('trim', $offer_numbers);

		$test = '';
		$like_clauses = [];
		foreach ($offer_numbers as $offer_no) {
			if (!empty($offer_no)) {
				$like_clauses[] = "o.offerNo LIKE '%" . $offer_no . "%'";
			}
		}
		if (!empty($like_clauses)) {
			$searchQuery .= "AND (" . implode(' OR ', $like_clauses) . ") ";
		}
	}
	if ($aocFilter != "")
		$searchQuery .= "AND SOC='$aocFilter' ";
	if ($oldOfferNoFilter != "")
		$searchQuery .= "AND oldOfferNo LIKE CONCAT('%','$oldOfferNoFilter','%') ";
	if ($scopeFilter != "")
		$searchQuery .= "AND scope LIKE CONCAT('%','$scopeFilter','%') ";
	if ($commentsFilter) {
		$searchQuery .= "AND comt.note LIKE CONCAT('%','$commentsFilter','%') ";
	}
	if ($lastContactFilter != "")
		$searchQuery .= "AND lastComment LIKE CONCAT('%','$lastContactFilter','%') ";
	if ($clientCityFilter != "")
		$searchQuery .= "AND c.city LIKE CONCAT('%','$clientCityFilter','%') ";
	if ($clientZipFilter != "")
		$searchQuery .= "AND c.zip LIKE CONCAT('%','$clientZipFilter','%') ";
	if ($endClientCityFilter != "")
		$searchQuery .= "AND k.city LIKE CONCAT('%','$endClientCityFilter','%') ";
	if ($endClientZipFilter != "")
		$searchQuery .= "AND k.zip LIKE CONCAT('%','$endClientZipFilter','%') ";

	if ($inquiryNoFilter !== null && $inquiryNoFilter !== '') {
		$searchQuery .= "AND `o`.`inquiryNo` LIKE '%" . $inquiryNoFilter . "%' ";
	}

	if ($endClientInquiryNoFilter !== null && $endClientInquiryNoFilter !== '') {
		$searchQuery .= "AND `o`.`endclientInquiryNo` LIKE '%" . $endClientInquiryNoFilter . "%' ";
	}
	if ($marketRFilter != "") {
		$searchQuery .= "AND ( ";
		$marketr = explode(",", $marketRFilter);
		$pom = 1;
		foreach ($marketr as $market) {
			if ($pom == 1) {
				$searchQuery .= " `c`.`market` LIKE CONCAT('%','$market','%')";
				$pom++;
			} else {
				$searchQuery .= " OR `c`.`market` LIKE CONCAT('%','$market','%')";
			}
		}
		$searchQuery .= ") ";
	}
	if ($marketEFilter != "") {
		$searchQuery .= "AND ( ";
		$markete = explode(",", $marketEFilter);
		$pom = 1;
		foreach ($markete as $market) {
			if ($pom == 1) {
				$searchQuery .= " `k`.`market` LIKE CONCAT('%','$market','%')";
				$pom++;
			} else {
				$searchQuery .= " OR `k`.`market` LIKE CONCAT('%','$market','%')";
			}
		}
		$searchQuery .= ") ";
	}
	if ($projectFilter != "null") {
		$searchQuery .= "AND ( ";
		$projects = explode(",", $projectFilter);
		$pom = 1;
		foreach ($projects as $project)
			if ($pom == 1) {
				$searchQuery .= " o.projectId='$project' ";
				$pom++;
			} else
				$searchQuery .= "OR o.projectId='$project' ";
		$searchQuery .= ") ";
	}
	if ($projectNoFilter != "null") {
		$searchQuery .= "AND ( ";
		$projectsNo = explode(",", $projectNoFilter);
		$pom = 1;
		foreach ($projectsNo as $project)
			if ($pom == 1) {
				$searchQuery .= " p.projectNo='$project' ";
				$pom++;
			} else
				$searchQuery .= "OR p.projectNo='$project' ";
		$searchQuery .= ") ";
	}
	if ($ocFilter != "null") {
		$searchQuery .= "AND ( ";
		$ocs = explode(",", $ocFilter);
		$pom = 1;
		foreach ($ocs as $oc)
			if ($pom == 1) {
				$searchQuery .= " o.orderCompany='$oc' ";
				$pom++;
			} else
				$searchQuery .= "OR o.orderCompany='$oc' ";
		$searchQuery .= ") ";
	}
	if ($cmpFilter != "null" && $cmpFilter != "all") {
		$searchQuery .= "AND ( ";
		$cmp = explode(",", $cmpFilter);
		$pom = 1;
		foreach ($cmp as $cp)
			if ($pom == 1) {
				$searchQuery .= " o.company='$cp' ";
				$pom++;
			} else
				$searchQuery .= "OR o.company='$cp' ";
		$searchQuery .= ") ";
	}
	if ($prodFilter != "null" && $prodFilter != "all") {
		$searchQuery .= "AND ( ";
		$prd = explode(",", $prodFilter);
		$pom = 1;
		foreach ($prd as $pr)
			if ($pom == 1) {
				$searchQuery .= " o.productionLocation LIKE CONCAT('%','$pr','%') ";
				$pom++;
			} else
				$searchQuery .= "OR o.productionLocation LIKE CONCAT('%','$pr','%') ";
		$searchQuery .= ") ";
	}
	$pom = 0;
	if ($cA == 1 || $cB == 1 || $cC == 1 || $cD == 1) {
		if ($cA == 1 && $cB == 1 && $cC == 1 && $cD == 1) {
			$searchQuery .= "";
		} else {
			$searchQuery .= " AND ( ";
			if ($cA == 1) {
				$searchQuery .= "c.type='A+' ";
				$pom = 1;
			}
			if ($cB == 1) {
				if ($pom == 1)
					$searchQuery .= " OR c.type='B+' ";
				else {
					$searchQuery .= " c.type='B+' ";
					$pom = 1;
				}
			}
			if ($cC == 1) {
				if ($pom == 1)
					$searchQuery .= " OR c.type='A-' ";
				else {
					$searchQuery .= " c.type='A-' ";
					$pom = 1;
				}
			}
			if ($cD == 1) {
				if ($pom == 1)
					$searchQuery .= " OR c.type='B-' ";
				else {
					$searchQuery .= " c.type='B-' ";
					$pom = 1;
				}
			}
			$searchQuery .= " ) ";
		}
	}
	$pom = 0;
	if ($cAEND == 1 || $cBEND == 1 || $cCEND == 1 || $cDEND == 1) {
		if ($cAEND == 1 && $cBEND == 1 && $cCEND == 1 && $cDEND == 1) {
			$searchQuery .= "";
		} else {
			$searchQuery .= " AND ( ";
			if ($cAEND == 1) {
				$searchQuery .= " k.type='A+' ";
				$pom = 1;
			}
			if ($cBEND == 1) {
				if ($pom == 1)
					$searchQuery .= " OR k.type='B+' ";
				else {
					$searchQuery .= " k.type='B+' ";
					$pom = 1;
				}
			}
			if ($cCEND == 1) {
				if ($pom == 1)
					$searchQuery .= " OR k.type='A-' ";
				else {
					$searchQuery .= " k.type='A-' ";
					$pom = 1;
				}
			}
			if ($cDEND == 1) {
				if ($pom == 1)
					$searchQuery .= " OR k.type='B-' ";
				else {
					$searchQuery .= " k.type='B-' ";
					$pom = 1;
				}
			}
			$searchQuery .= " ) ";
		}
	}
	$pom = 0;
	if ($sGreen == 1 || $sYellow == 1 || $sRed == 1 || $sBlue == 1 || $sGrey == 1) {
		if ($sGreen == 1 && $sYellow == 1 && $sRed == 1 && $sBlue == 1 && $sGrey == 1) {
			$new = 1;
		} else {
			$searchQuery .= " AND ( ";
			if ($sGreen == 1) {
				$data = date("Y-m-d");
				if ($pom == 1) {
					$searchQuery .= " OR (AX='' AND (OV!='0') AND (o.nextContactDate!='0000-00-00' AND o.nextContactDate>='$data')) ";
				} else {
					$searchQuery .= " (AX='' AND (OV!='0') AND (o.nextContactDate!='0000-00-00' AND o.nextContactDate>='$data')) ";
					$pom = 1;
				}
			}
			if ($sYellow == 1) {
				$data = date("Y-m-d");
				if ($pom == 1) {
					$searchQuery .= " OR (AX='' AND (OV!='0') AND (o.nextContactDate!='0000-00-00' AND o.nextContactDate<'$data')) ";
				} else {
					$searchQuery .= " (AX='' AND (OV!='0') AND (o.nextContactDate!='0000-00-00' AND o.nextContactDate<'$data')) ";
					$pom = 1;
				}
			}
			if ($sRed == 1) {
				$data = date('Y-m-d', strtotime(date("Y-m-d") . " - 3 day"));
				if ($pom == 1) {
					$searchQuery .= " OR ((OV!='0') AND AX='' AND o.nextContactDate='0000-00-00' AND offer<='$data') ";
				} else {
					$searchQuery .= " ((OV!='0') AND AX='' AND o.nextContactDate='0000-00-00' AND offer<='$data') ";
					$pom = 1;
				}
			}
			if ($sBlue == 1) {
				if ($pom == 1) {
					$searchQuery .= " OR ( (OV='0') AND AX='' ) ";
				} else {
					$searchQuery .= " ( (OV='0') AND AX='' ) ";
					$pom = 1;
				}
			}
			if ($sGrey == 1) {
				if ($pom == 1) {
					$searchQuery .= " OR ( AX!='' AND offer!='0000-00-00') ";
				} else {
					$searchQuery .= " ( AX!='' AND offer!='0000-00-00') ";
					$pom = 1;
				}
			}
			if ($pom == 0)
				$searchQuery .= " 1 ) ";
			else
				$searchQuery .= " ) ";
		}
	}
	$cpn = $_SESSION['plasticonDigitalUser']['company'];
	$userid = $_SESSION['plasticonDigitalUser']['id'];
	if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 0) {
		$searchQuery .= " AND o.company='$cpn'";
	}
	if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 0 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1) {
		$searchQuery .= " AND o.company='$cpn' AND (c.kam='$userid' OR c.asm='$userid' OR c.tl='$userid' OR o.V='$userid' OR o.oID='$userid' OR o.calcPersons LIKE CONCAT('%;','$userid',';%') OR o.F='$userid')";
	}
	if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1 && $_SESSION['plasticonDigitalUser']['crm']['onlyPersonalInfo'] == 1) {
		$searchQuery .= " AND (c.kam='$userid' OR c.asm='$userid' OR c.tl='$userid' OR o.V='$userid' OR o.oID='$userid' OR o.calcPersons LIKE CONCAT('%;','$userid',';%') OR o.F='$userid')";
	}
	//echo $searchQuery;
	## Total number of records without filtering
	$sel = $link->query("select count(*) as allcount from offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id");
	$records = $sel->fetch_object();
	$totalRecords = $records->allcount;

	## Total number of records with filtering
	$sel_query = "select count(*) as allcount from offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id";
	if ($commentsFilter) {
		$sel_query .= " LEFT JOIN comments comt ON o.id=comt.offerId ";
	}
	$sel_query .= " WHERE 1 " . $searchQuery;

	$sel = $link->query($sel_query);
	if ($link->errno > 0) {
		/*
             * TO DO - admin property in session
             */
		//echo $link->error."<br/>";
	}
	$records = $sel->fetch_object();
	$totalRecordwithFilter = $records->allcount;

	$orderQuery = "";
	foreach ($columnIndex as $ind) {
		$col = $ind['column'];
		$orderdir = $ind['dir'];
		$columnName = $_POST['columns'][$col]['data']; // Column name
		if ($columnName != "") {
			if ($columnName == "clientShortName" || $columnName == 'city' || $columnName == 'zip' || $columnName == 'InKAM' || $columnName == 'InASM' || $columnName == 'InTL') {
				$table = "c.";
			} else {
				if ($columnName == 'gxg' || $columnName == 'OVgg' || $columnName == "oneClient" || $columnName == "oi" || $columnName == "gCMp" || $columnName == "gCMe") {
					$table = "";
				} elseif ($columnName == "shortNameEnd" || $columnName == "typeEnd" || $columnName == "cityEnd") {
					$table = "k.";
				} else {
					$table = 'o.';
				}
			}

			if ($table == 'k.' && ($columnName == "shortNameEnd")) {
				$columnName = 'clientShortName';
			}

			if ($table == 'k.' && ($columnName == "cityEnd")) {
				$columnName = 'city';
			}

			if ($columnName == "comments") {
				$table = "comt.";
				$columnName = "note";
			}

			$orderQuery .= " " . $table . $columnName . " " . $orderdir . ", ";
		}
	}

	$orderQuery = rtrim($orderQuery, ", ");

	$_SESSION['searchQueryOffers'] = "SELECT o.OVE, `GO`/100*`GET` as gxg, ((`OVE`)/100)*(`GO`/100)*`GET` as OVgg FROM offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id LEFT JOIN clientsresponsibility crr ON (c.company=crr.company AND c.id=crr.clientId) LEFT JOIN clientsresponsibility cre ON k.company=cre.company AND k.id=cre.clientId";

	if ($commentsFilter) {
		$_SESSION['searchQueryOffers'] .= " LEFT JOIN comments comt ON o.id=comt.offerId ";
	}
	$_SESSION['searchQueryOffers'] .= " WHERE 1 " . $searchQuery;


	$searchQueryOffers = "SELECT o.OVE, o.orderValue, o.OV, `GO`/100*`GET` as gxg, ((`OVE`)/100)*(`GO`/100)*`GET` as OVgg FROM offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id LEFT JOIN clientsresponsibility crr ON (c.company=crr.company AND c.id=crr.clientId) LEFT JOIN clientsresponsibility cre ON k.company=cre.company AND k.id=cre.clientId LEFT JOIN (SELECT DISTINCT(offerNo) as offerNo, MAX(revision) as revNo FROM `components` WHERE revision!=0 GROUP BY offerNo) com ON com.offerNo=o.offerNo";

	if ($commentsFilter) {
		$searchQueryOffers .= " LEFT JOIN comments comt ON o.id=comt.offerId ";
	}
	$searchQueryOffers .= " WHERE 1 " . $searchQuery;

	$_SESSION['searchQueryOffersCondition'] = $searchQuery . " order by " . $orderQuery;

	$empQuery = "SELECT o.*, (((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100))*100/orderValue as gCMp, ((orderValue*ORVCM)/100)+((productionValue*prodValCM)/100) as gCMe, c.clientShortName, c.market as marketR, k.market as marketE, c.previousName, c.type, c.city, c.KAM, c.ASM, c.country, c.zip, k.country as Ecountry, k.zip as Ezip, c.TL as TLc, c.InTL, k.InTL as InTLE, `GO`/100*`GET` as gxg, ((`OVE`)/100)*(`GO`/100)*`GET` as OVgg, COALESCE(NULLIF(c.clientLongName,''), k.clientLongName) as oneClient, DATEDIFF(o.offer, o.inquiry) as oi, k.clientShortName as shortNameEnd, k.previousName, k.type as typeEnd, k.city as cityEnd, crr.inTL as InTL, crr.inKAM as InKAM, crr.inASM as InASM, cre.inTL as InTLE, cre.inKAM as InKAME, cre.inASM as InASME, p.projectNo FROM offers o LEFT JOIN projects p ON o.projectid = p.id LEFT JOIN clients c ON o.client=c.id LEFT JOIN clients k ON o.finalClient=k.id LEFT JOIN clientsresponsibility crr ON (c.company=crr.company AND c.id=crr.clientId) LEFT JOIN clientsresponsibility cre ON k.company=cre.company AND k.id=cre.clientId";

	if ($commentsFilter) {
		$empQuery .= " LEFT JOIN comments comt ON o.id=comt.offerId ";
	}

	$empQuery .= " WHERE 1 " . $searchQuery . " order by " . $orderQuery . " limit " . $row . "," . $rowperpage;

	$empRecords = $link->query($empQuery);

	$data = array();
	$userData = getUsersShortLongNames();

	$offer_numbers_for_rev_no = [];

	while ($row = $empRecords->fetch_object()) {
		$cpIdArray = array_unique(explode(";", $row->calcPersons));
		$cpIdString = "";

		foreach ($cpIdArray as $value) {
			if ($value != "" && $value != 0)
				$cpIdString .= $userData[$value][1] . ", ";
		}
		if (strlen($cpIdString) != 0)
			$cpIdString = substr($cpIdString, 0, -2);

		$cpIdString = mb_convert_encoding($cpIdString, 'UTF-8', 'UTF-8');

		if ($row->offer == '0000-00-00')
			$offerDate = '';
		else
			$offerDate = $row->offer;
		$orderDateHelp = "";
		if ($row->order == '0000-00-00')
			$orderDate = '';
		else
			$orderDate = $row->order;
		if ($row->requestedOrderDate == '0000-00-00')
			$requestedOrderDate = '';
		else {
			$requestedOrderDate = $row->requestedOrderDate;
			if ($requestedOrderDate < date("Y-m-d"))
				$orderDateHelp = "Past";
		}
		if ($row->inquiry == '0000-00-00')
			$inquiryDate = '';
		else
			$inquiryDate = $row->inquiry;
		if ($row->nextContactDate == '0000-00-00' || $row->nextContactDate == '') {
			$nextContactDate = '';
			$nextContactDateAX = '0000-00-00';
		} else {
			$nextContactDate = $row->nextContactDate;
			$nextContactDateAX = $row->nextContactDate;
		}

		$segs = "";
		$segm = explode(";", $row->segment);
		foreach ($segm as $seg) {
			$segs .= translateSegment($seg) . " ";
		}

		$oneType = "";
		if ($row->clientShortName == "")
			$oneType = $row->typeEnd;
		else
			$oneType = $row->type;
		$otS = "";
		switch ($row->OT) {
			case 'A':
				$otS = "<strong>A</strong>";
				break;
			case 'B':
				$otS = "B";
				break;
			case 'E':
				$otS = "<strong>E</strong>";
				break;
			case 'Q':
				$otS = "Q";
				break;
		}

		$folderPath = $row->offerNo;
		if ($row->folder_link != "") {
			$path = "\\offers\\" . $row->folder_link;
			$server_addr = "10.201.1.200";
			$url = $server_addr . $path;
			$folderPath = "<a href='localexplorer:\\\\" . $url . "'>" . $row->offerNo . "</a>";
		}
		$offer = array(
			"id" => $row->id,
			"offer_no_raw" => $row->offerNo,
			"AX" => $row->AX,
			"conDate" => ($row->id) . "[-]" . ($row->AX),
			"reason" => ($row->reason),
			"OT" => $otS,
			"offerNo" => $folderPath,
			"oldOfferNo" => ($row->oldOfferNo),
			"projectName" => ($row->projectNo) . "-" . ($row->projectName),
			"projectNo" => ($row->projectNo),
			"oneClient" => $oneType . "{-}" . ($row->oneClient),
			"client" => ($row->type) . "{-}" . ($row->clientShortName),
			"inquiryNo" => $row->inquiryNo,
			"zip" => ($row->zip),
			"city" => ($row->city),
			"marketR" => ($row->marketR),
			"country" => ($row->country),
			"countryE" => ($row->Ecountry),
			"shortNameEnd" => ($row->typeEnd) . "{-}" . ($row->shortNameEnd),
			"endclientInquiryNo" => $row->endclientInquiryNo,
			"Ezip" => ($row->Ezip),
			"cityEnd" => ($row->cityEnd),
			"marketE" => ($row->marketE),
			"scope" => str_replace(";", "; ", ($row->scope)),
			"segment" => $segs,
			"SOC" => ($row->SOC),
			"productionLocation" => ($row->productionLocation),
			"WHP" => ($row->WHP),
			"WHS" => ($row->WHS),
			"Company" => ($row->company),
			"InR" => "<strong>" . ($row->InR) . "</strong>",
			"InID" => ($row->InID),
			"InTL" => ($row->client == 0 ? $row->InTLE : $row->InTL),
			"calcPersons" => $cpIdString,
			"InKAM" => ($row->client == 0 ? $row->InKAME : $row->InKAM),
			"InASM" => ($row->client == 0 ? $row->InASME : $row->InASM),
			"inquiry" => dateFormat($inquiryDate),
			"request" => ($row->request == '0000-00-00') ? '' : dateFormat($row->request),
			"offer" => dateFormat($offerDate),
			"oi" => $row->oi,
			"requestedOrderDate" => dateFormat($requestedOrderDate) . "[-]" . ($row->AX) . "[-]" . $orderDateHelp,
			"nextContactDate" => dateFormat($nextContactDate),
			"InF" => ($row->InF),
			"lastComment" => ($row->lastComment),
			"OVE" => "<strong>" . number_format(($row->OVE), 0, ".", " ") . "</strong>",
			"OV" => number_format(($row->OV), 1, ".", " "),
			"GO" => ($row->GO),
			"GET" => ($row->GET),
			"OVgg" => number_format(round($row->OVgg), 0, ".", " "),
			"orderNo" => ($row->orderNo),
			"productionOrderNo" => ($row->productionOrderNo),
			"orderCompany" => ($row->orderCompany),
			"inPM" => ($row->inPM),
			"order" => dateFormat($orderDate),
			"requestedDeliveryDate" => ($row->requestedDeliveryDate == '0000-00-00') ? '' : dateFormat($row->requestedDeliveryDate),
			"deliveryDate" => ($row->deliveryDate == '0000-00-00') ? '' : dateFormat($row->deliveryDate),
			"orderValue" => "<strong>" . number_format(($row->orderValue), 0, ".", " ") . "</strong>",
			"gCMe" => round($row->gCMe),
			"gCMp" => round($row->gCMp),
			"comments" => "",
		);

		$offer_numbers_for_rev_no[$row->id] = $row->offerNo;

		$rev_no_result = $link->query("SELECT MAX(revision) AS revNo FROM components WHERE offerNo='" . $row->offerNo . "' ");

		$conDate = "";
		$circle_text = "";

		if (!empty(mysqli_num_rows($rev_no_result))) {
			$row_rev_no_result = $rev_no_result->fetch_assoc();

			$revNo = $row_rev_no_result['revNo'];

			if (!empty($revNo)) {
				$circle_text = "R";
			}
		}

		if (($row->OV == "0" || empty($row->OV)) && $offer['AX'] == "") {
			$conDate = "<div class='circle-blue'>" . $circle_text . "</div>";
		} else {
			if ($offer['AX'] != "Order" && $offer['AX'] != "Lost" && $offer['AX'] != "Terminated") {
				$today = date("Y-m-d");
				if ($nextContactDateAX == "0000-00-00") {
					if (date("Y-m-d", strtotime("+3 days", strtotime($offer['offer']))) <= $today) {
						$conDate = "<div class='circle-red'>" . $circle_text . "</div>";
					} else {
						$conDate = "<div class='circle-green'>" . $circle_text . "</div>";
					}
				} else {
					if ($nextContactDateAX >= $today) {
						$conDate = "<div class='circle-green'>" . $circle_text . "</div>";
					} else {
						$conDate = "<div class='circle-orenge'>" . $circle_text . "</div>";
					}
				}
			} else {
				$conDate = "<div class='circle-grey'>" . $circle_text . "</div>";
			}
		}

		$offer['conDate'] = $conDate . "[-]" . ($offer['id']) . "[-]" . ($offer['AX']);

		$saved_comments = $link->query("SELECT note as comment, offerId FROM comments WHERE offerId = " . $offer['id'] . " ORDER BY id DESC LIMIT 1");

		if (!empty(mysqli_num_rows($saved_comments))) {
			$comments_arr = [];
			$output_comments = "";
			while ($row = $saved_comments->fetch_assoc()) {
				if ($offer['id'] == $row['offerId']) {
					$row['comment'] = nl2br($row['comment']);
					$comments_arr[] = $row['comment'];
				}
			}
			if (count($comments_arr) > 0) {
				$output_comments = implode("<br>", $comments_arr);
			}
			$output_comments = rtrim($output_comments, "; ");

			$offer['comments'] = '<div class="comment-box">' . $output_comments . '</div>';
		}

		$data[] = $offer;
	}


	$result = $link->query("SELECT SUM(OVE) AS OVE, SUM(orderValue) AS orderValue, SUM(OVgg) AS OVgg, AVG(GxG) AS GxG FROM ($searchQueryOffers) t");
	$row = $result->fetch_object();

	## Response
	$response = array(
		"draw" => intval($draw),
		"iTotalRecords" => $totalRecords,
		"iTotalOVE" => number_format(round($row->OVE), 0, "", " "),
		"iTotalOV" => number_format(round($row->orderValue), 0, "", " "),
		"iTotalOVgg" => number_format(round($row->OVgg), 0, "", " "),
		"iAvgGxG" => round($row->GxG),
		"iTotalDisplayRecords" => $totalRecordwithFilter,
		"iTotalDisplayRecordsTop" => number_format(round($totalRecordwithFilter), 0, "", " "),
		"aaData" => $data,
		'query' => ''
	);
} else {
	$response = array(
		"draw" => intval($draw),
		"iTotalRecords" => 0,
		"iTotalOVE" => 0,
		"iTotalOV" => 0,
		"iTotalOVgg" => 0,
		"iAvgGxG" => 0,
		"iTotalDisplayRecords" => 0,
		"iTotalDisplayRecordsTop" => 0,
		"aaData" => [],
		'query' => ''
	);
}

echo json_encode($response);
