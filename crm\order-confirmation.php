<?php require('header.php'); ?>
<style>
    .select2-container {
        width: 100% !important;
        text-align: left;
    }

    .select2-container .select2-selection--single {
        height: 36px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 34px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
    }

    .select2-container--default .select2-selection--single {
        border: 1px solid #ced4da;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        font-size: 1rem;
        font-family: inherit;
        font-weight: 430;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        margin-top: 2px;
        line-height: 20px;
    }

    .select2-container .select2-selection--multiple {
        min-height: 28px;
    }

    .select2-selection__rendered {
        color: #6C757D !important;
    }

    #clients_processing {
        top: 16px;
        width: 98%;
        left: 1%;
        background-color: #dedef3;
        margin: 0;
    }

    .form-control-sm {
        height: 38px !important;
    }

    .dataTables_length label {
        margin: 0;
    }
</style>
<script>
    function redBorder() {
        $('[required]').each(function() {
            if ($(this).hasClass("select2")) {
                if ($(this).val() == '' || $(this).val() == null)
                    $(this).next().children().children().addClass("borderRed");
                else
                    $(this).next().children().children().removeClass("borderRed");
            } else
            if ($(this).val() == '' || $(this).val() == null)
                $(this).addClass("borderRed");
            else
                $(this).removeClass("borderRed");
        })
        setTimeout(function() {
            $('.borderRed').removeClass('borderRed');
        }, 5000);
    }

    function saveColumns(columns, table) {
        var visibleCols = [];
        columns.every(function() {
            if (this.visible() == true)
                visibleCols.push(table.settings().init().columns[this[0][0]].name);
        })
        $.ajax({
            url: "assets/php/ajaxHandeler.php",
            method: "get",
            data: {
                action: 'saveClientCols',
                cols: visibleCols
            }
        })
    }
</script>

<body class="widescreen adminbody-void">
    <?php
    if ($_SESSION['plasticonDigitalUser']['crm']['orderConfirmation'] != '1')
        header("Location: index.php");
    $_SESSION['url'] = 'order-confirmation.php';
    require('menu.php');
    ?>
    <div class="content-page">
        <div class="content">
            <div class="container-fluid">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                    <div class="card mb-3">
                        <div class="card-body">
                            <div id="accordion" style="margin:0px 0px;">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="row">
                                            <div class="text-center">
                                                <button id="getFilters" title="Expand filters" data-toggle="collapse" href="#collapseOne" style="padding:2px 9px; margin-right:10px;" class="btn btn-primary"><i style="font-size:30px;" class="fas fa-angle-down"></i></button>
                                            </div>
                                            <div class="col has-search"><span class="fa fa-search form-control-feedback"></span>
                                                <form class="reset-box">
                                                    <input class="form-control searchBox" placeholder="Search" id="mySearchBox">
                                                    <button type="reset" class="reset-button" title="Clear">
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="collapseOne" class="collapse" data-parent="#accordion">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-lg-4">
                                                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == '1') { ?><label class="filterLabel d-inline"> Sales Company (SC)</label class="filterSpan">
                                                        <span class="filterSpan"><select class="form-control select2 filterStyle" name="cmpFilter[]" id="cmpFilter" multiple="multiple">
                                                                <option value="all" selected>Show all</option>
                                                                <?php echo getCompanies(); ?>
                                                            </select></span><?php } ?>
                                                </div>
                                                <div class="col-lg-4">
                                                    <label class="filterLabel d-inline"> Production Company (PC)</label>
                                                    <span class="filterSpan"><select class="form-control select2 filterStyle" name="prodFilter[]" id="prodFilter" multiple="multiple">
                                                            <option value="all" selected>Show all</option>
                                                            <?php echo getCompanies(); ?>
                                                        </select></span>
                                                </div>
                                                <div class="col-lg-4">

                                                </div>
                                            </div>
                                            <hr>
                                            <div class="row justify-content-center">
                                                <div class="col-lg-2 text-center"><label style="width:100%;"><br><button class="form-control btn btn-danger" id="filterReset"><img src="assets/images/reset-filters.png" style="width:25px;margin-right:10px;">Reset filters</button></label></div>
                                                <div class="col-lg-2"><label style="width:100%;"><br><button class="form-control btn btn-danger" id="restoreDefault"><i style="font-size:20px;margin-right:10px;" class="fas fa-undo"></i>Default view</button></label></div>
                                                <div class="col-lg-2 text-center"><label style="width:100%;"><br><button class="form-control btn btn-primary" id="checkAll"><i style="font-size:20px;margin-right:10px;" class="fas fa-check"></i>Select all</button></label></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="table-responsive" style="margin-top:5px;">
                                <table id="order-confirmation" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
                                    <thead>
                                        <tr>
                                            <th title="Quote number">QN</th>
                                            <th title="Sales order number">SON</th>
                                            <th title="Production order number">PON</th>
                                            <th>Description</th>
                                            <th>Customer</th>
                                            <th title="Order value">OV</th>
                                            <th title="Sales company">SC</th>
                                            <th title="Production company">PC</th>
                                            <th title="Responsible">R</th>
                                            <th title="Project Manager">PM</th>
                                            <th>Diameter</th>
                                            <th title="Protection layer">Prot</th>
                                            <th title="Static material">Stat</th>
                                            <th title="Production work hours">PHP</th>
                                            <th class="text-center">Actions</th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addGlass">
                                <div class="modal-dialog" style="min-width:60% !important;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Add wood and glass order.</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST" enctype="multipart/form-data">
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Client: *<br>
                                                                <select class="form-control select2-client" name="clientGlass" required></select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales order no: *<br>
                                                                <input type="text" placeholder="Sales order no" class="form-control " name="salesOrderNumberGlass" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Inside sales:<br>
                                                                <select name="insideSalesIdGlass" class="select2 form-control">
                                                                    <option selected disabled>Select</option>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales article: *<br>
                                                                <input type="text" placeholder="Scope" class="form-control " name="scopeGlass">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales company: *<br>
                                                                <select name="salesCompanyGlass" class="select2 form-control">
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Production order no: *<br>
                                                                <input type="text" placeholder="Production order no" class="form-control " name="productionOrderNumberGlass" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Responsible sales: *<br>
                                                                <select name="responsibleSCGlass" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Diameter: *<br>
                                                                <input type="number" step="0.01" min="0" placeholder="Diameter" class="form-control " name="diameterGlass">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Production company: *<br>
                                                                <select name="productionCompanyGlass" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3"></div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Supporting manager:<br>
                                                                <select name="supportingManagerIdGlass" class="select2 form-control">
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo listPMs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Liner material: *<br>
                                                                <select name="linerMaterialGlass" class="form-control select2">
                                                                    <option value="">Select</option>
                                                                    <option value="CBL+BPO">CBL+BPO</option>
                                                                    <option value="CBL">CBL</option>
                                                                    <option value="CBL+graphite">CBL+graphite</option>
                                                                    <option value="CBL+BÜFA">CBL+BÜFA</option>
                                                                    <option value="CBL+Nexus">CBL+Nexus</option>
                                                                    <option value="CBL+SiC">CBL+SiC</option>
                                                                    <option value="CBL2,5mm">CBL2,5mm</option>
                                                                    <option value="CBL3,3mm">CBL3,3mm</option>
                                                                    <option value="CBL5mm">CBL5mm</option>
                                                                    <option value="CBL6,3mm">CBL6,3mm</option>
                                                                    <option value="F(PVDF)">F(PVDF)</option>
                                                                    <option value="F(PVDF-el)">F(PVDF-el)</option>
                                                                    <option value="F(ECTFE)">F(ECTFE)</option>
                                                                    <option value="F(FEP)">F(FEP)</option>
                                                                    <option value="F(PFA)">F(PFA)</option>
                                                                    <option value="F(m-PTFE)">F(m-PTFE)</option>
                                                                    <option value="TP(PE100)">TP(PE100)</option>
                                                                    <option value="TP(PP-C-PK)">TP(PP-C-PK)</option>
                                                                    <option value="TP(PP-el)">TP(PP-el)</option>
                                                                    <option value="TP(PPh100)">TP(PPh100)</option>
                                                                    <option value="TP(PPh2222)">TP(PPh2222)</option>
                                                                    <option value="TP(PPR)">TP(PPR)</option>
                                                                    <option value="TP(PVC MZ)">TP(PVC MZ)</option>
                                                                    <option value="TP(PVC CAW)">TP(PVC CAW)</option>
                                                                    <option value="TP(PVC EN)">TP(PVC EN)</option>
                                                                    <option value="TP(PVC-U grey)">TP(PVC-U grey)</option>
                                                                    <option value="TP(PVC-U red)">TP(PVC-U red)</option>
                                                                    <option value="TP(PVC NL)">TP(PVC NL)</option>
                                                                    <option value="TP(PVC Dekadur plus)">TP(PVC Dekadur plus)</option>
                                                                    <option value="TP(C-PVC)">P(C-PVC)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Other">Other</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label>Delivery date: *<br>
                                                                <input type="date" class="form-control form-100" name="fabricationEGlass" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Project manager: *<br>
                                                                <select name="responsiblePCGlass" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo listPMs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Static material: *<br>
                                                                <select name="staticMaterialGlass" class="form-control select2">
                                                                    <option value="">Select</option>
                                                                    <option value="GRP(D411)">GRP(D411)</option>
                                                                    <option value="GRP(D441-400)">GRP(D441-400)</option>
                                                                    <option value="GRP(D451-400)">GRP(D451-400)</option>
                                                                    <option value="GRP(D470-300)">GRP(D470-300)</option>
                                                                    <option value="GRP(D470HT-400)">GRP(D470HT-400)</option>
                                                                    <option value="GRP(D510-A40)">GRP(D510-A40)</option>
                                                                    <option value="GRP(D510-C350)">GRP(D510-C350)</option>
                                                                    <option value="GRP(D510N)">GRP(D510N)</option>
                                                                    <option value="GRP(P104T)">GRP(P104T)</option>
                                                                    <option value="GRP(P122T)">GRP(P122T)</option>
                                                                    <option value="GRP(Syn 0266 N4)">GRP(Syn 0266 N4)</option>
                                                                    <option value="GRP(Viapal 797-59)">GRP(Viapal 797-59)</option>
                                                                    <option value="GRP(Vipel F010)">GRP(Vipel F010)</option>
                                                                    <option value="GRP(A Enova FW1045)">GRP(A Enova FW1045)</option>
                                                                    <option value="GRP(A430)">GRP(A430)</option>
                                                                    <option value="GRP(A580)">GRP(A580)</option>
                                                                    <option value="GRP(A590)">GRP(A590)</option>
                                                                    <option value="GRP(Aropol UN 2)">GRP(Aropol UN 2)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Thermoplastic">Thermoplastic</option>
                                                                    <option value="GRP(iso-resin)">GRP(iso-resin)</option>
                                                                    <option value="GRP(ortho-resin)">GRP(ortho-resin)</option>
                                                                    <option value="GRP(vinylester-resin)">GRP(vinylester-resin)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Other">Other</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label>Production hours:<br>
                                                                <input type="number" placeholder="Production hours" class="form-control form-100" name="productionHoursGlass" required></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <h4>Files</h4>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP") { ?>
                                                            <div class="row justify-content-center">
                                                                <label>Disposal:<br>
                                                                    <input type="file" name="disposalFileGlass" required></label>
                                                            </div>
                                                        <?php } ?>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label>Order:<br>
                                                                <input type="file" name="orderFileGlass" <?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP") echo "required"; ?>></label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label>GCE:<br>
                                                                <input type="file" name="gceFileGlass"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" onclick="redBorder()" name="saveGlass" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="confirmModal">
                                <div class="modal-dialog" style="min-width:60% !important;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Confirm fabrication end date.</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST" enctype="multipart/form-data">
                                            <div class="modal-body">
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Client: *<br>
                                                                <select class="form-control select2-client" name="client" required disabled></select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales order no: *<br>
                                                                <input type="text" placeholder="Sales order no" class="form-control " name="salesOrderNumber" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Inside sales:<br>
                                                                <select name="insideSalesId" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales article: *<br>
                                                                <input type="text" placeholder="Scope" class="form-control " name="scope" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales company: *<br>
                                                                <select name="salesCompany" class="select2 form-control" required disabled>
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo getCompanies(); ?>
                                                                </select>

                                                                <select name="sc_hidden" class="form-control d-none">
                                                                    <?php echo getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Offer number: *<br>
                                                                <input type="text" placeholder="Offer number" class="form-control " name="offerNumber" required disabled>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Responsible sales: *<br>
                                                                <select name="responsibleSC" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Diameter: *<br>
                                                                <input type="number" step="0.01" min="0" placeholder="Diameter" class="form-control " name="diameter" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Production company: *<br>
                                                                <select name="productionCompany" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo getCompanies(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Production order no: *<br>
                                                                <input type="text" placeholder="Production order no" class="form-control " name="productionOrderNumber" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Supporting manager:<br>
                                                                <select name="supportingManagerId" class="select2 form-control">
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo listPMs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Liner material: *<br>
                                                                <select name="linerMaterial" class="form-control select2" required>
                                                                    <option value="">Select</option>
                                                                    <option value="CBL+BPO">CBL+BPO</option>
                                                                    <option value="CBL">CBL</option>
                                                                    <option value="CBL+graphite">CBL+graphite</option>
                                                                    <option value="CBL+BÜFA">CBL+BÜFA</option>
                                                                    <option value="CBL+Nexus">CBL+Nexus</option>
                                                                    <option value="CBL+SiC">CBL+SiC</option>
                                                                    <option value="CBL2,5mm">CBL2,5mm</option>
                                                                    <option value="CBL3,3mm">CBL3,3mm</option>
                                                                    <option value="CBL5mm">CBL5mm</option>
                                                                    <option value="CBL6,3mm">CBL6,3mm</option>
                                                                    <option value="F(PVDF)">F(PVDF)</option>
                                                                    <option value="F(PVDF-el)">F(PVDF-el)</option>
                                                                    <option value="F(ECTFE)">F(ECTFE)</option>
                                                                    <option value="F(FEP)">F(FEP)</option>
                                                                    <option value="F(PFA)">F(PFA)</option>
                                                                    <option value="F(m-PTFE)">F(m-PTFE)</option>
                                                                    <option value="TP(PE100)">TP(PE100)</option>
                                                                    <option value="TP(PP-C-PK)">TP(PP-C-PK)</option>
                                                                    <option value="TP(PP-el)">TP(PP-el)</option>
                                                                    <option value="TP(PPh100)">TP(PPh100)</option>
                                                                    <option value="TP(PPh2222)">TP(PPh2222)</option>
                                                                    <option value="TP(PPR)">TP(PPR)</option>
                                                                    <option value="TP(PVC MZ)">TP(PVC MZ)</option>
                                                                    <option value="TP(PVC CAW)">TP(PVC CAW)</option>
                                                                    <option value="TP(PVC EN)">TP(PVC EN)</option>
                                                                    <option value="TP(PVC-U grey)">TP(PVC-U grey)</option>
                                                                    <option value="TP(PVC-U red)">TP(PVC-U red)</option>
                                                                    <option value="TP(PVC NL)">TP(PVC NL)</option>
                                                                    <option value="TP(PVC Dekadur plus)">TP(PVC Dekadur plus)</option>
                                                                    <option value="TP(C-PVC)">P(C-PVC)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Other">Other</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label>Delivery date: *<br>
                                                                <input type="date" class="form-control form-100" name="fabricationE" required>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Sales comp. purch. ord. no:<br>
                                                                <input type="text" placeholder="Sales comp. purchase ord. no" class="form-control " name="intercompanyOrderNumber">
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Project manager: *<br>
                                                                <select name="responsiblePC" class="select2 form-control" required>
                                                                    <option selected disabled>Select</option>
                                                                    <?php echo listPMs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label style="width:200px;">Static material: *<br>
                                                                <select name="staticMaterial" class="form-control select2" required>
                                                                    <option value="">Select</option>
                                                                    <option value="GRP(D411)">GRP(D411)</option>
                                                                    <option value="GRP(D441-400)">GRP(D441-400)</option>
                                                                    <option value="GRP(D451-400)">GRP(D451-400)</option>
                                                                    <option value="GRP(D470-300)">GRP(D470-300)</option>
                                                                    <option value="GRP(D470HT-400)">GRP(D470HT-400)</option>
                                                                    <option value="GRP(D510-A40)">GRP(D510-A40)</option>
                                                                    <option value="GRP(D510-C350)">GRP(D510-C350)</option>
                                                                    <option value="GRP(D510N)">GRP(D510N)</option>
                                                                    <option value="GRP(P104T)">GRP(P104T)</option>
                                                                    <option value="GRP(P122T)">GRP(P122T)</option>
                                                                    <option value="GRP(Syn 0266 N4)">GRP(Syn 0266 N4)</option>
                                                                    <option value="GRP(Viapal 797-59)">GRP(Viapal 797-59)</option>
                                                                    <option value="GRP(Vipel F010)">GRP(Vipel F010)</option>
                                                                    <option value="GRP(A Enova FW1045)">GRP(A Enova FW1045)</option>
                                                                    <option value="GRP(A430)">GRP(A430)</option>
                                                                    <option value="GRP(A580)">GRP(A580)</option>
                                                                    <option value="GRP(A590)">GRP(A590)</option>
                                                                    <option value="GRP(Aropol UN 2)">GRP(Aropol UN 2)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Thermoplastic">Thermoplastic</option>
                                                                    <option value="GRP(iso-resin)">GRP(iso-resin)</option>
                                                                    <option value="GRP(ortho-resin)">GRP(ortho-resin)</option>
                                                                    <option value="GRP(vinylester-resin)">GRP(vinylester-resin)</option>
                                                                    <option value="Steel">Steel</option>
                                                                    <option value="Other">Other</option>
                                                                </select>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">

                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row justify-content-center">
                                                            <label>Production hours:<br>
                                                                <input type="number" placeholder="Production hours" class="form-control form-100" name="productionHours" required></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <h4>Files</h4>
                                                </div>
                                                <div class="row">
                                                    <div class="col-lg-4">
                                                        <?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP") { ?>
                                                            <div class="row justify-content-center">
                                                                <label>Disposal:<br>
                                                                    <input type="file" name="disposalFile" required></label>
                                                            </div>
                                                        <?php } ?>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label>Order:<br>
                                                                <input type="file" name="orderFile" <?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP") echo "required"; ?>></label>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-4">
                                                        <div class="row justify-content-center">
                                                            <label>GCE:<br>
                                                                <input type="file" name="gceFile"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="hidden" class="form-control form-100" name="confirmId" id="confirmId" required>
                                                <input type="submit" onclick="redBorder()" name="saveConf" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <?php

                            if(isset($_GET['export_error'])) {

                                $export_error_offer_id = 0;
                                $export_error_offer_no = 0;
                                if(isset($_GET['offer_id'])) {
                                    $export_error_offer_id = $_GET['offer_id'];
                                    $export_error_offer_no = getOfferNo($export_error_offer_id);
                                }

                                $err_val = $_GET['export_error'];

                                $offer = getOfferInfo($export_error_offer_id);
                                $sales_company = $_GET['SA'];
                                $production_company = $_GET['PC'];
                                
                                $msg = "";
                                
                                if($err_val == 1) {
                                    $err_msg = "Not exported to OMS. Empty Offer number / PON / SON.";
                                } else if($err_val == 2) {
                                    $err_msg = "Not exported to OMS. Check OMS: <a href=\"".OMS_LINK."/article.php?id=".getOmsId($export_error_offer_no, $production_company, $sales_company)."\" target=\"_blank\">".$export_error_offer_no."</a> and CRM: <a href=\"".CRM_LINK."/offer.php?id=".$export_error_offer_id."\" target=\"_blank\">".$export_error_offer_no."</a>.";
                                } else if ($err_val == 3) {
                                    $err_msg = "Not exported to OMS. Data transfer error for offer <a href=\"".CRM_LINK."/offer.php?id=".$export_error_offer_id."\" target=\"_blank\"> ".$export_error_offer_no."</a>.";
                                } else if ($err_val == 4) {
                                    $err_msg = "Not exported to OMS. Files upload error for offer <a href=\"".CRM_LINK."/offer.php?id=".$export_error_offer_id."\" target=\"_blank\"> ".$export_error_offer_no."</a>.";
                                }

                                alertDanger($err_msg);

                            }

                            if (isset($_GET['exported']))
                                alertSuccess("Order exported to OMS");
                            if (isset($_POST['saveGlass'])) {
                                $link = connect();
                                $oms = connectOMS();
                                $milestones = connectMilestones();
                                $lastOfferNo = lastOfferNo();
                                if (date("m") >= 7) {
                                    $offerNoYear = substr($lastOfferNo, 0, 2);
                                    $sumThisYear = substr(date("Y") + 1, 2, 2);
                                    if ($offerNoYear != $sumThisYear)
                                        $offerNo = $sumThisYear . "700001";
                                    else {
                                        $offerNo = explode("-", $lastOfferNo);
                                        $offerNo = $offerNo[0] + 1;
                                    }
                                } else {
                                    $offerNo = explode("-", $lastOfferNo);
                                    $offerNo = $offerNo[0] + 1;
                                }

                                $offer_exists = true;

                                $pon = $_POST['productionOrderNumberGlass'];
                                $son = $_POST['salesOrderNumberGlass'];
                                $production_company = $_POST['productionCompanyGlass'];
                                
                                if(empty($offerNo) || empty($pon) || empty($son) || empty($production_company)) {
                                    header("Location: order-confirmation.php?export_error=1");
                                    exit();
                                }

                                $query = sprintf(
                                    "INSERT IGNORE INTO `offers`(`offerNo`, `scope`, V, InR, company, client, glassOffer) VALUES ('%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($link, $offerNo),
                                    mysqli_real_escape_string($link, $_POST['scopeGlass']),
                                    mysqli_real_escape_string($link, $_POST['responsibleSCGlass']),
                                    mysqli_real_escape_string($link, strip_tags(inicialy($_POST['responsibleSCGlass']))),
                                    mysqli_real_escape_string($link, $_POST['salesCompanyGlass']),
                                    mysqli_real_escape_string($link, $_POST['clientGlass']),
                                    mysqli_real_escape_string($link, 1)
                                );
                                $link->query($query);

                                $sales_company =  $_POST['salesCompanyGlass'];
                            
                                $pon_exists = checkIfOfferPonExistsInOms($pon, $production_company);
            
                                if($pon_exists) {
                                    $offer_id = getOfferId($offerNo);
                                    $link->close();
                                    $oms->close();
                                    $milestones->close();
                                    header("Location: order-confirmation.php?export_error=2&offer_id=" . urlencode($offer_id)."&SC=".urlencode($sales_company)."&PC=".urlencode($production_company));
                                    exit();
                                }

                                // If project does not exist in OMS first try create project folder link. If success then insert project into OMS. If not then redirect with error
                                $project_folder_dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", $offerNo)))));

                                $created_project_folders = createFoldersOmsExport($project_folder_dirname);

                                if (!$created_project_folders) {
                                    $project_folder_dirname = "";

                                    removeFoldersOmsExport($created_project_folders);

                                    $offer_id = getOfferId($offer_no);

                                    $link->query(sprintf(
                                        "UPDATE components SET fabricationE='0000-00-00', confirmTimestamp='0000-00-00 00:00:00', confirmPerson='' WHERE id='%s'",
                                        mysqli_real_escape_string($link, $article['id'])
                                    ));

                                    $link->close();
                                    $oms->close();
                                    $milestones->close();

                                    header("Location: order-confirmation.php?export_error=4&offer_id=" . urlencode($offer_id));
                                    exit();
                                }


                                $query = sprintf(
                                    "INSERT IGNORE INTO `projects`(`offerNo`, `clientId`, `clientName`, `SC`, `projectManagerId`, `projectManagerFull`, `supportingManagerId`, `supportingManagerFull`, `location`, `insideSalesId`, `insideSalesFull`, `responsibleSalesId`, `responsibleSalesFull`, `status`, `isCritical`, `folder_link`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($oms, $offerNo),
                                    mysqli_real_escape_string($oms, $_POST['clientGlass']),
                                    mysqli_real_escape_string($oms, getClientName($_POST['clientGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['salesCompanyGlass']),
                                    mysqli_real_escape_string($oms, $_POST['responsiblePCGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsiblePCGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['supportingManagerIdGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['supportingManagerIdGlass'])),
                                    mysqli_real_escape_string($oms, getClientLocation($_POST['clientGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['insideSalesIdGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['insideSalesIdGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['responsibleSCGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsibleSCGlass'])),
                                    mysqli_real_escape_string($oms, "In progress"),
                                    mysqli_real_escape_string($oms, "0"),
                                    mysqli_real_escape_string($oms, $project_folder_dirname)
                                );
                                $oms->query($query);


                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `salesarticles`(`offerNo`, `name`, `SON`, `PON`, `diameter`, `PC`, `linerMaterial`, `staticMaterial`, `hours`, `projectManagerId`, `projectManagerFull`, `supportingManagerId`, `supportingManagerFull`, `status`, `isCritical`, `fabrication`, `costGroup`, `orderDate`, `qualityControl`, `folder_link`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s', '%s')",
                                    mysqli_real_escape_string($oms, $offerNo),
                                    mysqli_real_escape_string($oms, $_POST['scopeGlass']),
                                    mysqli_real_escape_string($oms, trim($_POST['salesOrderNumberGlass'])),
                                    mysqli_real_escape_string($oms, trim($_POST['productionOrderNumberGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['diameterGlass']),
                                    mysqli_real_escape_string($oms, $_POST['productionCompanyGlass']),
                                    mysqli_real_escape_string($oms, $_POST['linerMaterialGlass']),
                                    mysqli_real_escape_string($oms, $_POST['staticMaterialGlass']),
                                    mysqli_real_escape_string($oms, $_POST['productionHoursGlass']),
                                    mysqli_real_escape_string($oms, $_POST['responsiblePCGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsiblePCGlass'])),
                                    mysqli_real_escape_string($oms, $_POST['supportingManagerIdGlass']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['supportingManagerIdGlass'])),
                                    mysqli_real_escape_string($oms, "In progress"),
                                    mysqli_real_escape_string($oms, 0),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, kosztaOrders($_POST['productionOrderNumberGlass'])),
                                    mysqli_real_escape_string($oms, date("Y-m-d")),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, $project_folder_dirname)
                                ));
                                $articleId = $oms->insert_id;

                                if(empty($articleId)) {
                                    $offer_id = getOfferId($offerNo);
                                    $oms->query("DELETE FROM `projects` WHERE `offerNo` = '".$offerNo."'");
                                    $link->close();
                                    $oms->close();
                                    $milestones->close();
                                    header("Location: order-confirmation.php?export_error=3&offer_id=" . urlencode($offer_id));
                                    exit();
                                }

                                $result = file_get_contents("http://api.nbp.pl/api/exchangerates/rates/A/EUR?format=json");
                                if ($result) {
                                    $nbpData = json_decode($result, true);
                                    if (json_last_error() == JSON_ERROR_NONE && isset($nbpData['rates'][0]['mid'])) {
                                        $eurorate = (float) (round($nbpData['rates'][0]['mid'] * 100) / 100);
                                        if ($eurorate) {
                                            $oms->query(sprintf(
                                                "UPDATE `salesarticles` SET exchangeRate='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($oms, $eurorate),
                                                mysqli_real_escape_string($oms, $articleId)
                                            ));
                                        }
                                    }
                                }

                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `end_plan`, `status`) VALUES ('%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 7),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $_POST['fabricationEGlass']),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `status`) VALUES ('%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 8),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $_POST['fabricationEGlass']),
                                    mysqli_real_escape_string($milestones, date('Y-m-d', strtotime($_POST['fabricationEGlass'] . ' +2 weekdays'))),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `contract_date`, `status`) VALUES ('%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 9),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $_POST['fabricationEGlass']),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));

                                $error = false;
           
                                if (!$project_folder_dirname) {
                                    $error = true;
                                }

                                $disp_file_ok = false;
                                $order_file_ok = false;
                                $gce_file_ok = true; 

                                $article_pc = $_POST['productionCompanyGlass'];

                                $disposal = explode(".", $_FILES['disposalFile']['name']);
                                $extD = end($disposal);
                                
                                $disposal_file_name =  $article_pc . "_" .  $_POST['productionOrderNumberGlass'] . "_" . date("Y-m-d His") . "_disposal." . $extD;

                                if (!$error) {
                                    $disp_file_ok = uploadDisposalFile($project_folder_dirname, $_FILES['disposalFileGlass']['tmp_name'], $disposal_file_name);
                                    if(!$disp_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $order_file_ok = uploadOrderFile($project_folder_dirname, $_FILES['orderFileGlass']['tmp_name'], $_FILES['orderFileGlass']['name'], $_POST['productionOrderNumberGlass'], $article_pc);
                                    if(!$order_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $gce_file_ok = uploadGceFile($project_folder_dirname, $_FILES['gceFileGlass']['tmp_name'], $_FILES['gceFileGlass']['name'], $_POST['productionOrderNumberGlass'], $article_pc);
                                    if(!$gce_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $lop_list_ok = copyLopList($project_folder_dirname);
                                    if(!$lop_list_ok) {
                                        $error = true;
                                    }
                                }


                                if($error) {

                                    removeFoldersOmsExport($created_project_folders);

                                    $oms->query("DELETE FROM `projects` WHERE `offerNo` = (SELECT `offerNo` FROM `salesarticles` WHERE `id` = '" . $articleId . "')");
                                    $oms->query("DELETE FROM `salesarticles` WHERE `id` = '" . $articleId . "'");
                                    $milestones->query("DELETE FROM `dates` WHERE `a_id` = '" . $articleId . "'");
    
                                    $offer_id = getOfferId($offerNo);

                                    $link->close();
                                    $oms->close();
                                    $milestones->close();

                                    header("Location: order-confirmation.php?export_error=4&offer_id=" . urlencode($offer_id));
                                    exit();
                                }
              

                                $link->close();
                                $oms->close();
                                require("assets/libs/PHPMailer/mailAtt.php");
                                $cl = getClientName($_POST['clientGlass']);
                                $tittle = "New order transfered to OMS: " . $_POST['salesOrderNumberGlass'] . "-" . $_POST['productionOrderNumberGlass'];
                                $tresc = "Order " . $_POST['salesOrderNumberGlass'] . "-" . $_POST['productionOrderNumberGlass'] . " has been transfered to OMS<br><br>
Sales article: " . $_POST['scopeGlass'] . " <br>
Offer: " . $offerNo . " <br>

Production hours: " . $_POST['productionHoursGlass'] . " <br>
Weight: 0 <br>
Static material: " . $_POST['staticMaterialGlass'] . "<br>
Liner material: " . $_POST['linerMaterialGlass'] . "<br>
Sales company: " . $_POST['salesCompanyGlass'] . "<br>
Production company: " . $_POST['productionCompanyGlass'] . "<br><br>

Client: " . $cl . "<br><br>


Responsible: " . getNameAndSurname($_POST['responsiblePCGlass']);
                                $mails = [];
                                $i = 1;
                                if (!in_array($_SESSION['plasticonDigitalUser']['email'], $mails)) {
                                    $mails[] = $_SESSION['plasticonDigitalUser']['email'];
                                }
                                echo "<div class='hidden'>asa";
                                if ($_POST['productionCompany'] == "PP") {

                                    if (count($mails) > 0) {
                                        for ($i = 0; $i < sizeof($mails); $i++) {
                                            wyslijMaila($mails[$i], $tresc, $tittle, '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                        }
                                    }
                                }
                                echo "</div>";
                            }
            
                            if (isset($_POST['saveConf'])) {

                                $article = getComponentInfo($_POST['confirmId']);
                                $offer = getOfferInfo(getOfferId($article['offerNo']));
                                
                                $offer_exists = true;

                                $offer_no = $offer['offerNo'];
                                $pon = $_POST['productionOrderNumber'];
                                $son = $_POST['salesOrderNumber'];
                                $production_company = $_POST['productionCompany'];

                                if(empty($offer_no) || empty($pon) || empty($son) || empty($production_company)) {
                                    header("Location: order-confirmation.php?export_error=1");
                                    exit();
                                }

                                $offer_id = getOfferId($offer_no);

                                $sales_company = $_POST['sc_hidden'];

                                $pon_exists = checkIfOfferPonExistsInOms($pon, $production_company);
                            
                                if($pon_exists) {
                                    header("Location: order-confirmation.php?export_error=2&offer_id=" . urlencode($offer_id)."&SC=".urlencode($sales_company)."&PC=".urlencode($production_company));
                                    exit();
                                }

                                $link = connect();
                                $oms = connectOMS();
                                $milestones = connectMilestones();
                         
                                $fabricationEnd = $_POST['fabricationE'];
        
                                $link->query(sprintf(
                                    "UPDATE components SET fabricationE='%s', confirmTimestamp=NOW(), confirmPerson='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, $fabricationEnd),
                                    mysqli_real_escape_string($link, $_SESSION['plasticonDigitalUser']['imie'] . " " . $_SESSION['plasticonDigitalUser']['nazwisko']),
                                    mysqli_real_escape_string($link, $article['id'])
                                ));


                                $link->query(sprintf(
                                    "UPDATE components SET orderNo='%s', orderNoProduction='%s', intercompanyOrderNo='%s' WHERE id='%s'",
                                    mysqli_real_escape_string($link, trim($_POST['salesOrderNumber'])),
                                    mysqli_real_escape_string($link, trim($_POST['productionOrderNumber'])),
                                    mysqli_real_escape_string($link, $_POST['intercompanyOrderNumber']),
                                    mysqli_real_escape_string($link, $article['id'])
                                ));
                                
                                countSummaryOffer($offer_no);
                                summarySonPon($offer_no);

                                $projects_insert = false;


                                $ifResult = $oms->query(sprintf(
                                    "SELECT * FROM projects WHERE offerNo='%s'",
                                    mysqli_real_escape_string($oms, $offer_no)
                                ));

                                
                                $project_folder_dirname = "";

                                // If project exists in OMS then do the following:
                                // 1. Check if project folder link is empty
                                // 2. If empty, create project folder link. If success then update OMS project folder link. If not then remove project from OMS and redirect with error
                                // 3. If not empty, use existing project folder link
                                if($ifResult->num_rows > 0) {

                                    $project_folder_link = $ifResult->fetch_assoc()['folder_link'];

                                    if(empty($project_folder_link)) {
                                        $project_folder_dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", $offer_no)))));
    
                                        $created_project_folders = createFoldersOmsExport($project_folder_dirname);
    
                                        if(!$created_project_folders) {

                                            $project_folder_dirname = "";

                                            removeFoldersOmsExport($created_project_folders);
                                            
                                            $link->query(sprintf(
                                                "UPDATE components SET fabricationE='0000-00-00', confirmTimestamp='0000-00-00 00:00:00', confirmPerson='' WHERE id='%s'",
                                                mysqli_real_escape_string($link, $article['id'])
                                            ));
        
                                            $link->close();
                                            $oms->close();
                                            $milestones->close();

                                            header("Location: order-confirmation.php?export_error=4&offer_id=" . urlencode($offer_id));
                                            exit();

                                        }
    
                                        $oms->query(sprintf(
                                            "UPDATE `projects` SET folder_link='%s' WHERE id='%s'",
                                            mysqli_real_escape_string($oms, $project_folder_dirname),
                                            mysqli_real_escape_string($oms, $ifResult->fetch_assoc()['id'])
                                        ));

                                    } else {
                                        $project_folder_dirname = $project_folder_link;
                                    }
    
                                }

                                $created_project_folders = [];

                                if ($ifResult->num_rows == 0) {

                                    $projects_insert = true;

                                    // If project does not exist in OMS first try create project folder link. If success then insert project into OMS. If not then redirect with error
                                    $project_folder_dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", $offer_no)))));

                                    $created_project_folders = createFoldersOmsExport($project_folder_dirname);

                                    if(!$created_project_folders) {
                                        $project_folder_dirname = "";

                                        removeFoldersOmsExport($created_project_folders);

                                        $offer_id = getOfferId($offer_no);
                                        
                                        $link->query(sprintf(
                                            "UPDATE components SET fabricationE='0000-00-00', confirmTimestamp='0000-00-00 00:00:00', confirmPerson='' WHERE id='%s'",
                                            mysqli_real_escape_string($link, $article['id'])
                                        ));
    
                                        $link->close();
                                        $oms->close();
                                        $milestones->close();

                                        header("Location: order-confirmation.php?export_error=4&offer_id=" . urlencode($offer_id));
                                        exit();
                                    }

                                    $query = sprintf(
                                        "INSERT IGNORE INTO `projects`(`offerNo`, `clientId`, `clientName`, `SC`, `deliveryDate`, `projectManagerId`, `projectManagerFull`, `supportingManagerId`, `supportingManagerFull`, `location`, `orderValue`, `insideSalesId`, `insideSalesFull`, `responsibleSalesId`, `responsibleSalesFull`, `status`, `isCritical`, `clientOrderNo`, `folder_link`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s', '%s')",
                                        mysqli_real_escape_string($oms, $offer_no),
                                        mysqli_real_escape_string($oms, $offer['client'] == 0 ? $offer['endClient'] : $offer['client']),
                                        mysqli_real_escape_string($oms, $offer['client'] == 0 ? getClientName($offer['endClient']) : getClientName($offer['client'])),
                                        mysqli_real_escape_string($oms, $offer['company']),
                                        mysqli_real_escape_string($oms, $offer['deliveryDate']),
                                        mysqli_real_escape_string($oms, $_POST['responsiblePC']),
                                        mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsiblePC'])),
                                        mysqli_real_escape_string($oms, $_POST['supportingManagerId']),
                                        mysqli_real_escape_string($oms, getNameAndSurname($_POST['supportingManagerId'])),
                                        mysqli_real_escape_string($oms, $offer['client'] == 0 ? getClientLocation($offer['endClient']) : getClientLocation($offer['client'])),
                                        mysqli_real_escape_string($oms, $offer['orderValue']),
                                        mysqli_real_escape_string($oms, $_POST['insideSalesId']),
                                        mysqli_real_escape_string($oms, getNameAndSurname($_POST['insideSalesId'])),
                                        mysqli_real_escape_string($oms, $_POST['responsibleSC']),
                                        mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsibleSC'])),
                                        mysqli_real_escape_string($oms, "In progress"),
                                        mysqli_real_escape_string($oms, "0"),
                                        mysqli_real_escape_string($oms, $offer['clientOrderNo']),
                                        mysqli_real_escape_string($oms, $project_folder_dirname)
                                    );
                                    $oms->query($query);

                              
   
                                }

                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `salesarticles`(`offerNo`, `name`, `SON`, `PON`, `diameter`, `PC`, `linerMaterial`, `staticMaterial`, `weight`, `pressure`, `hours`, `projectManagerId`, `projectManagerFull`, `supportingManagerId`, `supportingManagerFull`, `orderValue`, `status`, `isCritical`, `detailDrawing`, `approvalOfEngineering`, `fabrication`, `transport`, `serviceInstalation`, `orderDate`, `issueDate`, `costGroup`, `segment`, `intercompanyOrderNo`, `cmSales`, `cmProd`, prodDate, nobo, noboClient, ped) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($oms, $offer_no),
                                    mysqli_real_escape_string($oms, $_POST['scope']),
                                    mysqli_real_escape_string($oms, trim($_POST['salesOrderNumber'])),
                                    mysqli_real_escape_string($oms, trim($_POST['productionOrderNumber'])),
                                    mysqli_real_escape_string($oms, $_POST['diameter']),
                                    mysqli_real_escape_string($oms, $_POST['productionCompany']),
                                    mysqli_real_escape_string($oms, $_POST['linerMaterial']),
                                    mysqli_real_escape_string($oms, $_POST['staticMaterial']),
                                    mysqli_real_escape_string($oms, $article['kg']),
                                    mysqli_real_escape_string($oms, $article['pressure']),
                                    mysqli_real_escape_string($oms, $_POST['productionHours']),
                                    mysqli_real_escape_string($oms, $_POST['responsiblePC']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['responsiblePC'])),
                                    mysqli_real_escape_string($oms, $_POST['supportingManagerId']),
                                    mysqli_real_escape_string($oms, getNameAndSurname($_POST['supportingManagerId'])),
                                    mysqli_real_escape_string($oms, $article['orderValue']),
                                    mysqli_real_escape_string($oms, "In progress"),
                                    mysqli_real_escape_string($oms, 0),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, 1),
                                    mysqli_real_escape_string($oms, $offer['order']),
                                    mysqli_real_escape_string($oms, $offer['inquiry']),
                                    mysqli_real_escape_string($oms, kosztaOrders($_POST['productionOrderNumber'])),
                                    mysqli_real_escape_string($oms, $article['segmentOMS']),
                                    mysqli_real_escape_string($oms, $_POST['intercompanyOrderNumber']),
                                    mysqli_real_escape_string($oms, $article['ORVCM']),
                                    mysqli_real_escape_string($oms, $article['prodValCM']),
                                    mysqli_real_escape_string($oms, $article['prodDate']),
                                    mysqli_real_escape_string($oms, $article['nobo']),
                                    mysqli_real_escape_string($oms, $article['noboClient']),
                                    mysqli_real_escape_string($oms, $article['ped'])
                                ));
                                
                                $articleId = $oms->insert_id;
                                
                                if(empty($articleId)) {

                                    if($projects_insert) {
                                        $oms->query("DELETE FROM `projects` WHERE `offerNo` = '".$offer_no."'");
                                    }
                                                                    
                                    $link->query(sprintf(
                                        "UPDATE components SET fabricationE='0000-00-00', confirmTimestamp='0000-00-00 00:00:00', confirmPerson='' WHERE id='%s'",
                                        mysqli_real_escape_string($link, $article['id'])
                                    ));

                                    $link->close();
                                    $oms->close();
                                    $milestones->close();

                                    $offer_id = getOfferId($offer_no);

                                    header("Location: order-confirmation.php?export_error=3&offer_id=" . urlencode($offer_id));
                                    exit();

                                }

                                $result = file_get_contents("http://api.nbp.pl/api/exchangerates/rates/A/EUR?format=json");
                                if ($result) {
                                    $nbpData = json_decode($result, true);
                                    if (json_last_error() == JSON_ERROR_NONE && isset($nbpData['rates'][0]['mid'])) {
                                        $eurorate = (float) (round($nbpData['rates'][0]['mid'] * 100) / 100);
                                        if ($eurorate) {
                                            $oms->query(sprintf(
                                                "UPDATE `salesarticles` SET exchangeRate='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($oms, $eurorate),
                                                mysqli_real_escape_string($oms, $articleId)
                                            ));
                                        }
                                    }
                                }

                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 2),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['ddS']),
                                    mysqli_real_escape_string($milestones, $article['ddE']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['ddE']) - strtotime($article['ddS'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['ddE']) - strtotime($article['ddS'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 3),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['aoeS']),
                                    mysqli_real_escape_string($milestones, $article['aoeE']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['aoeE']) - strtotime($article['aoeS'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['aoeE']) - strtotime($article['aoeS'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));

                                // work preparation - added 
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 5),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['wp_milestone_start']),
                                    mysqli_real_escape_string($milestones, $article['wp_milestone_end']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['wp_milestone_end']) - strtotime($article['wp_milestone_start'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['wp_milestone_end']) - strtotime($article['wp_milestone_start'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));

                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 7),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['fS']),
                                    mysqli_real_escape_string($milestones, $article['fE']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['fE']) - strtotime($article['fS'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['fE']) - strtotime($article['fS'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));

                                // QC check / inspection - added
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 8),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['qc_milestone_start']),
                                    mysqli_real_escape_string($milestones, $article['qc_milestone_end']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['qc_milestone_end']) - strtotime($article['qc_milestone_start'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['qc_milestone_end']) - strtotime($article['qc_milestone_start'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));

                                if ($article['fS'] != '0000-00-00' && $article['fE'] != '0000-00-00') {
                                    $oms->query(sprintf(
                                        "UPDATE salesarticles SET frozen='1' WHERE id='%s'",
                                        mysqli_real_escape_string($oms, $articleId)
                                    ));
                                }
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `contract_date`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 9),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['tS']),
                                    mysqli_real_escape_string($milestones, $article['tE']),
                                    mysqli_real_escape_string($milestones, $fabricationEnd),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['tE']) - strtotime($article['tS'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['tE']) - strtotime($article['tS'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));
                                $milestones->query(sprintf(
                                    "INSERT IGNORE INTO `dates`(`u_id`, `m_id`, `a_id`, `start_plan`, `end_plan`, `days`, `weeks`, `status`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s')",
                                    mysqli_real_escape_string($milestones, $_SESSION['plasticonDigitalUser']['id']),
                                    mysqli_real_escape_string($milestones, 10),
                                    mysqli_real_escape_string($milestones, $articleId),
                                    mysqli_real_escape_string($milestones, $article['siS']),
                                    mysqli_real_escape_string($milestones, $article['siE']),
                                    mysqli_real_escape_string($milestones, round((strtotime($article['siE']) - strtotime($article['siS'])) / (3600 * 24)) + 1),
                                    mysqli_real_escape_string($milestones, ceil((round((strtotime($article['siE']) - strtotime($article['siS'])) / (3600 * 24)) + 1) / 7)),
                                    mysqli_real_escape_string($milestones, "Not started")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Components")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Bottom / Top")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Shell")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Assembly")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Steel parts")
                                ));
                                $oms->query(sprintf(
                                    "INSERT IGNORE INTO `productionstages`(`articleId`, `stage`) VALUES ('%s','%s')",
                                    mysqli_real_escape_string($oms, $articleId),
                                    mysqli_real_escape_string($oms, "Water filling")
                                ));
                                  
                                $subcomponents = $link->query(sprintf(
                                    "SELECT * FROM subcomponents WHERE componentId='%s'",
                                    mysqli_real_escape_string($link, $article['id'])
                                ));

                                while ($cmpRow = $subcomponents->fetch_object()) {
                                    $oms->query(sprintf(
                                        "INSERT IGNORE INTO `subcomponents`(`componentId`, `offerNo`, `scope`, `productionLocation`, `stat`, `prot`, `medium`, `diameter`, `pressure`, `WHP`, `weight`, `volume`, `offerValue`, `offerValueCM`) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                        mysqli_real_escape_string($oms, $articleId),
                                        mysqli_real_escape_string($oms, $cmpRow->offerNo),
                                        mysqli_real_escape_string($oms, $cmpRow->scope),
                                        mysqli_real_escape_string($oms, $cmpRow->productionLocation),
                                        mysqli_real_escape_string($oms, $cmpRow->stat),
                                        mysqli_real_escape_string($oms, $cmpRow->prot),
                                        mysqli_real_escape_string($oms, $cmpRow->medium),
                                        mysqli_real_escape_string($oms, $cmpRow->diameter),
                                        mysqli_real_escape_string($oms, $cmpRow->pressure),
                                        mysqli_real_escape_string($oms, $cmpRow->WHP),
                                        mysqli_real_escape_string($oms, $cmpRow->weight),
                                        mysqli_real_escape_string($oms, $cmpRow->volume),
                                        mysqli_real_escape_string($oms, $cmpRow->offerValue),
                                        mysqli_real_escape_string($oms, $cmpRow->offerValueCM)
                                    ));
                                }


                                $error = false;
           
                                if (!$project_folder_dirname) {
                                    $error = true;
                                }
                    
                                $disp_file_ok = false;
                                $order_file_ok = false;
                                $gce_file_ok = true; 

                                $article_pc = $_POST['productionCompany'];

                                $disposal = explode(".", $_FILES['disposalFile']['name']);
                                $extD = end($disposal);

                                $disposal_file_name =  $article_pc . "_" .  $_POST['productionOrderNumber'] . "_" . date("Y-m-d His") . "_disposal." . $extD;

                                if (!$error) {
                                    $disp_file_ok = uploadDisposalFile($project_folder_dirname, $_FILES['disposalFile']['tmp_name'], $disposal_file_name);
                                    if(!$disp_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $order_file_ok = uploadOrderFile($project_folder_dirname, $_FILES['orderFile']['tmp_name'], $_FILES['orderFile']['name'], $_POST['productionOrderNumber'], $article_pc);
                                    if(!$order_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $gce_file_ok = uploadGceFile($project_folder_dirname, $_FILES['gceFile']['tmp_name'], $_FILES['gceFile']['name'], $_POST['productionOrderNumber'], $article_pc);
                                    if(!$gce_file_ok) {
                                        $error = true;
                                    }
                                }
                                
                                if (!$error) {
                                    $lop_list_ok = copyLopList($project_folder_dirname);
                                    if(!$lop_list_ok) {
                                        $error = true;
                                    }
                                }

                                if(!$error) {
                                    $oms->query(sprintf("UPDATE salesarticles SET folder_link='%s' WHERE id='%s'",
                                        mysqli_real_escape_string($oms, $project_folder_dirname),
                                        mysqli_real_escape_string($oms, $articleId)
                                    ));
                                }

                                if($error) {
                        
                                    if($projects_insert) {
                                        removeFoldersOmsExport($created_project_folders);
                                        $oms->query("DELETE FROM `projects` WHERE `offerNo` = (SELECT `offerNo` FROM `salesarticles` WHERE `id` = '" . $articleId . "')");
                                    }

                                    $oms->query("DELETE FROM `salesarticles` WHERE `id` = '" . $articleId . "'");
                                    $milestones->query("DELETE FROM `dates` WHERE `a_id` = '" . $articleId . "'");
                                    $oms->query("DELETE FROM `productionstages` WHERE `articleId` = '" . $articleId . "'");
                                    $oms->query("DELETE FROM `subcomponents` WHERE `componentId` = '" . $articleId . "'");
                                    
                                    $link->query(sprintf(
                                        "UPDATE components SET fabricationE='0000-00-00', confirmTimestamp='0000-00-00 00:00:00', confirmPerson='' WHERE id='%s'",
                                        mysqli_real_escape_string($link, $article['id'])
                                    ));

                                    $link->close();
                                    $oms->close();
                                    $milestones->close();

                                    $offer_id = getOfferId($offer_no);

                                    header("Location: order-confirmation.php?export_error=4&offer_id=" . urlencode($offer_id));
                                    exit();

                                }

                                $sumArt = $oms->query("SELECT COUNT(*) as ile FROM components WHERE salesArticleId='$articleId'");
                                $sumIle = $sumArt->fetch_object();
                                $aoc = $sumIle->ile;
                                $oms->query("UPDATE salesarticles SET AoC='$aoc' WHERE id='$articleId'");
                                $link->close();
                                $oms->close();
                                require("assets/libs/PHPMailer/mailAtt.php");
                                $cl = $offer['client'] == 0 ? getClientName($offer['endClient']) : getClientName($offer['client']);
                                $tittle = "New order transfered to OMS: " . $_POST['salesOrderNumber'] . "-" . $_POST['productionOrderNumber'];
                                $tresc = "Order " . $_POST['salesOrderNumber'] . "-" . $_POST['productionOrderNumber'] . " has been transfered to OMS by " . $_SESSION['plasticonDigitalUser']['imie'] . " " . $_SESSION['plasticonDigitalUser']['nazwisko'] . "<br><br>
Sales article: " . $_POST['scope'] . " <br>
Offer: " . $offer['offerNo'] . " <br>

Production hours: " . $_POST['productionHours'] . " <br>
Weight: " . $article['kg'] . " <br>
Static material: " . $_POST['staticMaterial'] . "<br>
Liner material: " . $_POST['linerMaterial'] . "<br>
Sales company: " . $offer['company'] . "<br>
Production company: " . $_POST['productionCompany'] . "<br><br>

Client: " . $cl . "<br>
Delivery date: " . $offer['deliveryDate'] . "<br><br>

Responsible: " . getNameAndSurname($_POST['responsiblePC']);
                                $mails = ['<EMAIL>'];
                                if (!in_array($_SESSION['plasticonDigitalUser']['email'], $mails))
                                    array_push($mails, $_SESSION['plasticonDigitalUser']['email']);
                                echo "<div class='hidden'>";

                                if ($_POST['productionCompany'] == "PP") {
                                    

                                    wyslijMaila("<EMAIL>", $tresc, $tittle, '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                    
                                    wyslijMaila($_SESSION['plasticonDigitalUser']['email'], $tresc, $tittle . " - confirmation copy", '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);

                                    $send_emails = [];
                                    $send_emails[] = $_SESSION['plasticonDigitalUser']['email'];

                                    $resposibleEmail = getUserEmail($_POST['responsiblePC']);
                                    if (!in_array($resposibleEmail, $send_emails)) {
                                        $send_emails[] = $resposibleEmail;
                                        wyslijMaila($resposibleEmail, $tresc, $tittle . " - Project manager copy", '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                    }

                                    $supportingManagerEmail = getUserEmail($_POST['supportingManagerId']);
                                    if (!in_array($supportingManagerEmail, $send_emails)) {
                                        $send_emails[] = $supportingManagerEmail;
                                        wyslijMaila($supportingManagerEmail, $tresc, $tittle . " - Supporting manager copy", '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                    }

                                    $resposibleSalesEmail = getUserEmail($_POST['responsibleSC']);
                                    if (!in_array($resposibleSalesEmail, $send_emails)) {
                                        $send_emails[] = $resposibleSalesEmail;
                                        wyslijMaila($resposibleSalesEmail, $tresc, $tittle . " - Responsible sales copy", '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                    }

                                    $insideSalesEmail = getUserEmail($_POST['insideSalesId']);
                                    if (!in_array($insideSalesEmail, $send_emails)) {
                                        $send_emails[] = $insideSalesEmail;
                                        wyslijMaila($insideSalesEmail, $tresc, $tittle . " - Inside sales copy", '../../orders/' . $project_folder_dirname . '/' . $disposal_file_name);
                                    }
                                }
                                echo "</div>";
                                if ($article['orderValue'] < 10)
                                    $gw = "*";
                                if ($article['orderValue'] >= 10 && $article['orderValue'] < 100)
                                    $gw = "**";
                                if ($article['orderValue'] >= 100 && $article['orderValue'] < 1000)
                                    $gw = "***";
                                if ($article['orderValue'] >= 1000)
                                    $gw = "****";
                                $companyTitle = "";
                                switch ($offer['company']) {
                                    case 'PTN':
                                        $companyTitle = "PN";
                                        break;
                                    case 'PSS':
                                        $companyTitle = "PS";
                                        break;
                                    case 'TNV':
                                        $companyTitle = "TP";
                                        break;
                                    case 'TBV':
                                        $companyTitle = "TP";
                                        break;
                                    default:
                                        $companyTitle = $offer['company'];
                                        break;
                                }
                                $tytul = $companyTitle . " (" . inicialy($offer['V']) . ") " . $gw . " " . round($article['orderValue']) . "," . $article['ORVCM'] . " " . $offer['offerNo'] . " " . $cl . " | " . $_POST['scope'];
                                $tresc = "
	Production hours: " . $_POST['productionHours'] . "<br>
	Delivery date: " . $offer['deliveryDate'] . "<br><br>

	This message was automatically generated, please do not reply.
							";
                                if ($offer['company'] == "PG" && $_POST['productionCompany'] == "PG")
                                    $mails1 = ['<EMAIL>', '<EMAIL>'];
                                else
                                    $mails1 = ['<EMAIL>', '<EMAIL>', $_SESSION['plasticonDigitalUser']['email']];
                                if (!in_array($_SESSION['plasticonDigitalUser']['email'], $mails1))
                                    array_push($mails1, $_SESSION['plasticonDigitalUser']['email']);
                                //echo"<div class='hidden'>asa";
                                //for($i=0;$i<sizeof($mails1);$i++)
                                //wyslijMaila($mails1[$i], $tresc, $tytul);
                                //echo"</div>";
                                header("Location: order-confirmation.php?exported=1");
                            }
                            /* if($_POST['salesCompany']=="PG"&&$_POST['productionCompany']=="PG")
                              {
                              echo"<div class='hidden'>asa";
                              $mailsPG=['<EMAIL>', getUserEmail($_POST['responsiblePC'])];
                              for($i=0;$i<sizeof($mailsPG);$i++)
                              wyslijMaila($mails[$i], $tresc, $tittle, '../../orders/'.$dirname.'/disposal'.$_POST['productionOrderNumber'].'.'.$extD);
                              echo"</div>";
                              } */
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php require('footer.php'); ?>
    <script>
        function confirmModal(id) {
            console.log(id);
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "getConfirmationInfo",
                    id: id
                },
                success: function(data) {
                    data = JSON.parse(data);
                    console.log(data);
                    var client = data.offer.client;
                    var clientName = data.offer.clientLongName;
                    if (data.offer.client == 0) {
                        client = data.offer.endClient;
                        clientName = data.offer.endClientName;
                    }
                    var newState = new Option(client + " | " + clientName, client, true, true);
                    $("[name='client']").append(newState).trigger('change');
                    $('[name="salesOrderNumber"]').val(data.article.orderNo);
                    $('[name="productionOrderNumber"]').val(data.article.orderNoProduction);
                    $('[name="insideSalesId"]').val(data.offer.oID).trigger("change");
                    $('[name="scope"]').val(data.article.scope);
                    $('[name="salesCompany"]').val(data.offer.company).trigger('change');
                    $('[name="sc_hidden"]').val(data.offer.company).trigger('change');
                    $('[name="offerNumber"]').val(data.offer.offerNo);
                    $('[name="responsibleSC"]').val(data.offer.V).trigger('change');
                    $('[name="diameter"]').val(data.article.DN);
                    $('[name="productionCompany"]').val(data.article.productionLocation).trigger('change');
                    $('[name="supportingManagerId"]').val(data.article.pm).trigger('change');
                    $('[name="linerMaterial"]').val(data.article.prot).trigger('change');
                    $('[name="staticMaterial"]').val(data.article.stat).trigger('change');
                    $('[name="responsiblePC"]').val(data.offer.pm).trigger('change');
                    $('[name="productionHours"]').val(data.article.WHP);
                    $('[name="fabricationE"]').val(data.article.delivery);
                    $('#confirmId').val(id);

                    $('#confirmModal').modal('show');
                }
            });
        }
        $(document).ready(function() {

            function validateRequiredFields(form) {
                let allValid = true;
                form.find('[required]').each(function() {
                    if ($(this).val() == '' || $(this).val() == null) {
                        allValid = false;
                    } 
                });
   
                return allValid;
            }

            function debounce(func, delay) {
				let timeout;
				return function(...args) {
					clearTimeout(timeout);
					timeout = setTimeout(() => func.apply(this, args), delay);
				};
			}
			
			function handleInput() {

                const modal = $(this).closest('.modal-body');
                let son = $(this).val();
                let salesCompany = "";

                if($(this).attr('name') === 'salesOrderNumber') {
                    salesCompany = $('select[name=salesCompany]').val();
                } else {
                    salesCompany = $('select[name=salesCompanyGlass]').val();
                }

                $.ajax({
                    url: "assets/php/ajaxHandeler.php",
                    method: "get",
                    data: {
                        action: 'isSonInOms',
                        son: son,
                        sales_company: salesCompany,
                    },
                    success: function(response) {
                        if (response.status === "error") {
                            const alertMessage = $('<div class="alert alert-info offer-no-alert" style="border-radius: 0;">' + response.message + '</div>');
                            if (!modal.find('.offer-no-alert').length) {
                                modal.prepend(alertMessage);
                            } else {
                                modal.find('.offer-no-alert').html(response.message);
                            }
                        } else {
                            if (modal.find('.offer-no-alert').length > 0) {
                                modal.find('.offer-no-alert').remove();
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    }
                });
	
			}

			const debouncedHandleInput = debounce(handleInput, 500);

            $(document).on('keyup', 'input[name=salesOrderNumber], input[name=salesOrderNumberGlass]', debouncedHandleInput);



            $(document).on('click', 'input[name=saveConf], input[name=saveGlass]', function(e) {

                const form = $(this).closest('form');
                const modal = $(this).parent().parent();

                let pon = 0;
                let son = 0;
                let productionCompany = "";
                let salesCompany = "";

                if($(this).attr('name') === 'saveGlass') {
                    pon = form.find('input[name=productionOrderNumberGlass]').val();
                    son = form.find('input[name=salesOrderNumberGlass]').val();
                    productionCompany = form.find('select[name=productionCompanyGlass]').val();
                    salesCompany = form.find('select[name=salesCompanyGlass]').val();
                } else {
                    pon = form.find('input[name=productionOrderNumber]').val();
                    son = form.find('input[name=salesOrderNumber]').val();
                    productionCompany = form.find('select[name=productionCompany]').val();
                    salesCompany = form.find('select[name=salesCompany]').val();
                }

                // Do not process if required fields are empty
                if(!validateRequiredFields(form)) {
                    return;
                }

                e.preventDefault();

                $.ajax({
                    url: "assets/php/ajaxHandeler.php",
                    method: "get",
                    data: {
                        action: 'isOfferInOms',
                        offerNo: form.find('input[name=offerNumber]').val(),
                        pon: pon,
                        son: son,
                        production_company: productionCompany,
                        sales_company: salesCompany,
                    },
                    success: function(response) {
                        if (response.status === "ok") {
                            // Submit form if offer does not exist in CRM
                            const hiddenInput = $('<input>').attr({
                                type: 'hidden',
                                name: e.target.name,
                                value: e.target.value
                            });
                            form.append(hiddenInput);

                            // Submit form if offer does not exist in CRM
                            form.submit();
                        } else {
                            const alertMessage = $('<div class="alert alert-danger offer-no-alert" style="border-radius: 0;">' + response.message + '</div>');
                            if (!modal.find('.offer-no-alert').length) {
                                modal.prepend(alertMessage);
                            } else {
                                modal.find('.offer-no-alert').html(response.message);
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    }
                });
            });

            $(document).on('hide.bs.modal', '#confirmModal, #addGlass', function() {
                if ($(this).find('.offer-no-alert').length > 0) {
                    $(this).find('.offer-no-alert').remove();
                }
            });

            $('.select2').select2();
            $(".select2-client").select2({
                allowClear: true,
                placeholder: 'Select',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getResellerNamesOC'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            var reset = 1;
            var sess = JSON.parse(window.localStorage.getItem("DataTables_order-confirmation_/crm/order-confirmation.php"));
            if (sess) {
                if (sess.cmpFilter != "" && sess.cmpFilter != null)
                    $("#cmpFilter").val(sess.cmpFilter).trigger('change');
                else
                    $("#cmpFilter").val('all').trigger('change');
                if (sess.prodFilter != "" && sess.prodFilter != null)
                    $("#prodFilter").val(sess.prodFilter).trigger('change');
                else
                    $("#prodFilter").val('all').trigger('change');
            }
            $('.filterStyle').each(function() {
                var t = $(this).next().children().first().children().first();
                $(t).css('height', '28px').children().first().css({
                    'line-height': '24px',
                    'height': '28px'
                });
                $(t).children().last().css('height', '30px');
                $(t).children().first().children().first().css("margin-top", "2px");
            })
            $("#getFilters").click(function() {
                setTimeout(function() {
                    $('.filterSpan').each(function() {
                        var t = $(this);
                        var labelWidth = $(t).prev().outerWidth();
                        var parentWidth = $(t).parent().outerWidth();
                        $(t).css("width", (parentWidth / 2) - 50);
                    })
                }, 100);
            })
            var table = $('#order-confirmation').DataTable({
                "iDisplayLength": 10,
                stateSave: true,
                "stateDuration": 0,
                "order": [
                    [0, "desc"]
                ],
                "processing": true,
                "serverSide": true,
                fixedHeader: {
                    header: true,
                    footer: false
                },
                'ajax': {
                    'url': 'assets/php/confirmation-processing.php',
                    type: "POST",
                    "data": {
                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1) { ?> "cmpFilter": function(d) {
                                return $('#cmpFilter').val()
                            },
                            "prodFilter": function(d) {
                                return $('#prodFilter').val()
                            },
                        <?php } else { ?> "cmpFilter": <?php echo $_SESSION['company']; ?>
                        <?php } ?>
                    }
                },
                "columnDefs": [{
                    "orderable": false,
                    "targets": 14
                }, ],
                "stateSaveParams": function(settings, data) {
                    data.cmpFilter = $('#cmpFilter').val();
                    data.prodFilter = $('#prodFilter').val();
                },
                dom: "<'row'<'col-sm-3 addDeleteClass'><'col-sm-3'><'col-sm-2'><'col-sm-1'l><'col-sm-3'p>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                'columns': [{
                        data: 'offerNo',
                        name: 'offerNo'
                    },
                    {
                        data: 'orderNo',
                        name: 'orderNo'
                    },
                    {
                        data: 'orderNoProduction',
                        name: 'orderNoProduction'
                    },
                    {
                        data: 'scope',
                        name: 'scope'
                    },
                    {
                        data: 'oneClient',
                        name: 'oneClient'
                    },
                    {
                        data: 'orderValue',
                        name: 'orderValue'
                    },
                    {
                        data: 'company',
                        name: 'company'
                    },
                    {
                        data: 'productionLocation',
                        name: 'productionLocation'
                    },
                    {
                        data: 'InR',
                        name: 'InR'
                    },
                    {
                        data: 'PM',
                        name: 'PM'
                    },
                    {
                        data: 'DN',
                        name: 'DN'
                    },
                    {
                        data: 'prot',
                        name: 'prot'
                    },
                    {
                        data: 'stat',
                        name: 'stat'
                    },
                    {
                        data: 'WHP',
                        name: 'WHP'
                    },
                    {
                        data: 'actions',
                        name: 'actions',
                        className: 'text-center'
                    },
                ],
            });
            var reset = 0;
            setTimeout(function() {
                if (!table.state.loaded())
                    window.location.reload();
            }, 500);
            table.ajax.reload();
            $('.filterStyle, .filterControl, .filterCheckbox').on('change input paste', function() {
                if (reset != 1)
                    table.ajax.reload();
            })
            $("#filterReset").click(function() {
                reset = 1;
                $("#cmpFilter").val("all").trigger('change');
                $("#prodFilter").val("all").trigger('change');
                setTimeout(function() {
                    reset = 0;
                }, 150);
                table.ajax.reload();
            })
            $('input.toggle-vis').each(function() {
                var id = $(this).attr("data-column");
                if (table.state.loaded().columns[id].visible == true)
                    $(this).prop("checked", true);
                saveColumns(table.columns(), table);
            })
            $('.toggle-vis').click(function() {
                var column = table.column($(this).attr('data-column'));
                column.visible(!column.visible());
                saveColumns(table.columns(), table);
            });
            $('#restoreDefault').click(function() {
                table.state.clear();
                window.location.reload();
            })
            $('#checkAll').click(function() {
                if ($(this).html() == '<i style="font-size:20px;margin-right:10px;" class="fas fa-check"></i>Select all') {
                    $(this).html('<i style="font-size:20px;margin-right:10px;" class="fas fa-check"></i>Deselect all');
                    $('.chbox').each(function() {
                        if ($(this).prop('disabled') == false) {
                            $(this).prop('checked', true);
                            var column = table.column($(this).attr('data-column'));
                            column.visible(true);
                        }
                    })
                    saveColumns(table.columns(), table);
                } else {
                    $(this).html('<i style="font-size:20px;margin-right:10px;" class="fas fa-check"></i>Select all');
                    $('.chbox').each(function() {
                        if ($(this).prop('disabled') == false) {
                            $(this).prop('checked', false);
                            var column = table.column($(this).attr('data-column'));
                            column.visible(false);
                        }
                    })
                    saveColumns(table.columns(), table);
                }
            })
            $("#clients_wrapper").css("padding", "0").children().first().css("margin-bottom", "-6px");
            $("#clients_length").remove();
            $("#clients_filter").remove();
            $("#mySearchBox").val(table.state.loaded().search.search);
            $('#mySearchBox').keyup(function() {
                table.search($(this).val()).draw();
            })
            $('.reset-button').click(function() {
                table.search('').draw();
            })
            <?php
            if ($_SESSION['plasticonDigitalUser']['crm']['glassOrders'] == 1) {
            ?>
                $(".addDeleteClass").html('<button class="btn btn-primary text-center" style="height:38px;" title="Glass order" data-toggle="modal" data-target="#addGlass">Wood, glass order and claim </button>');
            <?php } ?>
            $("#clients_wrapper").css("padding", "0").children().first().css("margin", "5px 0px -2px 0px");
            $(".pagePerPage").html('<select id="myPageLen" class="form-control"><option value="10">10</option><option value="50">50</option><option value="100">100</option><option value="200">200</option></select>');
            $("#myPageLen").val(table.state.loaded().length);
            $("#myPageLen").change(function() {
                table.page.len($(this).val()).draw();
            })
        });
    </script>