<?php
// error_reporting(E_ERROR | E_WARNING | E_PARSE | E_NOTICE);

require('header.php');
require('nobo-form-fn.php');
/*
 * DEFINE CONSTS:
 * UP_ORDERS_DIR
 * ORDERS_DIR
 */

define("UP_ORDERS_DIR", "../../orders/");
define("ORDERS_DIR", "orders/");
$id = (filter_input(INPUT_GET, "id") !== null) ? filter_input(INPUT_GET, "id") : '0';
$paramId = $_GET['id'];
$user = getUserInfo($_SESSION['plasticonDigitalUser']['id']);
$article = getSalesArticleInfo($id);
$articleOfferNo = ($article === null) ? -1 : $article['offerNo'];
$articleId = ($article === null) ? -1 : $article['id'];
$project = getProjectInfo(getProjectId($articleOfferNo));
$milestone = getMilestoneInfo($articleId);
$offerNo = Utilities::getOfferNo($article);
$offers = getOfferInfo($offerNo);
$offerId = getCRMofferId($articleOfferNo);
$gceOfferLink = Utilities::getGceOfferLink($offers, $user);
$wpcOfferLink = Utilities::getWpcOfferLink($offers, $user);
$wtcOfferLink = Utilities::getWtcOfferLink($offers, $user);
$crmOfferLink = Utilities::getCrmOfferLink($offers, $user);
$id_to_folder_path = getIdToSADirPath($id);
$dir_separator = "-*";
if ($id_to_folder_path != $articleId) {
    $dir_separator = "*";
}
/*
 * SET:
 * folder
 * folderP
 */

$folder = Utilities\Dir::getDirContent(UP_ORDERS_DIR, strval($id_to_folder_path), $dir_separator, 0, '-1');
$folderP = $env == "LOCAL" ? $folder : Utilities\Dir::getDirContent(ORDERS_DIR, strval($id_to_folder_path), $dir_separator, 0, '-1');

$image = false;
$messageRemoved = (filter_input(INPUT_GET, "removed") !== null) ? "Removed successfully" : "";

if ($_SESSION['plasticonDigitalUser']['oms']['dashboard_tab'] != 1) {
    $id = isset($_GET['id']) ? $_GET['id'] : '';
    $id = urldecode($id);

    header("Location: index.php");

    exit;
}
?>

<body class="widescreen adminbody-void">
    <?php
    require('menu.php');
    $_SESSION['url'] = 'article.php';
    ?>

    <?php if ($articleId < 0) { ?>
        <div class="position-absolute w-100 h-100 d-flex align-items-center justify-content-center">
            <div class="d-flex flex-column align-items-center justify-content-center gap-2">
                <div class="display-3 text-center text-success"><?= $messageRemoved; ?></div>
                <div class="display-4 text-center mx-5">The article you are trying to display does not exist. Click the button below to return to the list of available articles.</div>
                <a href="articles.php" class="btn btn-primary mt-5">Go back</a>
            </div>
        </div>
    <?php } else { ?>
        <div class="content-page">
            <div class="content">
                <div class="container-fluid">
                    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                        <div class="card mb-3">
                            <div class="card-body position-relative">

                                <div id="syncAlert"></div>

                                <?php include 'navigation.php'; ?>

                                <?php
                                if (isset($_GET['auf_error'])) {

                                    $err_val = (int) $_GET['auf_error'];

                                    $err_msg = "";
                                    if ($err_val == 1) {
                                        $err_msg = "Folder doesn't exist for this article. Please create first";
                                    }

                                    if ($err_val == 2) {
                                        $err_msg = "Folder for this PON/SON already exists";
                                    }

                                    if ($err_val == 3) {
                                        $err_msg = "Error while renaming folder";
                                    }

                                    alertDanger($err_msg);
                                }
                                ?>

                                <div class="row">
                                    <div class="col-lg-12 article-page-item article-page-item-active" id='dashboard'>
                                        <div class="row">
                                            <div class="col-lg-3 p-0">
                                                <div class="row m-0 p-0">
                                                    <div class="text-center">
                                                        <div style="text-align: right;">
                                                            <div class="input-group input-group-sm position-absolute" data-toggle="modal" data-target="#dashboardEditPhoto" style="width:90px;top:<?php echo Utilities\Link::getLinkMaringTop() ?>;right:0px; !important;"">
                                                                <button class=" btn btn-light text-dark btn-sm form-control border">Edit</button>
                                                                <div class="input-group-append">
                                                                    <span class="input-group-text" style="width:35px"><i class="fas fa-pen"></i></span>
                                                                </div>
                                                            </div>
                                                            <?php
                                                            echo $gceOfferLink
                                                                . $wpcOfferLink
                                                                . $wtcOfferLink
                                                                . $crmOfferLink;
                                                            ?>
                                                            <div class="input-group input-group-sm position-absolute" style="width:90px;top:<?php echo Utilities\Link::getLinkMaringTop() ?>;right:0px; !important;">
                                                                <a href=" #" role="button" class="btn btn-light text-dark btn-sm form-control border instatank-link"><span class="text-dark">IT</span></a>
                                                                <div class="input-group-append">
                                                                    <span class="input-group-text"><img src="<?= INSTATANK_ICO; ?>" alt="InstaTank" width="18px" height="18px" /></span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php
                                                        $tank_photo_name = "tank-photo";
                                                        if ($id_to_folder_path != $article['id']) {
                                                            $tank_photo_name = "tank-photo_" . $article['id'];
                                                        }

                                                        if (!empty(glob($folderP . "/" . $tank_photo_name . ".*"))) {
                                                            $image = glob($folderP . "/" . $tank_photo_name . ".*")[0];
                                                            if (is_dir($folderP) && $image != "") {
                                                                echo '<img class="min-tank-photo pointer" src="' . $image . '" alt="Tank image" onclick="fullSizePhoto(' . "'show'" . ')"></img>';
                                                            } else {
                                                                echo "";
                                                            }
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-9">
                                                <div class="row">
                                                    <div class="col">
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <?= $project['clientName']; ?>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-12">
                                                                <h4><?= $article['name'] . " | CRM - " . $project['offerNo'] . " | " . $project['SC'] . " - " . $article['SON'] . " | " . $article['PC'] . " - " . $article['PON']; ?></h4>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-lg-3">
                                                        <div class="row">
                                                            <div class="col-12 text-danger text-left">
                                                                <h1><?php
                                                                    if ($project['penalties'] == 1) {
                                                                        echo "&euro;";
                                                                    }
                                                                    if ($article['isCritical'] == 1) {
                                                                        echo "!";
                                                                    }
                                                                    ?></h1>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="w100p">
                                                            <div class="card">
                                                                <div class="card-header">
                                                                    Status
                                                                </div>
                                                                <div id="collapseOne" class="collapse show">
                                                                    <div class="card-body" style="padding:0px 25px 20px 25px">
                                                                        <div style="display: flex;">
                                                                            <div style="flex: 0 0 10%;">
                                                                                <div style="margin-top:30px;">Start</div>
                                                                                <div style="margin-top:35px;">Finish</div>
                                                                            </div>
                                                                            <div style="flex: 0 0 90%;" id="timeline-chart">
                                                                                <div class="status-container-tittle">
                                                                                    <div class="status-container-tittle">

                                                                                    </div>
                                                                                </div>
                                                                                <div class="status-container-tittle">
                                                                                    <div class="status-container-tittle">

                                                                                    </div>
                                                                                </div>
                                                                                <div class="status-container">
                                                                                    <div class="status-progress-container">
                                                                                        <div class="status-progress" id="progress"></div>

                                                                                    </div>
                                                                                </div>
                                                                                <div class="status-container-tittle">
                                                                                    <div class="status-container-tittle">

                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="row">
                                            <div class="col-12">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Details&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#dashboardEditDetails"><i class="fas fa-pen"></i></button><?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP" || $_SESSION['plasticonDigitalUser']['id'] == 56) { ?><a href="egzRaport.php?z=<?= $article["PON"]; ?>"><button type="button" name="downloadEgz" class="btn btn-primary" value="Raport godzinowy">Raport godzinowy</button></a><?php } ?>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-4 col-sm-6">
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Scope:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['name']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Sales order number:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['SON']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Production order number:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['PON']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Sales comp. purch. ord. no:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['intercompanyOrderNo']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Status:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['status']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Critical order:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['isCritical'] == 1 ? "YES" : "NO"; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Penalties:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $project['penalties'] == 1 ? "YES" : "NO"; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Discuss on Monday Meeting:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['lessonsLearned'] == 1 ? "YES - " . $article['discuss_user'] : "NO"; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Fabrication period frozen:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['frozen'] == 1 ? "YES" : "NO"; ?></strong></div>
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-md-4 col-sm-6">
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Diameter:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong <?php if ($article['diameter'] > 3000) echo "class='text-danger'"; ?>><?= $article['diameter']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Liner:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['linerMaterial']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Static material:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['staticMaterial']; ?></strong></div>
                                                                        </div>
                                                                        <!-- <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">NOBO:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= ($article['nobo'] == "NOBO from Client") ? $article['noboClient'] : $article['nobo']; ?></strong></div>
                                                                        </div> -->

                                                                        <!-- <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">PED:</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['ped'] == 1 ? "YES" : "NO"; ?></strong></div>
                                                                        </div> -->
                                                                        <div class="row">
                                                                            <div class="col-xs-4 col-sm-6">Exchange rate (EUR to PLN):</div>
                                                                            <div class="col-xs-8 col-sm-6"><strong><?= $article['exchangeRate']; ?></strong></div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-4 col-sm-12">
                                                                        <div class="row">
                                                                            <table class="table">
                                                                                <thead>
                                                                                    <tr>
                                                                                        <th></th>
                                                                                        <th style="text-align: right;">
                                                                                            Sold
                                                                                        </th>
                                                                                        <th style="text-align: right;" colspan="2">
                                                                                            Technology
                                                                                        </th>
                                                                                        <th style="text-align: right;">
                                                                                            Actual
                                                                                        </th>
                                                                                    </tr>
                                                                                </thead>
                                                                                <tbody>
                                                                                    <tr>
                                                                                        <td>Weight:</td>
                                                                                        <td style="text-align: right;"><strong><?= $article['weight']; ?></strong></td>
                                                                                        <td colspan="2" style="text-align: right;"><strong><?= round($article['materialyWydane']); ?></strong></td>
                                                                                        <td style="text-align: right;"><strong><?= $article['weightActual']; ?></strong></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td rowspan="2">Hours:</td>
                                                                                        <td rowspan="2" style="text-align: right;"><strong><?= $article['hours']; ?></strong></td>
                                                                                        <td style="text-align: right;">G: <strong><?= $article['rbhTechGRP']; ?></strong></td>
                                                                                        <td style="text-align: right;">T: <strong><?= $article['rbhTechTP']; ?></strong></td>
                                                                                        <td rowspan="2" style="text-align: right;"><strong <?php if (getEGZhours($article['PON']) > $article['hours']) echo "class='text-danger'"; ?>><?= getEGZhours($article['PON']); ?></strong></td>
                                                                                    </tr>
                                                                                    <tr>
                                                                                        <td colspan="2" style="text-align: right;">SUM: <strong <?php if ($article['rbhWgTechnologii'] > $article['hours']) echo "class='text-danger'"; ?>><?= round($article['rbhWgTechnologii']); ?></strong></td>
                                                                                    </tr>
                                                                                </tbody>
                                                                            </table>
                                                                        </div>

                                                                    </div>
                                                                </div>

                                                                <hr>

                                                                <div class="row">
                                                                    <div class="col-md-4 col-sm-6">
                                                                        <?php include "nobo-view.php"; ?>
                                                                    </div>
                                                                    <div class="col-md-8 col-sm-12">
                                                                        <?php include "compounds-view.php"; ?>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            <div class="row">
                                                                <div class="col-6">Sales&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#dashboardEditSales"><i class="fas fa-pen"></i></button></div>
                                                                <?php
                                                                $specialName = $_SESSION['plasticonDigitalUser']['imie'];
                                                                $specialSurname = $_SESSION['plasticonDigitalUser']['nazwisko'];
                                                                $specialEmail = $_SESSION['plasticonDigitalUser']['email'];
                                                                $specialTime = date('Y-m-d H:i:s');
                                                                $specialPasswd = "Y3Q88hvWD6kNTyWQZCcy";
                                                                $specialControl = md5($specialName . $specialSurname . $specialEmail . $specialTime . $specialPasswd);
                                                                ?>
                                                                <div class="col-6 text-right"><?php if ($_SESSION['plasticonDigitalUser']['apps']['crm'] == 1) { ?><a target="_blank" href="<?= CRM_OFFER_LINK . $offerId; ?>"><button class="btn btn-light page-menu-btn">Open in CRM&nbsp;<i class="fas fa-folder-open"></i></button></a><?php } ?></div>
                                                            </div>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-lg-12">
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Sales offer number:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['offerNo']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Sales company:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['SC']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Production company:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['PC']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Order date:</div>
                                                                            <div class="col-lg-6"><strong><?= dateFormat($article['orderDate'] == '0000-00-00' ? '' : $article['orderDate']); ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Issue date:</div>
                                                                            <div class="col-lg-6"><strong><?= dateFormat($article['issueDate'] == '0000-00-00' ? '' : $article['issueDate']); ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Order value:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['orderValue']; ?> k&euro;</strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Sales CM:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['cmSales']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Production CM:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['cmProd']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Segment:</div>
                                                                            <div class="col-lg-6"><strong><?= html_entity_decode($article['segment']); ?></strong></div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Customer&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#dashboardEditCustomer"><i class="fas fa-pen"></i></button>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="col-lg-12">
                                                                    <div class="row">
                                                                        <div class="col-lg-4">Client:</div>
                                                                        <div class="col-lg-6"><strong><?= $project['clientName']; ?></strong></div>
                                                                    </div>
                                                                    <!--<div class="row">
                                                                        <div class="col-lg-4">Final client:</div>
                                                                        <div class="col-lg-6"><strong>TBD</strong></div>
                                                                </div>-->
                                                                    <div class="row">
                                                                        <div class="col-lg-4">Location:</div>
                                                                        <div class="col-lg-6"><strong><?= $project['location']; ?></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">Client order number</div>
                                                                        <div class="col-lg-6"><strong><?= $project['clientOrderNo']; ?></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                        <div class="col-lg-4">&nbsp;</div>
                                                                        <div class="col-lg-6"><strong></strong></div>
                                                                    </div>
                                                                    <!--<div class="row">
                                                                        <div class="col-lg-4">Project order number:</div>
                                                                        <div class="col-lg-6"><strong>TBD</strong></div>
                                                                </div>
                                                                <div class="row">
                                                                        <div class="col-lg-4">Article number:</div>
                                                                        <div class="col-lg-6"><strong>TBD</strong></div>
                                                                </div>-->
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Transport&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#dashboardEditTransport"><i class="fas fa-pen"></i></button>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-lg-12">
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Delivery term:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['deliveryTerms']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Delivery date:</div>
                                                                            <div class="col-lg-6"><strong><?= dateFormat($milestone['t']->contract_date == '0000-00-00' ? '' : $milestone['t']->contract_date); ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">Delivery address:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['deliveryAddress']; ?>
                                                                                </strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-4">&nbsp;</div>
                                                                            <div class="col-lg-6"><strong></strong></div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Contact&nbsp;<button class="btn btn-light page-menu-btn" data-toggle="modal" data-target="#dashboardEditContact"><i class="fas fa-pen"></i></button>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-lg-6" style="border-right:2px solid rgba(111,111,111,.13);">
                                                                        <div class="row">
                                                                            <div class="col-lg-6">Inside sales:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['insideSalesFull']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-6">Responsible sales:</div>
                                                                            <div class="col-lg-6"><strong><?= $project['responsibleSalesFull']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-6">Project manager:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['projectManagerFull']; ?></strong></div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-lg-6">Supporting manager:</div>
                                                                            <div class="col-lg-6"><strong><?= $article['supportingManagerFull']; ?></strong></div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-lg-6">
                                                                        <!--<div class="row">
                                                                            <div class="col-lg-6">Final client commercial:</div>
                                                                            <div class="col-lg-6"><strong>TBD</strong></div>
                                                                    </div>
                                                                    <div class="row">
                                                                            <div class="col-lg-6">Final client technical:</div>
                                                                            <div class="col-lg-6"><strong>TBD</strong></div>
                                                                    </div>-->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <br>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="w100p">
                                                    <div class="card">
                                                        <div class="card-header">
                                                            Components&nbsp;<button class="btn btn-light page-menu-btn" id="addComponent"><i class="fas fa-plus"></i></button>
                                                        </div>
                                                        <div id="collapseOne" class="collapse show">
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-lg-12">
                                                                        <div class="table-responsive">
                                                                            <table id="componentsTable" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
                                                                                <thead>
                                                                                    <tr>
                                                                                        <th>Id</th>
                                                                                        <th>Component</th>
                                                                                        <th>Prod. loc.</th>
                                                                                        <th>Static</th>
                                                                                        <th>Protection</th>
                                                                                        <th>Diameter</th>
                                                                                        <th>Weight</th>
                                                                                        <th class="tableM">Edit</th>
                                                                                    </tr>
                                                                                </thead>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditSales">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Sales</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST" enctype="multipart/form-data">
                                                        <div class="modal-body">
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Sales offer number:<br>
                                                                    <input type="text" placeholder="Offer number" class="form-control" name="proOfferNo" value="<?= $project['offerNo']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Sales company:<br>
                                                                    <select name="proSalesCompany" class="select2 form-control" required>
                                                                        <option selected disabled>Select</option>
                                                                        <?= getCompanies(); ?>
                                                                    </select>
                                                                </label>
                                                                <label class="w100">Production company:<br>
                                                                    <select name="proProductionCompany" class="select2 form-control" required>
                                                                        <option selected disabled>Select</option>
                                                                        <?= getCompanies(); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Order date:<br>
                                                                    <input type="date" class="form-control" name="proOrderDate" value="<?= $article['orderDate']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Issue date:<br>
                                                                    <input type="date" class="form-control" name="proIssueDate" value="<?= $article['issueDate']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Order Value:<br>
                                                                    <input type="number" min="0" step="0.01" class="form-control" name="proSalesORV" value="<?= $article['orderValue']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Sales CM:<br>
                                                                    <input type="number" min="0" max="100" class="form-control" name="proSalesCM" value="<?= $article['cmSales']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Production CM:<br>
                                                                    <input type="number" min="0" max="100" class="form-control" name="proProdCM" value="<?= $article['cmProd']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Segment:<br>
                                                                    <select id="segment" name="proSegment" class="form-control select2" required>
                                                                        <!-- <option value="" selected disabled>Select segment</option>
                                                                    <option value="V-Vessles/Apparatus (&lt;&#8960;4000mm)">V-Vessles/Apparatus (&lt;&#8960;4000mm)</option>
                                                                    <option value="V-Vessles/Apparatus (&gt;&#8960;4000mm)">V-Vessles/Apparatus (&gt;&#8960;4000mm)</option>
                                                                    <option value="V-Vessles/Apparatus (Pure TP)">V-Vessles/Apparatus (Pure TP)</option>
                                                                    <option value="V-Vessles/Apparatus (FF)">V-Vessles/Apparatus (FF)</option>
                                                                    <option value="P-Process Piping (&#8960;25-600mm)">P-Process Piping (&#8960;25-600mm)</option>
                                                                    <option value="P-Pipelines (&#8960;600mm-1500mm)">P-Pipelines (&#8960;600mm-1500mm)</option>
                                                                    <option value="L-Loose lining">L-Loose lining</option>
                                                                    <option value="L-Sheet lining">L-Sheet lining</option>
                                                                    <option value="L-Fixpoint lining">L-Fixpoint lining</option>
                                                                    <option value="Sp-Wet ESP">Sp-Wet ESP</option>
                                                                    <option value="Sp-Ducts">Sp-Ducts</option>
                                                                    <option value="Sp-Chimney & Stacks">Sp-Chimney & Stacks</option>
                                                                    <option value="Sp-Other">Sp-Other</option>
                                                                    <option value="Si-Installation">Si-Installation</option>
                                                                    <option value="Si-Daily service (Inspection, repairs)">Si-Daily service (Inspection, repairs)</option>
                                                                    <option value="Si-Revamping">Si-Revamping</option>
                                                                    <option value="Si-Shutdowns & Turnarounds">Si-Shutdowns & Turnarounds</option>
                                                                    <option value="OM-Spare parts">OM-Spare parts</option>
                                                                    <option value="OM-Trade components">OM-Trade components</option> -->
                                                                        <!-- te na górze były stare -->
                                                                        <!-- <option value="V-Vessles/Apparatus (&lt;&#8960;4000mm)">V-Vessles/Apparatus (<&#8960;4000mm)< /option>
                                                                        <option value="V-Vessles/Apparatus (&gt;&#8960;4000mm GRP)">V-Vessles/Apparatus (>&#8960;4000mm GRP)</option>
                                                                        <option value="V-Vessles/Apparatus (&lt;&#8960;4000mm Dual)">V-Vessles/Apparatus (>&#8960;4000mm Dual)</option>
                                                                        <option value="V-Vessles/Apparatus (Pure TP)">V-Vessles/Apparatus (Pure TP)</option>
                                                                        <option value="V-Vessles/Apparatus (FF)">V-Vessles/Apparatus (FF)</option>
                                                                        <option value="P-Process Piping (&#8960;25-600mm)">P-Process Piping (&#8960;25-600mm)</option>
                                                                        <option value="P-Pipelines (&#8960;600mm-1500mm)">P-Pipelines (&#8960;600mm-1500mm)</option>
                                                                        <option value="L-Loose lining">L-Loose lining</option>
                                                                        <option value="L-Sheet lining">L-Sheet lining</option>
                                                                        <option value="L-Fixpoint lining">L-Fixpoint lining</option>
                                                                        <option value="Sp-Wet ESP">Sp-Wet ESP</option>
                                                                        <option value="Sp-Ducts">Sp-Ducts</option>
                                                                        <option value="Sp-Chimney & Stacks">Sp-Chimney & Stacks</option>
                                                                        <option value="Sp-Other">Sp-Other</option>
                                                                        <option value="Si-Installation">Si-Installation</option>
                                                                        <option value="Si-Daily service (Inspection, repairs)">Si-Daily service (Inspection, repairs)</option>
                                                                        <option value="Si-Framework Contracts">Si-Framework Contracts</option>
                                                                        <option value="Si-Revamping">Si-Revamping</option>
                                                                        <option value="Si-Shutdowns & Turnarounds">Si-Shutdowns & Turnarounds</option>
                                                                        <option value="OM-Spare parts">OM-Spare parts</option>
                                                                        <option value="OM-External Trade components">OM-External Trade components</option>
                                                                        <option value="OM-Change Order">OM-Change Order</option> -->
                                                                        <!-- te na górze byl jeszcze starsze ;) -->
                                                                        <option value="Tanks/Apparatus ≤4000mm">Tanks/Apparatus ≤4000mm</option>
                                                                        <option value="Storage tank GRP ≤4000mm">Storage tank GRP ≤4000mm</option>
                                                                        <option value="Storage tank GRP 3D ≤4000mm">Storage tank GRP 3D ≤4000mm</option>
                                                                        <option value="Storage tank Dual ≤4000mm">Storage tank Dual ≤4000mm</option>
                                                                        <option value="Pressure vessel ≤4000mm">Pressure vessel ≤4000mm</option>
                                                                        <option value="Scrubber GRP ≤4000mm">Scrubber GRP ≤4000mm</option>
                                                                        <option value="Scrubber Dual ≤4000mm">Scrubber Dual ≤4000mm</option>
                                                                        <option value="Rectangular vessels">Rectangular vessels</option>
                                                                        <option value="Tanks/Apparatus >4000mm GRP">Tanks/Apparatus >4000mm GRP</option>
                                                                        <option value="Tanks/Apparatus >4000mm GRP  3D">Tanks/Apparatus >4000mm GRP 3D</option>
                                                                        <option value="Tanks/Apparatus >4000mm Dual">Tanks/Apparatus >4000mm Dual</option>
                                                                        <option value="Tanks/Apparatus >4000mm hand lay-up and assembly on site GRP">Tanks/Apparatus >4000mm hand lay-up and assembly on site GRP</option>
                                                                        <option value="Tanks/Apparatus >4000mm hand lay-up and assembly on site Dual">Tanks/Apparatus >4000mm hand lay-up and assembly on site Dual</option>
                                                                        <option value="Fully fluorinated liner/GRP">Fully fluorinated liner/GRP</option>
                                                                        <option value="Pure TP">Pure TP</option>
                                                                        <option value="Loose lining">Loose lining</option>
                                                                        <option value="Sheet lining">Sheet lining</option>
                                                                        <option value="Fixpoint lining">Fixpoint lining</option>
                                                                        <option value="Wet ESP">Wet ESP</option>
                                                                        <option value="Ducts round">Ducts round</option>
                                                                        <option value="Ducts rectangular">Ducts rectangular</option>
                                                                        <option value="Chimney & Stacks ≤4000mm">Chimney & Stacks ≤4000mm</option>
                                                                        <option value="Chimney & Stacks >4000mm">Chimney & Stacks >4000mm</option>
                                                                        <option value="Other">Other</option>
                                                                        <option value="Process Piping 25-600mm GRP">Process Piping 25-600mm GRP</option>
                                                                        <option value="Process Piping 25-600mm Dual">Process Piping 25-600mm Dual</option>
                                                                        <option value="Pipelines 601mm-1500mm GRP">Pipelines 601mm-1500mm GRP</option>
                                                                        <option value="Pipelines 601mm-1500mm Dual">Pipelines 601mm-1500mm Dual</option>
                                                                        <option value="Installation">Installation</option>
                                                                        <option value="Daily service">Daily service</option>
                                                                        <option value="Revamping">Revamping</option>
                                                                        <option value="Shutdowns & Turnarounds">Shutdowns & Turnarounds</option>
                                                                        <option value="Framework Contracts">Framework Contracts</option>
                                                                        <option value="Spare Parts">Spare Parts</option>
                                                                        <option value="External Trade Components">External Trade Components</option>
                                                                        <option value="Change Order">Change Order</option>

                                                                    </select>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditSales" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditDetails">
                                            <div class="modal-dialog" style="min-width:70%">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Details</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100">Name:<br>
                                                                        <input type="text" placeholder="Name" class="form-control" name="artScope" value="<?= $article['name']; ?>" required>
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">Diameter:<br>
                                                                        <input type="number" min="0" placeholder="Diameter" class="form-control" name="artDiameter" value="<?= $article['diameter']; ?>" required>
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">Weight:<br>
                                                                        <input type="number" min="0" placeholder="Weight" class="form-control" name="artWeight" value="<?= $article['weight']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100" title="Production order number">SON:<br>
                                                                        <input type="text" placeholder="SON" class="form-control" name="artSON" value="<?= $article['SON']; ?>" required>
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">Liner material:<br>
                                                                        <select name="artLinerMaterial" class="form-control select2 w-100">
                                                                            <option value="">Select</option>
                                                                            <option value="CBL+BPO">CBL+BPO</option>
                                                                            <option value="CBL">CBL</option>
                                                                            <option value="CBL+graphite">CBL+graphite</option>
                                                                            <option value="CBL+Nexus">CBL+Nexus</option>
                                                                            <option value="CBL+SiC">CBL+SiC</option>
                                                                            <option value="CBL2,5mm">CBL2,5mm</option>
                                                                            <option value="CBL3,3mm">CBL3,3mm</option>
                                                                            <option value="CBL5mm">CBL5mm</option>
                                                                            <option value="CBL6,3mm">CBL6,3mm</option>
                                                                            <option value="F(PVDF)">F(PVDF)</option>
                                                                            <option value="F(PVDF-el)">F(PVDF-el)</option>
                                                                            <option value="F(ECTFE)">F(ECTFE)</option>
                                                                            <option value="F(FEP)">F(FEP)</option>
                                                                            <option value="F(PFA)">F(PFA)</option>
                                                                            <option value="F(m-PTFE)">F(m-PTFE)</option>
                                                                            <option value="TP(PE100)">TP(PE100)</option>
                                                                            <option value="TP(PP-C-PK)">TP(PP-C-PK)</option>
                                                                            <option value="TP(PP-el)">TP(PP-el)</option>
                                                                            <option value="TP(PPh100)">TP(PPh100)</option>
                                                                            <option value="TP(PPh2222)">TP(PPh2222)</option>
                                                                            <option value="TP(PPR)">TP(PPR)</option>
                                                                            <option value="TP(PVC MZ)">TP(PVC MZ)</option>
                                                                            <option value="TP(PVC CAW)">TP(PVC CAW)</option>
                                                                            <option value="TP(PVC EN)">TP(PVC EN)</option>
                                                                            <option value="TP(PVC-U grey)">TP(PVC-U grey)</option>
                                                                            <option value="TP(PVC-U red)">TP(PVC-U red)</option>
                                                                            <option value="TP(PVC NL)">TP(PVC NL)</option>
                                                                            <option value="TP(PVC Dekadur plus)">TP(PVC Dekadur plus)</option>
                                                                            <option value="TP(C-PVC)">P(C-PVC)</option>
                                                                            <option value="Steel">Steel</option>
                                                                            <option value="Other">Other</option>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">GCE hours:<br>
                                                                        <input type="number" placeholder="Hours" class="form-control" name="artHours" value="<?= $article['hours']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100" title="Production order number">PON:<br>
                                                                        <input type="text" placeholder="PON" class="form-control" name="artPON" value="<?= $article['PON']; ?>">
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">Static material:<br>
                                                                        <select name="artStaticMaterial" class="form-control select2">
                                                                            <option value="">Select</option>
                                                                            <option value="GRP(D411)">GRP(D411)</option>
                                                                            <option value="GRP(D441-400)">GRP(D441-400)</option>
                                                                            <option value="GRP(D451-400)">GRP(D451-400)</option>
                                                                            <option value="GRP(D470-300)">GRP(D470-300)</option>
                                                                            <option value="GRP(D470HT-400)">GRP(D470HT-400)</option>
                                                                            <option value="GRP(D510-A40)">GRP(D510-A40)</option>
                                                                            <option value="GRP(D510-C350)">GRP(D510-C350)</option>
                                                                            <option value="GRP(D510N)">GRP(D510N)</option>
                                                                            <option value="GRP(P104T)">GRP(P104T)</option>
                                                                            <option value="GRP(P122T)">GRP(P122T)</option>
                                                                            <option value="GRP(Syn 0266 N4)">GRP(Syn 0266 N4)</option>
                                                                            <option value="GRP(Viapal 797-59)">GRP(Viapal 797-59)</option>
                                                                            <option value="GRP(Vipel F010)">GRP(Vipel F010)</option>
                                                                            <option value="GRP(A Enova FW1045)">GRP(A Enova FW1045)</option>
                                                                            <option value="GRP(A430)">GRP(A430)</option>
                                                                            <option value="GRP(A580)">GRP(A580)</option>
                                                                            <option value="GRP(A590)">GRP(A590)</option>
                                                                            <option value="GRP(Aropol UN 2)">GRP(Aropol UN 2)</option>
                                                                            <option value="Steel">Steel</option>
                                                                            <option value="Thermoplastic">Thermoplastic</option>
                                                                            <option value="GRP(iso-resin)">GRP(iso-resin)</option>
                                                                            <option value="GRP(ortho-resin)">GRP(ortho-resin)</option>
                                                                            <option value="GRP(vinylester-resin)">GRP(vinylester-resin)</option>
                                                                            <option value="Steel">Steel</option>
                                                                            <option value="Other">Other</option>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col">
                                                                    <label class="w100">Technology hours GRP:<br>
                                                                        <input type="number" placeholder="Hours" class="form-control" step="0.01" name="techHoursGRP" value="<?= $article['rbhTechGRP']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100" title="Production order number">Sales comp. purch. ord. no:<br>
                                                                        <input type="text" placeholder="Sales comp. purch. ord. no:" class="form-control" name="artIntercompanyOrderNo" value="<?= $article['intercompanyOrderNo']; ?>">
                                                                    </label>
                                                                </div>

                                                                <!-- GAP -->
                                                                <div class="col"></div>
                                                                <!-- /gap -->

                                                                <div class="col">
                                                                    <label class="w-100">Technology hours TP:<br>
                                                                        <input type="number" placeholder="Hours" class="form-control" step="0.01" name="techHoursTP" value="<?= $article['rbhTechTP']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100">Status:<br>
                                                                        <?php
                                                                        if ($user['statusChange'] == 1) {
                                                                        ?>
                                                                            <select name="artStatus" class="form-control select2">
                                                                                <option value="In progress">In progress</option>
                                                                                <option value="Delayed">Delayed</option>
                                                                                <option value="Finished in PC">Finished in PC</option>
                                                                                <option value="Finished">Finished</option>
                                                                                <option value="Hold">Hold</option>
                                                                                <option value="To be dispatched">To be dispatched</option>
                                                                                <option value="Canceled">Canceled</option>
                                                                            </select>
                                                                        <?php
                                                                        } else {
                                                                        ?>
                                                                            <input type="hidden" name="artStatus" value="<?= $article['status']; ?>" />
                                                                            <b><?= $article['status']; ?></b>
                                                                        <?php
                                                                        }
                                                                        ?>
                                                                    </label>
                                                                </div>

                                                                <!-- GAP -->
                                                                <div class="col"></div>
                                                                <!-- /gap -->

                                                                <div class="col">
                                                                    <label class="w-100">Actual weight<br>
                                                                        <input type="number" placeholder="Weight" class="form-control" name="actWeightEd" value="<?= $article['weightActual']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100">Critical:<br>
                                                                        <select name="artCritical" class="form-control select2">
                                                                            <option value="0">No</option>
                                                                            <option value="1">Yes</option>
                                                                        </select>
                                                                    </label>
                                                                </div>

                                                                <!-- GAP -->
                                                                <div class="col"></div>
                                                                <!-- /gap -->

                                                                <div class="col">
                                                                    <label class="w-100">Technology weight<br>
                                                                        <input type="number" placeholder="Weight" class="form-control" step="0.01" name="techWeight" value="<?= $article['materialyWydane']; ?>" required>
                                                                    </label>
                                                                </div>
                                                            </div>

                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100">Penalties:<br>
                                                                        <select name="artPenalties" class="form-control select2">
                                                                            <option value="0">No</option>
                                                                            <option value="1">Yes</option>
                                                                        </select>
                                                                    </label>
                                                                </div>

                                                                <!-- GAP -->
                                                                <div class="col"></div>
                                                                <!-- /gap -->

                                                                <div class="col">
                                                                    <label class="w-100">Exchange rate (EUR to PLN)<br>
                                                                        <input type="number" placeholder="Exchange rate" class="form-control" name="exchangeRate" value="<?= $article['exchangeRate']; ?>" step="0.01">
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w-100">Discuss on Monday Meeting:<br>
                                                                        <select name="artLessons" class="form-control select2">
                                                                            <option value="0">No</option>
                                                                            <option value="1">Yes</option>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col">

                                                                </div>
                                                                <div class="col">

                                                                </div>
                                                            </div>
                                                            <div class="row">
                                                                <div class="col">
                                                                    <label class="w100">Fabrication period frozen:<br>
                                                                        <select name="artFrozen" class="form-control select2">
                                                                            <option value="0">No</option>
                                                                            <option value="1">Yes</option>
                                                                        </select>
                                                                    </label>
                                                                </div>
                                                                <div class="col">

                                                                </div>
                                                                <div class="col">

                                                                </div>
                                                            </div>
                                                            <div class="px-2">
                                                                <?php
                                                                echo noboForm($paramId);
                                                                ?>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditDetails" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditCustomer">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Customer</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST" enctype="multipart/form-data">
                                                        <div class="modal-body">
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Client: *<br>
                                                                    <select class="form-control select2-client" name="client" required></select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Client order number:<br>
                                                                    <input type="text" class="form-control" name="clientOrderNo" value="<?= $project['clientOrderNo']; ?>">
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditCustomer" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditTransport">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Transport</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST" enctype="multipart/form-data">
                                                        <div class="modal-body">
                                                            <!--<div class="row justify-content-center">
                                                                <label class="w100">Delivery date:<br>
                                                                        <input type="date" placeholder="Delivery date" class="form-control" name="deliveryDate" value="<?= $project['deliveryDate']; ?>" required>
                                                                </label>
                                                        </div>-->
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Delivery terms:<br>
                                                                    <input type="text" placeholder="Delivery terms" class="form-control" name="deliveryTerms" value="<?= $project['deliveryTerms']; ?>" required>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Delivery address:<br>
                                                                    <input type="text" placeholder="Delivery address" class="form-control" name="deliveryAddress" value="<?= $project['deliveryAddress']; ?>" required>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditTransport" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditContact">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Contact</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST" enctype="multipart/form-data">
                                                        <div class="modal-body">
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Inside sales:<br>
                                                                    <select name="insideSales" class="select2 form-control" required>
                                                                        <option selected disabled>Select</option>
                                                                        <?php selectUsersSales(); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Responsible sales:<br>
                                                                    <select name="responsibleSales" class="select2 form-control" required>
                                                                        <option selected disabled>Select</option>
                                                                        <?php selectUsersSales(); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Project manager:<br>
                                                                    <select name="projectManager" class="select2 form-control" required>
                                                                        <option selected disabled>Select</option>
                                                                        <?php selectUsersPerm('pm'); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <label class="w100">Supporting manager:<br>
                                                                    <select name="supportingManager" class="select2 form-control">
                                                                        <option selected>Select</option>
                                                                        <?php selectUsersPerm('pm'); ?>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditContact" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="editSubcomponent">
                                            <div class="modal-dialog" style="margin-top:10px;">
                                                <div id="subCmpLoader" class="loader-box text-center position-absolute hidden" style="width:100%;height:100%;z-index:2000;background: rgba(255,255,255,0.8);">
                                                    <div class="loader loader-1" style="top:40%;"></div>
                                                </div>
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Edit component <strong id="componentEditScopeLabel"></strong></h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            <h4>General:</h4>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-lg-6">
                                                                <label style="width:200px;">Component:<br>
                                                                    <input type="text" id="subCscope" class="form-control form-100" placeholder="Component">
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-6">
                                                                <label style="width:200px;">Production location:<br>
                                                                    <select class="form-control select2" id="subCproductionLocation">
                                                                        <option value='' selected>Select</option>
                                                                        <?= getCompanies(); ?>
                                                                        <option value='third party'>third party</option>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <br>
                                                        <div class="row">
                                                            <h4>Technical:</h4>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-lg-6">
                                                                <label for="name" style="width:200px;">Static material:<br>
                                                                    <select id="subCstat" class="form-control select2">
                                                                        <option value="">Select</option>
                                                                        <option value="GRP(D411)">GRP(D411)</option>
                                                                        <option value="GRP(D441-400)">GRP(D441-400)</option>
                                                                        <option value="GRP(D451-400)">GRP(D451-400)</option>
                                                                        <option value="GRP(D470-300)">GRP(D470-300)</option>
                                                                        <option value="GRP(D470HT-400)">GRP(D470HT-400)</option>
                                                                        <option value="GRP(D510-A40)">GRP(D510-A40)</option>
                                                                        <option value="GRP(D510-C350)">GRP(D510-C350)</option>
                                                                        <option value="GRP(D510N)">GRP(D510N)</option>
                                                                        <option value="GRP(P104T)">GRP(P104T)</option>
                                                                        <option value="GRP(P122T)">GRP(P122T)</option>
                                                                        <option value="GRP(Syn 0266 N4)">GRP(Syn 0266 N4)</option>
                                                                        <option value="GRP(Viapal 797-59)">GRP(Viapal 797-59)</option>
                                                                        <option value="GRP(Vipel F010)">GRP(Vipel F010)</option>
                                                                        <option value="GRP(A Enova FW1045)">GRP(A Enova FW1045)</option>
                                                                        <option value="GRP(A430)">GRP(A430)</option>
                                                                        <option value="GRP(A580)">GRP(A580)</option>
                                                                        <option value="GRP(A590)">GRP(A590)</option>
                                                                        <option value="GRP(Aropol UN 2)">GRP(Aropol UN 2)</option>
                                                                        <option value="Steel">Steel</option>
                                                                        <option value="Thermoplastic">Thermoplastic</option>
                                                                        <option value="GRP(iso-resin)">GRP(iso-resin)</option>
                                                                        <option value="GRP(ortho-resin)">GRP(ortho-resin)</option>
                                                                        <option value="GRP(vinylester-resin)">GRP(vinylester-resin)</option>
                                                                        <option value="Steel">Steel</option>
                                                                        <option value="Other">Other</option>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-6">
                                                                <label for="name" style="width:200px;">Diameter:<br>
                                                                    <input type="number" step="0.01" min="0" id="subCDN" placeholder="DN" class="form-control form-100">
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class="col-lg-6">
                                                                <label for="name" style="width:200px;">Protection layer:<br>
                                                                    <select id="subCprot" class="form-control select2">
                                                                        <option value="">Select</option>
                                                                        <option value="CBL+BPO">CBL+BPO</option>
                                                                        <option value="CBL">CBL</option>
                                                                        <option value="CBL+graphite">CBL+graphite</option>
                                                                        <option value="CBL+Nexus">CBL+Nexus</option>
                                                                        <option value="CBL+SiC">CBL+SiC</option>
                                                                        <option value="CBL2,5mm">CBL2,5mm</option>
                                                                        <option value="CBL3,3mm">CBL3,3mm</option>
                                                                        <option value="CBL5mm">CBL5mm</option>
                                                                        <option value="CBL6,3mm">CBL6,3mm</option>
                                                                        <option value="F(PVDF)">F(PVDF)</option>
                                                                        <option value="F(PVDF-el)">F(PVDF-el)</option>
                                                                        <option value="F(ECTFE)">F(ECTFE)</option>
                                                                        <option value="F(FEP)">F(FEP)</option>
                                                                        <option value="F(PFA)">F(PFA)</option>
                                                                        <option value="F(m-PTFE)">F(m-PTFE)</option>
                                                                        <option value="TP(PE100)">TP(PE100)</option>
                                                                        <option value="TP(PP-C-PK)">TP(PP-C-PK)</option>
                                                                        <option value="TP(PP-el)">TP(PP-el)</option>
                                                                        <option value="TP(PPh100)">TP(PPh100)</option>
                                                                        <option value="TP(PPh2222)">TP(PPh2222)</option>
                                                                        <option value="TP(PPR)">TP(PPR)</option>
                                                                        <option value="TP(PVC MZ)">TP(PVC MZ)</option>
                                                                        <option value="TP(PVC CAW)">TP(PVC CAW)</option>
                                                                        <option value="TP(PVC EN)">TP(PVC EN)</option>
                                                                        <option value="TP(PVC-U grey)">TP(PVC-U grey)</option>
                                                                        <option value="TP(PVC-U red)">TP(PVC-U red)</option>
                                                                        <option value="TP(PVC NL)">TP(PVC NL)</option>
                                                                        <option value="TP(PVC Dekadur plus)">TP(PVC Dekadur plus)</option>
                                                                        <option value="TP(C-PVC)">P(C-PVC)</option>
                                                                        <option value="Steel">Steel</option>
                                                                        <option value="Other">Other</option>
                                                                    </select>
                                                                </label>
                                                            </div>
                                                            <div class="col-lg-6">
                                                                <label for="name" style="width:200px;">Weight (kg):<br>
                                                                    <input type="number" min="0" id="subCkg" placeholder="kg" class="form-control form-100">
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="number" id="editSubcomponentId" class="hidden">
                                                        <button type="button" class="btn btn-primary form-100" id="editSubcomponentSave">Save</button>
                                                        <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteComponent">
                                            <div class="modal-dialog" style="margin-top:10px;">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Are you sure to delete <strong id="deleteComponentScope"></strong>?</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <input type="number" id="deleteComponentId" class="hidden">
                                                        <button type="button" class="btn btn-primary form-100" id="deleteComponentDelete">Delete</button>
                                                        <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="modal fade" data-backdrop="static" data-keyboard="false" id="dashboardEditPhoto">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h4 class="modal-title">Photo</h4>
                                                        <button type="button" class="close" data-dismiss="modal">&times;</button>
                                                    </div>
                                                    <form method="POST" enctype="multipart/form-data">
                                                        <div class="modal-body">
                                                            <div class="row justify-content-center">
                                                                <label style="width:200px; margin:0 auto;">Tank photo:<br>
                                                                    <input type="file" name="tank-photo">
                                                                </label>
                                                            </div>
                                                            <div class="row text-center">
                                                                <p style="margin:0 auto;">Only *.jpg, *.png, *.jpeg, *.svg</p>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <input type="submit" name="saveDashboardEditPhoto" value="Save" class="btn btn-primary form-100">
                                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="d-flex justify-content-end py-2 mt-2">
                                            <?php include 'assets/php/removeArticle.php'; ?>
                                        </div>

                                        <?php
                                        if (isset($_POST['saveDashboardEditPhoto'])) {
                                            if ($_FILES["tank-photo"]["tmp_name"] != "") {
                                                if (!is_dir($folder)) {
                                                    $dirname = getIdToSADirPath($article['id']);
                                                    $folder = "../../orders/" . $dirname;
                                                }

                                                $id_to_folder_path = getIdToSADirPath($article['id']);


                                                $tank_photo_name = "tank-photo";

                                                if ($id_to_folder_path != $article['id']) {
                                                    $tank_photo_name = "tank-photo_" . $article['id'];
                                                }

                                                $name = $tank_photo_name . "." . end(explode(".", $_FILES["tank-photo"]["name"]));

                                                unlink(glob($folder . "/tank-photo.*")[0]);
                                                move_uploaded_file($_FILES["tank-photo"]["tmp_name"], $folder . '/' . $name);
                                                echo $folder . '/' . $name;
                                                $link = connect();
                                                $link->query(sprintf(
                                                    "UPDATE salesarticles SET photo_link='%s' WHERE id='%s'",
                                                    mysqli_real_escape_string($link, $name),
                                                    mysqli_real_escape_string($link, $article['id'])
                                                ));
                                                $link->close();
                                                //exec( 'icacls "'.$folder.'/'.$name.'" /q /c /reset' );
                                            }
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }


                                        if (isset($_POST['saveDashboardEditDetails'])) {
                                            $link = connect();

                                            $id_to_folder_path = getIdToSADirPath($article['id']);

                                            // przy zmianie PON lub SON dla artykułu ze starym schematem nazewnictwa katalogów zmień nazwę katalogu oraz aktualizuj folder link w bazie
                                            if ($id_to_folder_path == $article['id']) {

                                                $pon_changed = (strip_tags($_POST['artPON']) != $article['PON']) ? 1 : 0;
                                                $son_changed = (strip_tags($_POST['artSON']) != $article['SON']) ? 1 : 0;

                                                if ($pon_changed || $son_changed) {

                                                    $old_folder_link = $article['folder_link'];
                                                    $old_folder_path = "../../orders/" . $old_folder_link;

                                                    $pon = strip_tags($_POST['artPON']);
                                                    $son = strip_tags($_POST['artSON']);
                                                    $new_folder_link = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", $article['id'] . "-" . $pon . "-" . $son)))));
                                                    $new_folder_path = "../../orders/" . $new_folder_link;

                                                    // auf_error stands for Article Update Folder error
                                                    if (!is_dir($old_folder_path)) {
                                                        header("Location: article-dashboard.php?id=" . $article['id'] . "&auf_error=1");
                                                        exit();
                                                    }

                                                    if (file_exists($new_folder_path)) {
                                                        header("Location: article-dashboard.php?id=" . $article['id'] . "&auf_error=2");
                                                        exit();
                                                    }

                                                    $renamed = rename($old_folder_path, $new_folder_path);

                                                    if (!$renamed) {
                                                        header("Location: article-dashboard.php?id=" . $article['id'] . "&auf_error=3");
                                                        exit();
                                                    }

                                                    $link->query(sprintf(
                                                        "UPDATE salesarticles SET folder_link='%s' WHERE id='%s'",
                                                        mysqli_real_escape_string($link, $new_folder_link),
                                                        mysqli_real_escape_string($link, $article['id'])
                                                    ));
                                                }
                                            }

                                            if ($article['lessonsLearned'] != $_POST['artLessons'] && $_POST['artLessons'] == 1) {
                                                $rbhWgTechnologii = $article['rbhWgTechnologii'];

                                                if ($_POST['techHoursGRP'] + $_POST['techHoursTP'] != 0) {
                                                    $rbhWgTechnologii = $_POST['techHoursGRP'] + $_POST['techHoursTP'];
                                                }


                                                $link->query(sprintf(
                                                    "UPDATE salesarticles SET name='%s', diameter='%s', weight='%s', SON='%s', PON='%s', costGroup='%s', linerMaterial='%s', hours='%s', status='%s', staticMaterial='%s', isCritical='%s', rbhTechGRP='%s', rbhTechTP='%s', rbhWgTechnologii='%s', materialyWydane='%s', weightActual='%s', lessonsLearned='%s', frozen='%s', discuss_user='%s', intercompanyOrderNo='%s', nobo='%s', noboClient='%s', ped='%s', exchangeRate='%s' WHERE id='%s'",
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artScope'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artSON'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artPON'])),
                                                    mysqli_real_escape_string($link, strip_tags(costGroup($_POST['artPON']))),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artHours'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artCritical'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])),
                                                    mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])),
                                                    mysqli_real_escape_string($link, strip_tags($_SESSION['plasticonDigitalUser']['imie'] . " " . $_SESSION['plasticonDigitalUser']['nazwisko'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artIntercompanyOrderNo'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['nobo'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['noboClient'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artPed'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])),
                                                    mysqli_real_escape_string($link, strip_tags($article['id']))
                                                ));

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artScope'])) != $article['name']) {
                                                    saveLog($article['id'], "Name changed " . $article['name'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artScope'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])) != $article['diameter']) {
                                                    saveLog($article['id'], "Diameter changed " . $article['diameter'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])) != $article['weight']) {
                                                    saveLog($article['id'], "Weight changed " . $article['weight'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artSON'])) != $article['SON']) {
                                                    saveLog($article['id'], "SON changed " . $article['SON'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artSON'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artPON'])) != $article['PON']) {
                                                    saveLog($article['id'], "PON changed " . $article['PON'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artPON'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])) != $article['linerMaterial']) {
                                                    saveLog($article['id'], "Liner material changed " . $article['linerMaterial'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artHours'])) != $article['hours']) {
                                                    saveLog($article['id'], "GCE hours changed " . $article['hours'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artHours'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])) != $article['status']) {
                                                    saveLog($article['id'], "Status changed " . $article['status'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])) != $article['staticMaterial']) {
                                                    saveLog($article['id'], "Static material " . $article['staticMaterial'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])) != $article['rbhTechGRP']) {
                                                    saveLog($article['id'], "Technology hours GRP " . $article['rbhTechGRP'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])) != $article['rbhTechTP']) {
                                                    saveLog($article['id'], "Technology hours TP " . $article['rbhTechTP'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)) != $article['rbhWgTechnologii']) {
                                                    saveLog($article['id'], "Technology hours " . $article['rbhWgTechnologii'] . " => " . mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])) != $article['materialyWydane']) {
                                                    saveLog($article['id'], "Technology weight " . $article['materialyWydane'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])) != $article['weightActual']) {
                                                    saveLog($article['id'], "Actual weight " . $article['weightActual'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])) != $article['frozen']) {
                                                    saveLog($article['id'], "Fabrication period frozen " . $article['frozen'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])) != $article['lessonsLearned']) {
                                                    saveLog($article['id'], "Discuss on Monday Meeting " . $article['lessonsLearned'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])) != $article['exchangeRate']) {
                                                    saveLog($article['id'], "Exchange rate (EUR to PLN) " . $article['exchangeRate'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])));
                                                }
                                            } else {
                                                $rbhWgTechnologii = $article['rbhWgTechnologii'];
                                                if ($_POST['techHoursGRP'] + $_POST['techHoursTP'] != 0) {
                                                    $rbhWgTechnologii = $_POST['techHoursGRP'] + $_POST['techHoursTP'];
                                                }


                                                $link->query(sprintf(
                                                    "UPDATE salesarticles SET name='%s', diameter='%s', weight='%s', SON='%s', PON='%s', costGroup='%s', linerMaterial='%s', hours='%s', status='%s', staticMaterial='%s', isCritical='%s', rbhTechGRP='%s', rbhTechTP='%s', rbhWgTechnologii='%s', materialyWydane='%s', weightActual='%s', lessonsLearned='%s', frozen='%s', discuss_user='%s', intercompanyOrderNo='%s', nobo='%s', noboClient='%s', ped='%s', exchangeRate='%s' WHERE id='%s'",
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artScope'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artSON'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artPON'])),
                                                    mysqli_real_escape_string($link, strip_tags(costGroup($_POST['artPON']))),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artHours'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artCritical'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])),
                                                    mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])),
                                                    mysqli_real_escape_string($link, strip_tags("")),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artIntercompanyOrderNo'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['nobo'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['noboClient'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['artPed'])),
                                                    mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])),
                                                    mysqli_real_escape_string($link, strip_tags($article['id']))
                                                ));

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artScope'])) != $article['name']) {
                                                    saveLog($article['id'], "Name changed " . $article['name'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artScope'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])) != $article['diameter']) {
                                                    saveLog($article['id'], "Diameter changed " . $article['diameter'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artDiameter'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])) != $article['weight']) {
                                                    saveLog($article['id'], "Weight changed " . $article['weight'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artWeight'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artSON'])) != $article['SON']) {
                                                    saveLog($article['id'], "SON changed " . $article['SON'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artSON'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artPON'])) != $article['PON']) {
                                                    saveLog($article['id'], "PON changed " . $article['PON'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artPON'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])) != $article['linerMaterial']) {
                                                    saveLog($article['id'], "Liner material changed " . $article['linerMaterial'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artLinerMaterial'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artHours'])) != $article['hours']) {
                                                    saveLog($article['id'], "GCE hours changed " . $article['hours'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artHours'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])) != $article['status']) {
                                                    saveLog($article['id'], "Status changed " . $article['status'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artStatus'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])) != $article['staticMaterial']) {
                                                    saveLog($article['id'], "Static material " . $article['staticMaterial'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artStaticMaterial'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])) != $article['rbhTechGRP']) {
                                                    saveLog($article['id'], "Technology hours GRP " . $article['rbhTechGRP'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techHoursGRP'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])) != $article['rbhTechTP']) {
                                                    saveLog($article['id'], "Technology hours TP " . $article['rbhTechTP'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techHoursTP'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)) != $article['rbhWgTechnologii']) {
                                                    saveLog($article['id'], "Technology hours " . $article['rbhWgTechnologii'] . " => " . mysqli_real_escape_string($link, strip_tags($rbhWgTechnologii)));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])) != $article['materialyWydane']) {
                                                    saveLog($article['id'], "Technology weight " . $article['materialyWydane'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['techWeight'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])) != $article['weightActual']) {
                                                    saveLog($article['id'], "Actual weight " . $article['weightActual'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['actWeightEd'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])) != $article['frozen']) {
                                                    saveLog($article['id'], "Fabrication period frozen " . $article['frozen'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artFrozen'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])) != $article['lessonsLearned']) {
                                                    saveLog($article['id'], "Discuss on Monday Meeting " . $article['lessonsLearned'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['artLessons'])));
                                                }

                                                if (mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])) != $article['exchangeRate']) {
                                                    saveLog($article['id'], "Exchange rate (EUR to PLN) " . $article['exchangeRate'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['exchangeRate'])));
                                                }
                                            }

                                            $link->query(sprintf(
                                                "UPDATE projects SET isCritical='%s', penalties='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['artCritical'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['artPenalties'])),
                                                mysqli_real_escape_string($link, strip_tags($project['id']))
                                            ));
                                            $sanitizedData = sanitizedData($link, $_POST);

                                            $link->close();

                                            // NOBO
                                            // Insert or update nobo
                                            $noboData = (object)[
                                                'ped' => $sanitizedData['ped'],
                                                'pedCategory' => $sanitizedData['pedCategory'],
                                                'medium' => $sanitizedData['dangerousMedium'],
                                                'resistance' => $sanitizedData['chemicalResistance'],
                                                // 'nominalLevel' => $sanitizedData['nominalLevel'],
                                                'density' => $sanitizedData['density']
                                            ];

                                            processNoboData($paramId, $noboData, $_SESSION['plasticonDigitalUser']);

                                            // Insert compounds
                                            $compoundsData = (object)[
                                                "compounds" => $sanitizedData['0-compound'],
                                                "concentrations" => $sanitizedData['0-concentration']
                                            ];

                                            insertCompoundData($paramId, $compoundsData, $_SESSION['plasticonDigitalUser']);
                                            // /nobo

                                            setOrderStatus($article['offerNo']);
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }
                                        if (isset($_POST['saveDashboardEditSales'])) {
                                            $link = connect();
                                            $link->query(sprintf(
                                                "UPDATE salesarticles SET offerNo='%s', orderDate='%s', issueDate='%s', PC='%s', orderValue='%s', cmSales='%s', cmProd='%s', segment='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['proOfferNo'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proOrderDate'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proIssueDate'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proProductionCompany'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proSalesORV'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proSalesCM'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proProdCM'])),
                                                mysqli_real_escape_string($link, strip_tags(str_replace(">", "&gt;", str_replace("<", "&lt;", str_replace("⌀", "&#8960;", $_POST['proSegment']))))),
                                                mysqli_real_escape_string($link, strip_tags($article['id']))
                                            ));

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proOfferNo'])) != $article['offerNo']) {
                                                saveLog($article['id'], "Offer number changed " . $article['offerNo'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proOfferNo'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proOrderDate'])) != $article['orderDate']) {
                                                saveLog($article['id'], "Order date changed " . $article['orderDate'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proOrderDate'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proIssueDate'])) != $article['issueDate']) {
                                                saveLog($article['id'], "Issue date changed " . $article['issueDate'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proIssueDate'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proProductionCompany'])) != $article['PC']) {
                                                saveLog($article['id'], "Production company changed " . $article['PC'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proProductionCompany'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proSalesORV'])) != $article['orderValue']) {
                                                saveLog($article['id'], "Order value changed " . $article['orderValue'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proSalesORV'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proSalesCM'])) != $article['cmSales']) {
                                                saveLog($article['id'], "Sales CM changed " . $article['cmSales'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proSalesCM'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['proProdCM'])) != $article['cmProd']) {
                                                saveLog($article['id'], "Production CM changed " . $article['cmProd'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['proProdCM'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags(str_replace(">", "&gt;", str_replace("<", "&lt;", str_replace("⌀", "&#8960;", $_POST['proSegment']))))) != $article['segment']) {
                                                saveLog($article['id'], "Segment changed " . $article['segment'] . " => " . mysqli_real_escape_string($link, strip_tags(str_replace(">", "&gt;", str_replace("<", "&lt;", str_replace("⌀", "&#8960;", $_POST['proSegment']))))));
                                            }

                                            $link->query(sprintf(
                                                "UPDATE projects SET offerNo='%s', SC='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['proOfferNo'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['proSalesCompany'])),
                                                mysqli_real_escape_string($link, strip_tags($project['id']))
                                            ));
                                            $link->close();
                                            $link = connectCRM();
                                            $link->query(sprintf(
                                                "UPDATE components SET productionLocation='%s' WHERE offerNo='%s' AND orderNo='%s' AND orderNoProduction='%s' ",
                                                mysqli_real_escape_string($link, strip_tags($_POST['proProductionCompany'])),
                                                mysqli_real_escape_string($link, strip_tags($article['offerNo'])),
                                                mysqli_real_escape_string($link, strip_tags($article['SON'])),
                                                mysqli_real_escape_string($link, strip_tags($article['PON']))
                                            ));
                                            $result = $link->query(sprintf(
                                                "SELECT * FROM components  WHERE offerNo='%s' AND orderNo='%s' AND orderNoProduction='%s' ",
                                                mysqli_real_escape_string($link, strip_tags($article['offerNo'])),
                                                mysqli_real_escape_string($link, strip_tags($article['SON'])),
                                                mysqli_real_escape_string($link, strip_tags($article['PON']))
                                            ));
                                            $row = $result->fetch_object();
                                            $cmpCRMid = $row->id;
                                            $result = $link->query("SELECT DISTINCT(productionLocation) as productionLocation FROM components WHERE offerNo='$offerNo' AND counts=1");
                                            $locs = "";
                                            while ($row = $result->fetch_object()) {
                                                $locs .= ($row->productionLocation) . ",";
                                            }
                                            $locs = substr($locs, 0, -1);
                                            $link->query(sprintf(
                                                "UPDATE offers SET productionLocation='%s' WHERE offerNo='%s'",
                                                mysqli_real_escape_string($link, strip_tags($locs)),
                                                mysqli_real_escape_string($link, strip_tags($article['offerNo']))
                                            ));
                                            $link->query(sprintf(
                                                "UPDATE stats SET productionLocation='%s' WHERE IdCmp='%s' ",
                                                mysqli_real_escape_string($link, strip_tags($_POST['proProductionCompany'])),
                                                mysqli_real_escape_string($link, strip_tags($cmpCRMid))
                                            ));
                                            $link->close();
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }
                                        if (isset($_POST['saveDashboardEditCustomer'])) {
                                            $link = connect();
                                            $link->query(sprintf(
                                                "UPDATE projects SET clientId='%s', clientName='%s', location='%s', `clientOrderNo`='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['client'])),
                                                mysqli_real_escape_string($link, strip_tags(getClientName($_POST['client']))),
                                                mysqli_real_escape_string($link, strip_tags(getClientLocation($_POST['client']))),
                                                mysqli_real_escape_string($link, strip_tags($_POST['clientOrderNo'])),
                                                mysqli_real_escape_string($link, strip_tags($project['id']))
                                            ));

                                            if (mysqli_real_escape_string($link, strip_tags(getClientName($_POST['client']))) != $project['clientName']) {
                                                saveLog($article['id'], "Client changed " . $project['clientName'] . " => " . mysqli_real_escape_string($link, strip_tags(getClientName($_POST['client']))));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['clientOrderNo'])) != $project['clientOrderNo']) {
                                                saveLog($article['id'], "Client order number changed " . $project['clientOrderNo'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['clientOrderNo'])));
                                            }

                                            $link->close();
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }
                                        if (isset($_POST['saveDashboardEditTransport'])) {
                                            $link = connect();
                                            $link->query(sprintf(
                                                "UPDATE projects SET deliveryTerms='%s', deliveryAddress='%s' WHERE id='%s'",
                                                //mysqli_real_escape_string($link, strip_tags($_POST['deliveryDate'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['deliveryTerms'])),
                                                mysqli_real_escape_string($link, strip_tags($_POST['deliveryAddress'])),
                                                mysqli_real_escape_string($link, strip_tags($project['id']))
                                            ));

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['deliveryTerms'])) != $project['deliveryTerms']) {
                                                saveLog($article['id'], "Delivery term changed " . $project['deliveryTerms'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['deliveryTerms'])));
                                            }

                                            if (mysqli_real_escape_string($link, strip_tags($_POST['deliveryAddress'])) != $project['deliveryAddress']) {
                                                saveLog($article['id'], "Delivery address changed " . $project['deliveryAddress'] . " => " . mysqli_real_escape_string($link, strip_tags($_POST['deliveryAddress'])));
                                            }

                                            //                                        if (mysqli_real_escape_string($link, strip_tags($_POST['deliveryDate'])) != $project['deliveryAddress']) {
                                            //                                            saveLog($article['id'], "Delivery date changed ".$project['deliveryAddress']." => ".mysqli_real_escape_string($link, strip_tags($_POST['deliveryDate'])));
                                            //                                        }

                                            $link->close();
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }
                                        if (isset($_POST['saveDashboardEditContact'])) {
                                            $link = connect();
                                            $link->query(sprintf(
                                                "UPDATE projects SET projectManagerId='%s', projectManagerFull='%s', supportingManagerId='%s', supportingManagerFull='%s', insideSalesId='%s', insideSalesFull='%s', responsibleSalesId='%s', responsibleSalesFull='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['projectManager'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['projectManager']))),
                                                mysqli_real_escape_string($link, strip_tags($_POST['supportingManager'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['supportingManager']))),
                                                mysqli_real_escape_string($link, strip_tags($_POST['insideSales'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['insideSales']))),
                                                mysqli_real_escape_string($link, strip_tags($_POST['responsibleSales'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['responsibleSales']))),
                                                mysqli_real_escape_string($link, strip_tags($project['id']))
                                            ));
                                            $link->query(sprintf(
                                                "UPDATE salesarticles SET projectManagerId='%s', projectManagerFull='%s', supportingManagerId='%s', supportingManagerFull='%s' WHERE id='%s'",
                                                mysqli_real_escape_string($link, strip_tags($_POST['projectManager'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['projectManager']))),
                                                mysqli_real_escape_string($link, strip_tags($_POST['supportingManager'])),
                                                mysqli_real_escape_string($link, strip_tags(getUserNameAndSurname($_POST['supportingManager']))),
                                                mysqli_real_escape_string($link, strip_tags($article['id']))
                                            ));
                                            $link->close();
                                            header("Location: article-dashboard.php?id=" . $article['id'] . "&saved=1");
                                        }
                                        ?>
                                    </div>
                                    <div id="fullSizeDiv" class="position-fixed hidden text-center fullSizePhotoDiv justify-content-center">
                                        <div class="img-magnifier-container position-relative">
                                            <img id="fullSizeImg" src="<?php echo $image !== false ? $image : $image; ?>"></img>
                                        </div>
                                        <i class="fas fa-times position-absolute pointer fullSizePhotoClose" title="Close" onclick="fullSizePhoto('hide')"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php require('footer.php'); ?>

            <script src="assets/plugins/waypoints/lib/jquery.waypoints.min.js"></script>
            <script>
                var components = "";

                function deleteComponent(id, scope, parent) {
                    $('#deleteComponentScope').html(scope);
                    $('#deleteComponentId').val(id);
                    $('#deleteComponent').modal('show');
                }

                function getEditCmpInfo(id) {
                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php',
                        data: {
                            action: "getSubcomponentInfo",
                            id: id
                        },
                        success: function(subc) {
                            subc = JSON.parse(subc);
                            $('#subCscope').val(subc.scope);
                            $('#subCproductionLocation').val(subc.productionLocation).trigger("change");
                            $('#subCstat').val(subc.stat).trigger("change");
                            $('#subCprot').val(subc.prot).trigger("change");
                            $('#subCDN').val(subc.diameter);
                            $('#subCkg').val(subc.weight);
                            $('#editSubcomponentId').val(subc.id);
                            $('#componentEditScopeLabel').html(subc.scope);
                            $('#editSubcomponent').modal('show');
                        }
                    });
                }

                function fullSizePhoto(opt) {
                    if (opt == "show")
                        $('#fullSizeDiv').fadeIn('fast');
                    else
                        $('#fullSizeDiv').fadeOut('fast');
                }

                function getProgressInfo() {
                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php',
                        data: {
                            action: "getOrderProgress",
                            id: <?= $article['id']; ?>
                        },
                        success: function(info) {
                            info = JSON.parse(info);
                            if (info.length > 0) {
                                $("#timeline-chart").children().eq(0).children().html("");
                                $("#timeline-chart").children().eq(1).children().html("");
                                $("#timeline-chart").children().eq(2).children().html("<div class='status-progress-container'></div>");
                                $("#timeline-chart").children().eq(2).children().html("<div class='status-progress' id='progress'></div>");
                                $("#timeline-chart").children().eq(3).children().html("");
                                for (var i = 0; i < info.length; i++) {
                                    var row = info[i];
                                    $("#timeline-chart").children().eq(0).children().append(row[0]);
                                    $("#timeline-chart").children().eq(1).children().append(row[1]);
                                    $("#timeline-chart").children().eq(2).children().append(row[2]);
                                    $("#timeline-chart").children().eq(3).children().append(row[3]);
                                    if (row[4] == "Not started") {
                                        $("#timeline-chart").children().eq(2).children().children().eq(i + 1).css('border', '3px solid var(--line-border-empty)').html('<i class="fas fa-times text-danger"></i>');
                                    }
                                    if (row[4] == "Started") {
                                        $("#timeline-chart").children().eq(2).children().children().eq(i + 1).html('<i class="fas fa-hourglass-half text-warning"></i>');
                                    }
                                    if (row[4] == "Finished" && row[5] == "0") {
                                        $("#timeline-chart").children().eq(2).children().children().eq(i + 1).html('<i class="fas fa-check text-success"></i>');
                                    }
                                    if (row[4] == "Finished" && row[5] == "1") {
                                        $("#timeline-chart").children().eq(2).children().children().eq(i + 1).html('<i class="fas fa-check text-warning"></i>');
                                    }
                                }
                                $(".noZeros").each(function() {
                                    if ($(this).html() == "<i>00/00/00</i>" || $(this).html() == "<b>00/00/00</b>")
                                        $(this).html("&nbsp;");
                                })
                                const progress = document.getElementById("progress");
                                const circles = document.querySelectorAll(".status-circle");
                                const actives = document.querySelectorAll(".active");
                                if (status >= 5)
                                    progress.style.width = "100%";
                                else
                                    progress.style.width = ((actives.length - 1) / (circles.length - 1)) * 100 + "%";
                                if (((actives.length - 1) / (circles.length - 1)) * 100 > 100)
                                    progress.style.width = "100%";
                            }
                        }
                    });
                }

                $(document).ready(function() {
                    <?php $curTime = time(); ?>
                    let url = {
                        l_id: '<?= $user['id']; ?>',
                        l_time: '<?= $curTime; ?>',
                        l_control: '<?= md5($curTime . $user['id'] . $user['secret']); ?>',
                        oms_id: '<?= $article['id']; ?>',
                        act: 'projects_checkaccess'
                    };

                    let linkSelector = ".instatank-link"; //jakiś selektor do linku

                    $.ajax({
                        type: "GET",
                        url: "https://photo.plasticon.app",
                        data: url,
                        cache: false,
                        async: true
                    }).done(function(json) {
                        let link = $(linkSelector);

                        if (link && link.length > 0) {
                            if (json.status === "ok") {
                                link.show();
                                link.attr('href', json.link);
                            }
                        } else {
                            link.hide();
                        }
                    }).fail(function(responseObject) {
                        try {
                            let responseText = responseObject.responseText,
                                jobj = undefined;
                            if (responseText) {
                                jobj = JSON && JSON.parse(responseText) || $.parseJSON(responseText);
                            }
                            if (jobj) {
                                if (jobj.hasOwnProperty('status') && jobj.status === "error" && jobj.hasOwnProperty('error')) {
                                    console.error('[Photo]: ' + jobj.error);
                                } else {
                                    console.error('[Photo]: unrecognized server error');
                                }
                            } else {
                                console.error('[Photo]: unrecognized server response');
                            }
                        } catch (e) {
                            console.error('[Photo]: ' + e.message);
                        }
                    });

                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php',
                        data: {
                            action: "saveArtPage",
                            page: "dashboard"
                        },
                        success: function() {
                            var link = $("#prevBtn").prop("href");

                            if (link) {
                                link = link.replace("article", "article-dashboard");
                                $("#prevBtn").prop("href", link);
                                link = $("#nextBtn").prop("href")
                                link = link.replace("article", "article-dashboard");
                                $("#nextBtn").prop("href", link);
                            }
                        }
                    });

                    $('#nobo').change(function() {
                        if ($(this).val() == "NOBO from Client")
                            $("#noboClient").prop("disabled", false).prop("required", true);
                        else
                            $("#noboClient").prop("disabled", true).prop("required", false).val("");
                    })

                    $(".select2").select2();
                    var selectClient = $(".select2-client").select2({
                        allowClear: true,
                        placeholder: 'Select',
                        ajax: {
                            method: "GET",
                            url: "assets/php/ajaxHandeler.php",
                            dataType: 'json',
                            delay: 250,
                            data: function(params) {
                                return {
                                    name: params.term,
                                    action: 'getClientName'
                                };
                            },
                            processResults: function(data, params) {
                                var resData = [];
                                data.forEach(function(value) {
                                    if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                        resData.push(value)
                                })
                                return {
                                    results: $.map(resData, function(item) {
                                        return {
                                            text: item.longName,
                                            id: item.id
                                        }
                                    })
                                };
                            },
                            cache: true,
                            tags: true
                        },
                        minimumInputLength: 3
                    });

                    getProgressInfo();

                    $('#addComponent').click(function() {
                        $.ajax({
                            type: 'get',
                            url: 'assets/php/ajaxHandeler.php',
                            data: {
                                action: "addNewSubComponent",
                                articleId: "<?= $article['id']; ?>",
                                offerNo: "<?= $article['offerNo']; ?>",
                                stat: "<?= $article['staticMaterial']; ?>",
                                prot: "<?= $article['linerMaterial']; ?>"
                            },
                            success: function() {
                                components.ajax.reload();
                            }
                        });
                    });

                    $('INPUT[name="tank-photo"]').change(function() {
                        if (!$(this).hasClass("no-check")) {
                            var ext = this.value.match(/\.(.+)$/)[1];
                            switch (ext) {
                                case 'jpg':
                                case 'jpeg':
                                case 'png':
                                case 'svg':
                                    break;
                                default:
                                    alert('This is not an allowed file type, use only *.jpg, *.png, *.jpeg, *.svg');
                                    this.value = '';
                            }
                        }
                    });

                    $('#deleteComponentDelete').click(function() {
                        $.ajax({
                            type: 'get',
                            url: 'assets/php/ajaxHandeler.php',
                            data: {
                                action: "deleteComponentOfferInfo",
                                componentId: $('#deleteComponentId').val()
                            },
                            success: function() {
                                $('#deleteComponent').modal('hide');
                                components.ajax.reload();
                            }
                        });
                    })

                    $('#editSubcomponentSave').click(function() {
                        $("#subCmpLoader").removeClass("hidden");
                        $.ajax({
                            type: 'get',
                            url: 'assets/php/ajaxHandeler.php',
                            data: {
                                action: "saveSubcomponentEdit",
                                scope: $('#subCscope').val(),
                                productionLocation: $('#subCproductionLocation').val(),
                                stat: $('#subCstat').val(),
                                prot: $('#subCprot').val(),
                                diameter: $('#subCDN').val(),
                                weight: $('#subCkg').val(),
                                id: $('#editSubcomponentId').val()
                            },
                            success: function() {
                                $('#editSubcomponent').modal('hide');
                                components.ajax.reload();
                                $("#subCmpLoader").addClass("hidden");
                            }
                        });
                    })

                    components = $('#componentsTable').DataTable({
                        "iDisplayLength": 50,
                        searching: false,
                        info: false,
                        paging: false,
                        stateSave: true,
                        ordering: false,
                        "stateDuration": 0,
                        "order": [
                            [0, "desc"]
                        ],
                        "processing": true,
                        "serverSide": true,
                        'ajax': {
                            'url': 'assets/php/componentsOfferInfoProcessing.php',
                            type: "POST",
                            "data": {
                                articleId: <?= $article['id']; ?>,
                            }
                        },
                        "columnDefs": [{
                            "orderable": false,
                            "targets": 7
                        }, ],
                        'columns': [{
                                data: 'id',
                                name: 'id'
                            },
                            {
                                data: 'scope',
                                name: 'scope'
                            },
                            {
                                data: 'productionLocation',
                                name: 'productionLocation'
                            },
                            {
                                data: 'stat',
                                name: 'stat'
                            },
                            {
                                data: 'prot',
                                name: 'prot'
                            },
                            {
                                data: 'diameter',
                                name: 'diameter'
                            },
                            {
                                data: 'weight',
                                name: 'weight'
                            },
                            {
                                data: 'edit',
                                name: 'edit',
                                className: 'tableM'
                            }
                        ],
                    });

                    $("[name='artLinerMaterial']").val("<?= $article['linerMaterial'] ?>").trigger('change');
                    $("[name='artStaticMaterial']").val("<?= $article['staticMaterial'] ?>").trigger('change');
                    $("[name='artCritical']").val("<?= $article['isCritical'] ?>").trigger('change');
                    $("[name='artStatus']").val("<?= $article['status'] ?>").trigger('change');
                    $("[name='artPenalties']").val("<?= $project['penalties'] ?>").trigger('change');

                    var newState = new Option("<?= $project['clientId']; ?>" + " | " + "<?= $project['clientName']; ?>", "<?= $project['clientId']; ?>", true, true);

                    $("[name='client']").append(newState).trigger('change');
                    $("[name='projectManager']").val('<?= $article["projectManagerId"]; ?>').trigger('change');
                    $("[name='supportingManager']").val('<?= $article["supportingManagerId"]; ?>').trigger('change');
                    $("[name='insideSales']").val('<?= $project["insideSalesId"]; ?>').trigger('change');
                    $("[name='responsibleSales']").val('<?= $project["responsibleSalesId"]; ?>').trigger('change');
                    $("[name='artLessons']").val('<?= $article["lessonsLearned"]; ?>').trigger('change');
                    $("[name='artFrozen']").val('<?= $article["frozen"]; ?>').trigger('change');
                    $("[name='proSalesCompany']").val('<?= $project["SC"]; ?>').trigger('change');
                    $("[name='proProductionCompany']").val('<?= $article["PC"]; ?>').trigger('change');
                    $("[name='nobo']").val('<?= $article["nobo"]; ?>').trigger('change');
                    $("[name='artPed']").val('<?= $article["ped"]; ?>').trigger('change');
                    $("[name='proSegment']").val('<?= str_replace("&gt;", ">", str_replace("&lt;", "<", str_replace("&#8960;", "⌀", $article["segment"]))); ?>').trigger('change');
                });
            </script>
            <script src="assets/js/fixedTopTable.js"></script>
            <style>
                .select2-container {
                    width: 100% !important;
                    /* max-width: 200px; */

                    text-align: left;
                }

                .nobo-wrapper .select2-container {
                    width: 200px !important;
                }

                .select2-container .select2-selection--single {
                    height: 36px;
                }

                .select2-container--default .select2-selection--single .select2-selection__arrow {
                    height: 34px;
                }

                .select2-container--default .select2-selection--single .select2-selection__rendered {
                    line-height: 32px;
                }

                .select2-container--default .select2-selection--single {
                    border: 1px solid #ced4da;
                }

                .select2-container--default .select2-selection--single .select2-selection__rendered {
                    color: #495057;
                    font-size: 1rem;
                    font-family: inherit;
                    font-weight: 430;
                }

                .select2-container--default .select2-selection--multiple .select2-selection__choice {
                    margin-top: 2px;
                    line-height: 20px;
                }

                .select2-container .select2-selection--multiple {
                    min-height: 28px;
                }

                .select2-selection__rendered {
                    color: #6C757D !important;
                }

                #today {
                    height: 100%;
                    width: 3px;
                    top: 0px;
                    position: absolute;
                    background-color: rgba(220, 0, 0, 1);
                }

                #today::before {
                    content: "Now";
                    width: auto;
                    height: auto;
                    padding: 2px 10px;
                    background-color: rgba(220, 0, 0, 1);
                }

                .responsibleChart {
                    position: absolute;
                    top: -1px;
                    left: -1px;
                    color: #000;
                    width: 30px;
                    height: 30px;
                    border-radius: 2px 100px 100px 2px;
                    border: 2px solid #299cb4;
                    background-color: #FFF;
                    padding-left: 3px;
                }

                .statusChart {
                    position: absolute;
                    top: -1px;
                    right: -1px;
                    color: #000;
                    width: 30px;
                    height: 30px;
                    border-radius: 100px 2px 2px 100px;
                    background-color: #FFF;
                    padding-left: 3px;
                    text-align: center;
                }

                .statusFinished {
                    border: 2px solid #28a745;
                }

                .statusProggress {
                    border: 2px solid #007bff;
                }

                .statusNotStarted {
                    border: 2px solid #ffc107;
                }
            </style>
        <?php } ?>