<?php
include("functions.php");
$link=connect();
## Read value
$draw = $_POST['draw'];
$id=$_POST['id'];

## Fetch records
$empQuery = "SELECT * FROM platforms WHERE clientId='$id'"; 
$empRecords = $link->query($empQuery);
$prevEmpRecords = $link->query($prevEmpQuery);
$data = array();
while ($row = $empRecords->fetch_object()) {
	$actions="<a href='#' onclick='platformsDelInfo(".$row->id.")'>Delete</a> | <a href='#' onclick='platformsEditInfo(".$row->id.")'>Edit</a>";
	$url=$row->desc;
	if(!filter_var($row->desc, FILTER_VALIDATE_URL)===false) {
		$url="<a href='".$row->desc."' target='_blank'>".$row->desc."</a>";
	}
    $data[] = array(
    		"desc"=>$url,
    		"login"=>$row->login,
    		"password"=>'<div class="mb-3">
							  <div class="input-group mb-3">
								  <input class="form-control password" id="password" class="block mt-1 w-full" type="password" name="password" value="'.$row->password.'" disabled />
								  <span class="input-group-text togglePassword" id="">
									  <i class="fas fa-eye pointer"></i>
								  </span>
							  </div>
						  </div>',
    		"actions"=>$actions
    	);
}

## Response
$response = array(
    "draw" => intval($draw),
    "iTotalRecords" => 0,
    "iTotalDisplayRecords" => 0,
    "aaData" => $data
);

echo json_encode($response);
?>
