<?php
require('header.php');
$_SESSION['backbutton'] = "index.php";
setcookie('offerUserLogin', $_SESSION['plasticonDigitalUser']['email']);
unset($_SESSION['offerFormData']);
$userId = $_SESSION['plasticonDigitalUser']['id'];
$jsData = [
    'userId' => $userId
];
?>
<style>
    .btn-xxs {
        padding: 0.15rem 0.3rem;
        font-size: 0.7rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    @media(max-width: 1199px) {
        .pushPagePerPage {
            margin-top: 10px;
            margin-bottom: 10px;
        }
    }

    @media(min-width: 1200px) {
        .pushPagePerPage {
            margin-right: 10px;
        }
    }

    .offers-list-comment-container {
        height: auto;
        max-height: 50vh;
        overflow-y: auto;
    }

    .td-comments .comment-box {
        width: 100%;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        white-space: normal;
    }


    tr.pointer {
        line-height: 1 !important;
    }

    tr.pointer td {
        padding: 3px 2px;
    }

    .show {
        display: flex;
    }

    .select2-container {
        width: 100% !important;
        text-align: left;
    }

    .select2-container .select2-selection--single {
        height: 36px;
    }

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 34px;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 32px;
    }

    .select2-container--default .select2-selection--single {
        border: 1px solid #ced4da;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #495057;
        font-size: 1rem;
        font-family: inherit;
        font-weight: 430;
    }

    .select2-container--default .select2-selection--multiple .select2-selection__choice {
        margin-top: 2px;
        line-height: 20px;
    }

    .select2-container .select2-selection--multiple {
        min-height: 28px;
    }

    #offers_processing {
        top: 16px;
        width: 98%;
        left: 1%;
        background-color: #dedef3;
        margin: 0;
    }

    #accordion {
        margin: 0px 15px;
        height: max-content;
    }

    th {
        vertical-align: inherit !important;
    }
</style>
<script>
    window.data = <?= json_encode($jsData) ?>
</script>
<script>
    function countQsummary() {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#Qsummary').html(this.responseText);
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=countSummary&value=Q", true);
        xmlhttp.send();
    }

    function countOVsummary() {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#OVsummary').html(this.responseText + " k€");
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=countSummary&value=OVE", true);
        xmlhttp.send();
    }

    function countOValsummary() {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#OValsummary').html(this.responseText + " k€");
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=countSummary&value=OV", true);
        xmlhttp.send();
    }

    function countOVggsummary() {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#OVggsummary').html(this.responseText + " k€");
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=countSummary&value=OVgg", true);
        xmlhttp.send();
    }

    function countGxGsummary() {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#GxGsummary').html(this.responseText + " %");
                $('#GxGprogress').attr('aria-valuenow', this.responseText).css("width", this.responseText + "%");
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=countSummary&value=GxG", true);
        xmlhttp.send();
    }

    function getContacts(client) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#contactPurchase').html(this.responseText).prop("disabled", false);
                $('#contactTechnican').html(this.responseText).prop("disabled", false);
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientContacts&value=" + client, true);
        xmlhttp.send();
    }

    function getContactsEnd(client) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#contactPurchaseEnd').html(this.responseText).prop("disabled", false);
                $('#contactTechnicanEnd').html(this.responseText).prop("disabled", false);
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientContacts&value=" + client, true);
        xmlhttp.send();
    }

    function getContactsEdit(client) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                $('#contact').html(this.responseText);
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientContacts&value=" + client, true);
        xmlhttp.send();
    }

    function getCities(client, thi) {
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                thi.html(this.responseText).prop("disabled", false).trigger("change");
            }
        };
        n = encodeURIComponent(client);
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=getClientLocationsAddOffer&value=" + n, true);
        xmlhttp.send();
    }

    function saveClient() {
        $('#addClient').modal('hide');
        var clientLongName = $('#clientLongName').val();
        var enterprise = $('#enterprise').val();
        var clientShortName = $('#clientLongName').val();
        var country = $('#country').val();
        var zip = $('#zip').val();
        var city = $('#city').val();
        var category = $('#category').val();
        var type = $('#type').val();
        var adres = $('#adres').val();
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                var response = this.responseText;
                $('#alert').html("<div><br><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>Client has been added!</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>");
                $('#addInquiry').modal('show');
            }
            $('#clientLongName').val("");
            $('#enterprise').val("");
            $('#country').val("").trigger('change');
            $('#zip').val("");
            $('#city').val("");
            $('#adres').val("");
            $('#category').val("").trigger('change');
            $('#type').val("").trigger('change');
            $('#asm').val("").trigger('change');
        };
        cat = encodeURIComponent(category);
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=saveClient&clientLongName=" + clientLongName + "&enterprise=" + enterprise + "&clientShortName=" + clientShortName + "&country=" + country + "&zip=" + zip + "&city=" + city + "&type=" + type + "&category=" + cat + "&adres=" + adres, true);
        xmlhttp.send();
    }

    function saveContact() {
        $('#addContact').modal('hide');
        var gender = $('#contactGender').val();
        var name = $('#contactName').val();
        var surname = $('#contactSurname').val();
        var email = $('#contactEmail').val();
        var phone1 = $('#contactPhone1').val();
        var phone2 = $('#contactPhone2').val();
        var position = $('#contactPosition').val();
        var id = $('#addContactClientId').val();
        var ifEnd = $('#ifend').val();
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                var response = this.responseText.split("[-]");
                if (ifEnd == 0) {
                    $('#contactPurchase').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                    $('#contactTechnican').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                } else {
                    $('#contactPurchaseEnd').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                    $('#contactTechnicanEnd').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                }
                $('#alert').html("<div><br><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>Contact has been added!</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>");
                $('#addInquiry').modal('show');
            }
            $('#contactName').val("");
            $('#contactSurname').val("");
            $('#contactEmail').val("");
            $('#contactPhone1').val("");
            $('#contactPhone2').val("");
            $('#contactPosition').val("");
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=saveContact&gender=" + gender + "&name=" + name + "&surname=" + surname + "&email=" + email + "&phone1=" + phone1 + "&phone2=" + phone2 + "&position=" + position + "&id=" + id, true);
        xmlhttp.send();
    }

    function saveProject() {
        $('#addProject').modal('hide');
        var projectName = $('#projectNameNew').val();
        var plantLocation = $('#plantLocation').val();
        var plantCountry = $('#plantCountry').val();
        var description = $('#description').val();
        var status = $('#status').val();
        var endClient = $('#endClient').val();
        var comment = $('#comment').val();
        xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function() {
            if (this.readyState == 4 && this.status == 200) {
                var response = this.responseText.split("[-]");
                $('#project').append("<option value='" + response[0] + "'>" + response[1] + "</option>");
                $('#alert').html("<div><br><div class='row alert-success justify-content-center'><div style='width:97%;' class='text-center'><strong>Project has been added!</strong></div><div style='width:3%;' class='text-center'><i class='fas fa-times' onclick='$(this).parent().parent().parent().remove();' style='cursor:pointer'></i></div></div></div>");
                $('#addInquiry').modal('show');
            }
        };
        xmlhttp.open("GET", "assets/php/ajaxHandeler.php?action=saveProject&projectName=" + projectName + "&plantLocation=" + plantLocation + "&plantCountry=" + plantCountry + "&description=" + description + "&status=" + status + "&endClient=" + endClient + "&comment=" + comment, true);
        xmlhttp.send();
    }

    function redBorder() {
        $('[required]').each(function() {
            if ($(this).hasClass("select2")) {
                if ($(this).val() == '' || $(this).val() == null)
                    $(this).next().children().children().addClass("borderRed");
                else
                    $(this).next().children().children().removeClass("borderRed");
            } else
            if ($(this).val() == '' || $(this).val() == null)
                $(this).addClass("borderRed");
            else
                $(this).removeClass("borderRed");
        })
        setTimeout(function() {
            $('.borderRed').removeClass('borderRed');
        }, 5000);
    }

    function addFile() {
        var a = document.getElementById('fileInput');
        if (a.value == "") {
            fileText.innerHTML = "";
        } else {
            var theSplit = a.value.split('\\');
            fileText.innerHTML = theSplit[theSplit.length - 1];
        }
    }
</script>

<body class="widescreen adminbody-void">
    <?php
    $_SESSION['url'] = 'index.php';
    require('menu.php');
    ?>
    <div class="content-page">
        <div class="content">
            <div class="container-fluid">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 col-xl-12">
                    <div class="card mb-3">
                        <div class="card-body">


                            <div class="modal fade" id="editCommentModal">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Edit comment</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label>Comment:<br>
                                                        <textarea class="form-control form-100" name="editComment" required></textarea>
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-primary save-edit-comment">Save</button>
                                                <button type="button" class="btn btn-danger" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="delCommentModal">
                                <div class="modal-dialog ui-draggable" style="margin-top:10px;">
                                    <div class="modal-content">
                                        <div class="modal-header ui-draggable-handle">
                                            <h4 class="modal-title">Do you want to delete this comment?</h4>
                                            <button type="button" class="close" data-dismiss="modal">×</button>
                                        </div>
                                        <div class="modal-footer">
                                            <input type="submit" data-dismiss="modal" value="Yes" class="btn btn-primary submit-del-comment form-100">
                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="offerComments" tabindex="">
                                <div class="modal-dialog" style="margin-top:10px;min-width:80%;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Comments</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <div class="modal-body offers-list-comment-container">

                                        </div>

                                        <div class="modal-footer">
                                            <div class="offers-list-comment-field-wrapper" style="margin-bottom: 10px; text-align: left;">
                                                <label for="comment" id="comment_label" class="control-label mb-0">Add a comment:</label>
                                                <textarea name="offers_list_comment" class="form-control">
                                                </textarea>
                                            </div>

                                            <button type="button" class="btn btn-primary form-100 offers-list-comment-add" data-creator="<?php echo $_SESSION['plasticonDigitalUser']['id']; ?>">Save</button>

                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['addOffer'] == 1) { ?>
                                <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addInquiry" tabindex="">
                                    <div class="modal-dialog" style="margin-top:10px;min-width:80%;">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h4 class="modal-title">Add inquiry</h4>
                                                <button type="button" class="close" data-dismiss="modal">&times;</button>
                                            </div>
                                            <form method="POST" enctype="multipart/form-data">
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <div class="col-lg-3">
                                                            <h4>General</h4>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col">
                                                            <label for="name">Quote Type (QT): *<br>
                                                                <select class="form-control form-100" name="OT" required>
                                                                    <?= Index\Inquiry\Modal\QuoteType::show() ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label style="width:200px;">Project:<br>
                                                                <select id='project' name="project" class="form-control select2">
                                                                    <?php selectProjects(); ?>
                                                                </select>
                                                                <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" data-dismiss="modal" data-toggle="modal" data-target="#addProject"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label for="name">Description: *<br>
                                                                <input class="form-control form-100" type="text" name="scope" placeholder="Description" required></label>
                                                        </div>
                                                        <div class="col">
                                                            <label style="width:200px;">Sales company: *<br>
                                                                <?php
                                                                $companies_for_select = getCompaniesDataForSelect();
                                                                $default_sales_company = $_SESSION['plasticonDigitalUser']['crm']['default_sales_company'];
                                                                $user_company = $_SESSION['plasticonDigitalUser']['company'];
                                                                ?>
                                                                <select id='company' name="company" class="form-control select2" required>
                                                                    <option value="" selected>Sales company</option>
                                                                    <?php
                                                                    foreach ($companies_for_select as $short => $name) {

                                                                        $selected = '';

                                                                        if ($default_sales_company === $short) {
                                                                            $selected = 'selected';
                                                                        } elseif (empty($default_sales_company) && $user_company === $short) {
                                                                            $selected = 'selected';
                                                                        }

                                                                        echo "<option value='" . $short . "' " . $selected . ">" . $name . "</option>";
                                                                    }
                                                                    ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col">
                                                            <label for="name">Internal number:<br>
                                                                <input class="form-control form-100" type="text" name="internalNumber" placeholder="Internal number"></label>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-6 borderRight">
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <h4>Reseller</h4>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Inquiry number:<br>
                                                                        <input type="text" name="fillInquiryNo" placeholder="Inquiry number" class="form-control form-100 client">
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Company:<br>
                                                                        <select class="form-control select2-reseller client" name="client" required onchange="getCities($(this).val(), $('#clientLocation')); $('#addConBtn').prop('disabled', false); $('.clientEnd').prop('required', false)">
                                                                        </select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" data-dismiss="modal" data-toggle="modal" data-target="#addClient"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Purchaser:<br>
                                                                        <select class="form-control select2 client" id="contactPurchase" name="contactPurchase" disabled required onchange="$('#contactTechnican').attr('required', false)">
                                                                        </select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" id="addConBtn" data-dismiss="modal" onClick="$('#ifend').val('0');" data-toggle="modal" data-target="#addContact" disabled><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Location:<br>
                                                                        <select class="form-control select2 client" id="clientLocation" name="clientLocation" disabled required onchange="getContacts($(this).val()); $('#addContactClientId').val($('#clientLocation').val())">
                                                                        </select></label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Technican:<br>
                                                                        <select class="form-control select2 client" id="contactTechnican" name="contactTechnican" disabled required onchange="$('#contactPurchase').attr('required', false)">
                                                                        </select></label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-lg-6">
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <h4>End client</h4>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Inquiry number:<br>
                                                                        <input type="text" name="fillInquiryNoEnd" placeholder="Inquiry number" class="form-control form-100 clientEnd">
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Company:<br>
                                                                        <select class="form-control select2-endclient clientEnd" name="clientEnd" required onchange="getCities($(this).val(), $('#endClientLocation')); $('#addEndConBtn').prop('disabled', false); $('.client').prop('required', false)">
                                                                        </select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" data-dismiss="modal" data-toggle="modal" data-target="#addClient"><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Purchaser:<br>
                                                                        <select class="form-control select2 clientEnd" id="contactPurchaseEnd" name="contactPurchaseEnd" disabled required onchange="$('#contactTechnicanEnd').attr('required', false)">
                                                                        </select>
                                                                        <span class="position-absolute" style="line-height:34px;">&nbsp;<button class="btn btn-primary" id="addEndConBtn" data-dismiss="modal" onClick="$('#ifend').val('1');" data-toggle="modal" data-target="#addContact" disabled><i style="font-size:20px;padding-top:1px;" class="fas fa-plus"></i></button></span>
                                                                    </label>
                                                                </div>
                                                            </div>
                                                            <div class="row justify-content-center">
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Location:<br>
                                                                        <select class="form-control select2 clientEnd" id="endClientLocation" name="endClientLocation" disabled required onchange="getContactsEnd($(this).val()); $('#addContactClientId').val($('#endClientLocation').val())">
                                                                        </select></label>
                                                                </div>
                                                                <div class="col-lg-6">
                                                                    <label for="name" style="width:200px;">Technican:<br>
                                                                        <select class="form-control select2 clientEnd" id="contactTechnicanEnd" name="contactTechnicanEnd" disabled required onchange="$('#contactPurchaseEnd').attr('required', false)">
                                                                        </select></label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Inquiry date: *<br>
                                                                <input class="form-control form-100" type="date" name="inquiry" value="<?php echo date('Y-m-d'); ?>" required></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Requested offer date: *<br>
                                                                <input class="form-control form-100" type="date" name="request" value="<?php echo date('Y-m-d', strtotime(date('Y-m-d') . ' +3 day')); ?>" required></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Requested order date: *<br>
                                                                <input class="form-control form-100" type="date" name="requestedOrderDate" required></label>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Next follow-up date: *<br>
                                                                <input class="form-control form-100" type="date" name="inqFollowUpDate" required></label>
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Responsibility (R): *<br>
                                                                <select id="resp" name="v" class="form-control select2" required>
                                                                    <?php selectVs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Inside Sales (IS): *<br>
                                                                <select id="iSales" name="id" class="form-control select2" required>
                                                                    <?php selectIDs(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label style="width:200px;">Follow up: *<br>
                                                                <select id="fUp" name="followUp" class="form-control select2" required>
                                                                    <?php listFollowUpUsersAdd(); ?>
                                                                </select>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-3">
                                                            <label for="name">Order Value Est. (OrVE) k&euro;: *<br>
                                                                <input onfocusout="checkKiloEuro(this);" class="form-control form-100" step="0.01" type="number" name="ove" placeholder="OVE" required></label>
                                                        </div>
                                                    </div>
                                                    <div class="row justify-content-center">* - Fields required.</div>
                                                </div>
                                                <div class="modal-footer">
                                                    <input type="submit" name="save" onclick="redBorder()" value="Save" class="btn btn-primary form-100">
                                                    <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addClient">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Adding new client</h4>
                                            <button type="button" class="close" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">&times;</button>
                                        </div>
                                        <form method="POST" onsubmit="saveClient(); return false">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Client name: *<br>
                                                        <input type="text" placeholder="Client name" class="form-control form-100" id="clientLongName" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Enterprise: *<br>
                                                        <input type="text" placeholder="Enterprise" class="form-control form-100" id="enterprise" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="example1" style="width:200px;">Country: *<br>
                                                        <select class="form-control select2" id="country" required>
                                                            <?php listCountries(); ?>
                                                        </select></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Zip: *<br>
                                                        <input class="form-control form-100" type="text" id="zip" placeholder="Zip" required value=""></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">City: *<br>
                                                        <input class="form-control form-100" type="text" id="city" placeholder="City" required value=""></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Address:<br>
                                                        <input class="form-control form-100" type="text" id="adres" placeholder="Address"></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="example1" style="width:200px;">Category: *<br>
                                                        <select class="form-control select2" id="category" id="category" required>
                                                            <option value="" disabled selected>Select</option>
                                                            <option value="A+">A+</option>
                                                            <option value="B+">B+</option>
                                                            <option value="A-">A-</option>
                                                            <option value="B-">B-</option>
                                                            <option value="New">New</option>
                                                        </select></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="example1" style="width:200px;">Type: *<br>
                                                        <select class="form-control select2" id="type" id="type" required>
                                                            <option value="" disabled selected>Select</option>
                                                            <option value="R">Reseller</option>
                                                            <option value="I">Intercompany</option>
                                                            <option value="E">End client</option>
                                                        </select></label>
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" onclick="redBorder();" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addContact">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Adding new contact person</h4>
                                            <button type="button" class="close" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">&times;</button>
                                        </div>
                                        <form method="POST" onsubmit="saveContact(); return false">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Gender: *<br>
                                                        <select name="contactGender" id="contactGender" class="select2 form-control" required>
                                                            <option value="" selected disabled>Select</option>
                                                            <option value="Male">Male</option>
                                                            <option value="Female">Female</option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Name: *<br>
                                                        <input type="text" placeholder="Name" class="form-control form-100" id="contactName" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Surname: *<br>
                                                        <input type="text" placeholder="Surname" class="form-control form-100" id="contactSurname" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Adress e-mail: *<br>
                                                        <input type="email" placeholder="Adress e-mail" class="form-control form-100" id="contactEmail" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Phone number 1: *<br>
                                                        <input type="text" placeholder="Phone number 1" class="form-control form-100" id="contactPhone1" required>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Phone number 2:<br>
                                                        <input type="text" placeholder="Phone number 2" class="form-control form-100" id="contactPhone2">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Position:<br>
                                                        <input type="text" placeholder="Position" class="form-control form-100" id="contactPosition">
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="number" id="ifend" class="hidden">
                                                <input type="number" id="addContactClientId" class="hidden">
                                                <input type="submit" onclick="redBorder()" name="addContactPerson" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="addProject">
                                <div class="modal-dialog" style="min-width:700px;">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Add project</h4>
                                            <button type="button" class="close" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">&times;</button>
                                        </div>
                                        <form method="POST" onsubmit="saveProject(); return false">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label for="name">Project name: *<br>
                                                        <input class="form-control form-100" type="text" id="projectNameNew" placeholder="Project name" required></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name">Plant location: *<br>
                                                        <input class="form-control form-100" type="text" id="plantLocation" placeholder="Plant location" required></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Plant country: *<br>
                                                        <select id="plantCountry" class="form-control select2" required>
                                                            <?php listCountriesOffer(); ?>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Description (ENG): *<br>
                                                        <textarea class="form-control form-100" id="description" style="height:100px;" required></textarea>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Project status:<br>
                                                        <select id="status" class="form-control select2">
                                                            <option value="">Select status</option>
                                                            <option value="Lead">Lead</option>
                                                            <option value="Running">Running</option>
                                                            <option value="In production">In production</option>
                                                            <option value="Finalized">Finalized</option>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">End client: *<br>
                                                        <select class="form-control select2-endclient" id="clientEnd" name="clientEnd" onchange="getCities($(this).val(), $('#endClient')); $('.client').prop('required', false)">
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Location: *<br>
                                                        <select class="form-control select2" id="endClient" name="endClientLocation" disabled onchange="$('#addContactClientId').val($('#endClientLocation').val())">
                                                        </select></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">comments (ENG):<br>
                                                        <textarea class="form-control form-100" id="comment" style="height:100px;"></textarea>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" onclick="redBorder()" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal" data-toggle="modal" data-target="#addInquiry">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="deleteOffer">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Delete Quote</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Quote: *<br>
                                                        <select name="offToDel" class="select2-ajax form-control" onchange="$('#delOfNoo').html($(this).val()); $('#delHide').removeClass('hidden')" required>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center hidden" id="delHide">
                                                    Are you sure to delete Quote no&nbsp;<strong><span id="delOfNoo"></span></strong>?
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" name="deleteOffer" value="Yes, delete" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No, close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" data-backdrop="static" data-keyboard="false" id="duplicateOffer">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Duplicate offer</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label style="width:200px;">Offer: *<br>
                                                        <select name="offToDUplicate" class="select2-ajax form-control" required>
                                                        </select>
                                                    </label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    This will copy selected quote. New quote will be created, with new quote number, without dates, contacts and calc. person.
                                                </div>
                                                <div class="row justify-content-center">* - Fields required.</div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" name="duplicateOfferNow" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" id="saveStateModal" tabindex="">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Add current filters state</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST" onsubmit="saveFilters('CRM-offers-management'); return false;">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Filters group name: *<br>
                                                        <input class="form-control form-200" type="text" id="saveStateName" placeholder="Name" required></label>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <h4>or</h4>
                                                </div>
                                                <div class="row justify-content-center">
                                                    <button type="button" class="btn btn-primary form-100" data-dismiss="modal" data-toggle="modal" data-target="#overwriteState" onclick="$('#overwriteStateName').val($('#stateSelect').val());">Override selected</button>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" name="save" onclick="redBorder()" value="Add" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" id="overwriteState" tabindex="">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Override current filters state</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <form method="POST" onsubmit="overwriteFilters('CRM-offers-management'); return false;">
                                            <div class="modal-body">
                                                <div class="row justify-content-center">
                                                    <label for="name" style="width:200px;">Filters group name: *<br>
                                                        <input class="form-control form-200" type="text" id="overwriteStateName" placeholder="Name" required></label>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <input type="submit" name="save" onclick="redBorder()" value="Save" class="btn btn-primary form-100">
                                                <button type="button" class="btn btn-danger form-100" data-dismiss="modal">Close</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="modal fade" id="delState" tabindex="">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h4 class="modal-title">Are you sure you want do delete current view?</h4>
                                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                                        </div>
                                        <div class="modal-footer">
                                            <input type="button" name="save" onclick="removeState('CRM-offers-management');" value="Yes" data-dismiss="modal" class="btn btn-primary form-100">
                                            <button type="button" class="btn btn-danger form-100" data-dismiss="modal">No</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div id="accordion" style="margin:0;">
                                <div style="padding: 1px 0px 0px 0px;">
                                    <div class="card" style="margin-bottom: 1px;border-radius:0;">

                                        <div class="card-header" style="margin-bottom:0;padding: 5px 10px 5px 20px;">
                                            <div class="row position-relative">
                                                <div class="text-center">
                                                    <button id="getFilters" title="Expand filters" data-toggle="collapse" href="#collapseTwo" style="padding:2px 9px; margin-right:10px;width:38px;height:38px;" class="btn btn-primary"><i style="font-size:22px;padding-top:4px;" class="fas fa-angle-down"></i></button>
                                                </div>
                                                <div class="col has-search"><span class="fa fa-search form-control-feedback"></span>
                                                    <form class="reset-box mobile-margin-top">
                                                        <input class="form-control searchBox" placeholder="Search" id="mySearchBox">
                                                        <button type="reset" class="reset-button" title="Clear"></button>
                                                    </form>
                                                </div>
                                                <div class="filterDots">
                                                    <label class="checkbox-container" title="Next contact date is fine and there are no other problems.">
                                                        <input type="checkbox" class="custom-checkbox-green filterCheckbox" name="statusGreen" id="statusGreen" value="1">
                                                        <span class="checkbox-checkmark-green"></span>
                                                    </label>
                                                    <label class="checkbox-container" title="The date of the next contact has passed. Please contact with client.">
                                                        <input type="checkbox" class="custom-checkbox-orange filterCheckbox" name="statusYellow" id="statusYellow" value="1">
                                                        <span class="checkbox-checkmark-orange"></span>
                                                    </label>
                                                    <label class="checkbox-container" title="The offer date has passed by at least 3 days. First contact with client is needed.">
                                                        <input type="checkbox" class="custom-checkbox-red filterCheckbox" name="statusRed" id="statusRed" value="1">
                                                        <span class="checkbox-checkmark-red"></span>
                                                    </label>
                                                    <label class="checkbox-container" title="Quote is beeing processed. There is no quote value.">
                                                        <input type="checkbox" class="custom-checkbox-blue filterCheckbox" name="statusBlue" id="statusBlue" value="1">
                                                        <span class="checkbox-checkmark-blue"></span>
                                                    </label>
                                                    <label class="checkbox-container" title="This quote has been changed into an order so there is no need to contact with client anymore.">
                                                        <input type="checkbox" class="custom-checkbox-grey filterCheckbox" name="statusGrey" id="statusGrey" value="1">
                                                        <span class="checkbox-checkmark-grey"></span>
                                                    </label>
                                                </div>
                                                <!-- <div class="col-lg-2 text-center">
                                                        <label class="checkbox-container" title="Next contact date is fine and there are no other problems.">
                                                                <input type="checkbox" class="custom-checkbox-green filterCheckbox" name="statusGreen" id="statusGreen" value="1">
                                                                <span class="checkbox-checkmark-green"></span>
                                                        </label>
                                                        <label class="checkbox-container" title="The date of the next contact has passed. Please contact with client.">
                                                                <input type="checkbox" class="custom-checkbox-orange filterCheckbox" name="statusYellow" id="statusYellow" value="1">
                                                                <span class="checkbox-checkmark-orange"></span>
                                                        </label>
                                                        <label class="checkbox-container" title="The offer date has passed by at least 3 days. First contact with client is needed.">
                                                                <input type="checkbox" class="custom-checkbox-red filterCheckbox" name="statusRed" id="statusRed" value="1">
                                                                <span class="checkbox-checkmark-red"></span>
                                                        </label>
                                                        <label class="checkbox-container" title="Quote is beeing processed. There is no quote value.">
                                                                <input type="checkbox" class="custom-checkbox-blue filterCheckbox" name="statusBlue" id="statusBlue" value="1">
                                                                <span class="checkbox-checkmark-blue"></span>
                                                        </label>
                                                        <label class="checkbox-container" title="This quote has been changed into an order so there is no need to contact with client anymore.">
                                                                <input type="checkbox" class="custom-checkbox-grey filterCheckbox" name="statusGrey" id="statusGrey" value="1">
                                                                <span class="checkbox-checkmark-grey"></span>
                                                        </label>
                                                </div> -->
                                                <div class="col-lg-1 text-center"><button class="form-control btn btn-primary scaleBtnMobile" id="checkAll">Select all</button></div>
                                                <div class="col-lg-1">
                                                    <button data-toggle="modal" data-target="#saveStateModal" class="btn btn-primary form-control scaleBtnMobile">Save view</button>
                                                </div>
                                                <div class="col-lg-2">
                                                    <select id="stateSelect" onchange="changeState($('#stateSelect').val(), 0);" class="form-control"></select>
                                                </div>
                                                <div class="col-lg-1">
                                                    <button data-toggle="modal" data-target="#delState" class="btn btn-danger form-control scaleBtnMobile">Remove view</button>
                                                </div>
                                                <div class="col-lg-1 text-center">
                                                    <button class="form-control btn btn-danger" id="filterReset"><img src="assets/images/reset-filters.png" style="width:25px;margin-right:5px;">Reset view<span id="filtersCountSpan"></span></button>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="collapseTwo" class="collapse" data-parent="#accordion" style="flex-direction:column">
                                            Click on filter name to hide/show the column in table. If the name is in bold, the column becomes visible.
                                            <div class="RAM">
                                                <div class="filterContainer general">
                                                    <div class="filterContainer_title">
                                                        General
                                                    </div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="0" class="chbox toggle-vis" disabled>Status</label>
                                                            <span class="filterSpanNonStyle">
                                                                <select class="form-control select2 filterStyle" placeholder="Status" data-placeholder="Status" name="axFilter[]" id="axFilter" multiple="multiple">
                                                                    <option value="open">Open</option>
                                                                    <option value="Order">Order</option>
                                                                    <option value="Terminated">Terminated</option>
                                                                    <option value="Lost">Lost</option>
                                                                </select>
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="1" class="chbox toggle-vis">Reason</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Reason" class="form-control select2 filterStyle" name="reasonFilter[]" id="reasonFilter" multiple="multiple">
                                                                    <?php reasonFilter("", "notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="2" class="chbox toggle-vis">Q. Type</label><!--Quote Type-->
                                                            <span class="filterSpanNonStyle"><select placeholder="Quote type" class="form-control select2 filterStyle" name="otFilter[]" id="otFilter" multiple="multiple">
                                                                    <!-- <option value="all" selected>Show all</option> -->
                                                                    <option value="A">A</option>
                                                                    <option value="B">B</option>
                                                                    <option value="E">E</option>
                                                                    <option value="Q">Q</option>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="3" class="chbox toggle-vis">Q. No.</label><!--Quote Number-->
                                                            <span class="filterSpanNonStyle"><input type="text" class="form-control filterStyle" style="height:28px;" name="offerNoFilter" id="offerNoFilter" placeholder="Quote number"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="4" class="chbox toggle-vis">Int. No.</label><!--Internal Number-->
                                                            <span class="filterSpanNonStyle"><input type="number" class="form-control filterStyle" style="height:28px;" name="oldOfferNoFilter" id="oldOfferNoFilter" placeholder="Old quote number"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="5" class="chbox toggle-vis">Project</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Project name" class="form-control select2 filterStyle" name="projectFilter[]" id="projectFilter" multiple="multiple">
                                                                    <!-- <option value="all" selected>Show all</option> -->
                                                                    <option value="0">Non projects offers</option>
                                                                    <?php listProjects(); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="5" class="chbox toggle-vis">Project No.</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Project number" class="form-control select2 filterStyle" name="projectNoFilter[]" id="projectNoFilter" multiple="multiple">
                                                                    <option value="0">Non projects offers</option>
                                                                    <?php listProjectsNo(); ?>
                                                                </select></span>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="filterContainer reseller">
                                                    <div class="filterContainer_title">Reseller</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="7" class="chbox toggle-vis">Reseller</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Reseller name" class="form-control select2-reseller-filter filterStyle" name="clientFilter" id="clientFilter"></span>
                                                            <option value='' selected>--Reseller--</option>
                                                            </select>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="12" class="chbox toggle-vis"> Country</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Country" class="form-control select2 filterStyle" name="countryFilter[]" id="countryFilter" multiple="multiple">
                                                                    <!-- <option value="all" selected>Show all</option> -->
                                                                    <?php listCountriesFilters(); ?>
                                                                </select></span>
                                                        </div>
                                                        <!-- <div class="filterContainer_body-row">
                                                                <label class="filterLabel"><input type="checkbox" data-column="10" class="chbox toggle-vis"> Location</label>
                                                                <span class="filterSpanNonStyle"><select class="form-control select2 filterStyle" id="clientLocationFilter" name="clientLocationFilter" disabled>
                                                                        <option value='' selected>--Location--</option></label>
                                                                </select></span>
                                                        </div> -->
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="10" class="chbox toggle-vis"> City</label>
                                                            <span class="filterSpanNonStyle"><input type="text" class="form-control filterStyle" style="height:28px;" name="clientCityFilter" id="clientCityFilter" placeholder="City"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="9" class="chbox toggle-vis"> Zip</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Zip code" type="text" class="form-control filterStyle" style="height:28px;" name="clientZipFilter" id="clientZipFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="11" class="chbox toggle-vis"> Industry</label>
                                                            <span class="filterSpanNonStyle">
                                                                <select placeholder="Industry" class="form-control select2 filterStyle" name="marketRFilter" id="marketRFilter" multiple="multiple">
                                                                    <?= Index\Filter\Industry::show() ?>
                                                                </select>
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="8" class="chbox toggle-vis"> Inquiry no:</label>
                                                            <span class="filterSpanNonStyle"><input type="text" class="form-control filterStyle" style="height:28px;" name="inquiryNoFilter" id="inquiryNoFilter" placeholder="Inquiry no"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Client categories"><input type="checkbox" class="chbox" disabled>Category</label>
                                                            <div class="select-size">
                                                                <input type="checkbox" class="filterCheckbox" name="clientA" id="clientA" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientB" id="clientB" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientC" id="clientC" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientD" id="clientD" value="1" />

                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientA">A+</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientC">A-</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientB">B+</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientD">B-</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer endclient">
                                                    <div class="filterContainer_title">End client</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="13" class="chbox toggle-vis">E. client</label><!--End client-->
                                                            <span class="filterSpanNonStyle"><select placeholder="End client" class="form-control select2-endclient-filter filterStyle" name="endClientFilter" id="endClientFilter"></span>
                                                            <option value='' selected>--End client--</option>
                                                            </select>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="18" class="chbox toggle-vis">Country</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Country" class="form-control select2 filterStyle" name="countryEFilter[]" id="countryEFilter" multiple="multiple">
                                                                    <!-- <option value="all" selected>Show all</option> -->
                                                                    <?php listCountriesFilters(); ?>
                                                                </select></span>
                                                        </div>
                                                        <!-- <div class="filterContainer_body-row">
                                                                <label class="filterLabel"><input type="checkbox" data-column="16" class="chbox toggle-vis"> Location</label>
                                                                <span class="filterSpanNonStyle"><select class="form-control select2 filterStyle" id="endClientLocationFilter" name="endClientLocationFilter" disabled>
                                                                        <option value='' selected>--Location--</option></label>
                                                                </select></span>
                                                        </div> -->
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="16" class="chbox toggle-vis">City</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="City" type="text" class="form-control filterStyle" style="height:28px;" name="endClientCityFilter" id="endClientCityFilter" placeholder="City"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="15" class="chbox toggle-vis">Zip</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Zip code" type="text" class="form-control filterStyle" style="height:28px;" name="endClientZipFilter" id="endClientZipFilter" placeholder="Zip"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="17" class="chbox toggle-vis">Industry</label>
                                                            <span class="filterSpanNonStyle">
                                                                <select placeholder="Industry" class="form-control select2 filterStyle" name="marketEFilter" id="marketEFilter" multiple="multiple">
                                                                    <?= Index\Filter\Industry::show() ?>
                                                                </select>
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="14" class="chbox toggle-vis"> Inquiry no:</label>
                                                            <span class="filterSpanNonStyle"><input type="text" class="form-control filterStyle" style="height:28px;" name="endClientInquiryNoFilter" id="endClientInquiryNoFilter" placeholder="Inquiry no"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Client categories"><input type="checkbox" class="chbox" disabled>Category</label>
                                                            <div class="select-size">
                                                                <input type="checkbox" class="filterCheckbox" name="clientAEnd" id="clientAEnd" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientBEnd" id="clientBEnd" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientCEnd" id="clientCEnd" value="1" />
                                                                <input type="checkbox" class="filterCheckbox" name="clientDEnd" id="clientDEnd" value="1" />

                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientAEnd">A+</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientCEnd">A-</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientBEnd">B+</label>
                                                                <label style="margin-bottom:0;" class="checkbox-label" for="clientDEnd">B-</label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer pricing">
                                                    <div class="filterContainer_title">Pricing</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Order Value Estimated"><input type="checkbox" data-column="41" class="chbox toggle-vis">OVE</label><!--Order Value Estimated-->
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" class="form-control filterControl filterStyle" name="minOVE" id="minOVE">
                                                                <input type="number" placeholder="Max" class="form-control filterControl filterStyle" name="maxOVE" id="maxOVE">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Quote Value"><input type="checkbox" data-column="42" class="chbox toggle-vis">QV</label><!--Quote Value-->
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" class="form-control filterControl filterStyle" name="minOFV" id="minOFV">
                                                                <input type="number" placeholder="Max" class="form-control filterControl filterStyle" name="maxOFV" id="maxOFV">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="43" class="chbox toggle-vis">GO</label><!--Internal Number-->
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minGO" id="minGO">
                                                                <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxGO" id="maxGO">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="44" class="chbox toggle-vis">GET</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minGET" id="minGET">
                                                                <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxGET" id="maxGET">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="45" class="chbox toggle-vis">OVEgg</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" class="form-control filterControl filterStyle" name="minOVgg" id="minOVgg">
                                                                <input type="number" placeholder="Max" class="form-control filterControl filterStyle" name="maxOVgg" id="maxOVgg">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="53" class="chbox toggle-vis">OV</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="number" placeholder="Min" class="form-control filterControl filterStyle" name="minORV" id="minORV">
                                                                <input type="number" placeholder="Max" class="form-control filterControl filterStyle" name="maxORV" id="maxORV">
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer timeline">
                                                    <div class="filterContainer_title">Timeline</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="33" class="chbox toggle-vis">Inquiry</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl filterStyle filterContainer_date-start" name="startInquiryDateFilter" id="startInquiryDateFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl filterStyle filterContainer_date-end" name="endInquiryDateFilter" id="endInquiryDateFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="34" class="chbox toggle-vis">Req.Quo.</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl filterStyle" name="startORDFilter" id="startORDFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl filterStyle" name="endORDFilter" id="endORDFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="35" class="chbox toggle-vis">Quote</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl filterStyle" name="startOfferFilter" id="startOfferFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl filterStyle" name="endOfferFilter" id="endOfferFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="36" class="chbox toggle-vis">Req. Ord.</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl filterStyle" name="startReqOrderDateFilter" id="startReqOrderDateFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl filterStyle" name="endReqOrderDateFilter" id="endReqOrderDateFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="50" class="chbox toggle-vis">Order</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl" name="startOrderDateFilter" id="startOrderDateFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl" name="endOrderDateFilter" id="endOrderDateFilter">
                                                            </span>
                                                        </div>
                                                        <!-- <div class="filterContainer_body-row hideFont">-</div> -->
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="51" class="chbox toggle-vis">Req. Deli.</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl" name="startDRDFilter" id="startDRDFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl" name="endDRDFilter" id="endDRDFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="52" class="chbox toggle-vis">Delivery</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl" name="startDeliveryFilter" id="startDeliveryFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl" name="endDeliveryFilter" id="endDeliveryFilter">
                                                            </span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="37" class="chbox toggle-vis">Next Con.</label>
                                                            <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                <input type="date" placeholder="Start" class="form-control filterControl filterStyle" name="startNCDFilter" id="startNCDFilter">
                                                                <input type="date" placeholder="End" class="form-control filterControl filterStyle" name="endNCDFilter" id="endNCDFilter">
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer offer">
                                                    <div class="filterContainer_title">Offer</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == '1') { ?><label class="filterLabel">
                                                                    <input type="checkbox" data-column="23" class="chbox toggle-vis">PC</label>
                                                                <span class="filterSpanNonStyle"><select placeholder="Production company" class="form-control select2 filterStyle" name="prodFilter[]" id="prodFilter" multiple="multiple">
                                                                        <!-- <option value="all" selected>Show all</option> -->
                                                                        <?php echo getCompanies(); ?>
                                                                    </select></span><?php } ?>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="19" class="chbox toggle-vis">Descr.</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Description" type="text" class="form-control filterStyle" style="height:28px;" name="scopeFilter" id="scopeFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="20" class="chbox toggle-vis">Comm.</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Comments" type="text" class="form-control filterStyle" style="height:28px;" name="commentsFilter" id="commentsFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="21" class="chbox toggle-vis">Seg.</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Segment" class="form-control select2 filterStyle" name="segmentFilter[]" id="segmentFilter" multiple="multiple">
                                                                    <?= Index\Filter\Segment::show() ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Amount of Components"><input type="checkbox" data-column="22" class="chbox toggle-vis">ASA</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Amount of Sales" type="number" class="form-control filterStyle" style="height:28px;" name="aocFilter" id="aocFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                                <label class="filterLabel" title="Production hours project">
                                                                    <input type="checkbox" data-column="24" class="chbox toggle-vis">EPHP</label>
                                                                <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                    <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minWHP" id="minWHP">
                                                                    <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxWHP" id="maxWHP">
                                                                </span>
                                                            <?php } ?>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                                <label class="filterLabel" title="Production hours service">
                                                                    <input type="checkbox" data-column="25" class="chbox toggle-vis">EPHS</label>
                                                                <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                    <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minWHS" id="minWHS">
                                                                    <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxWHS" id="maxWHS">
                                                                </span>
                                                            <?php } ?>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="40" class="chbox toggle-vis">Last Con.</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Last contact" type="text" class="form-control filterStyle" style="height:28px;" name="lastContactFilter" id="lastContactFilter"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer sales">
                                                    <div class="filterContainer_title">Sales</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == '1') { ?><label class="filterLabel">
                                                                    <input type="checkbox" data-column="26" class="chbox toggle-vis">SC</label>
                                                                <span class="filterSpanNonStyle"><select placeholder="Sales company" class="form-control select2 filterStyle" name="cmpFilter[]" id="cmpFilter" multiple="multiple">
                                                                        <!-- <option value="all" selected>Show all</option> -->
                                                                        <?php echo getCompanies(); ?>
                                                                    </select></span><?php } ?>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Responsibility"><input type="checkbox" data-column="27" class="chbox toggle-vis">Respons.</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Responsible person" class="form-control select2 filterStyle" name="vFilter[]" id="vFilter" multiple="multiple">
                                                                    <?php selectV("", "notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Inside Sales"><input type="checkbox" data-column="28" class="chbox toggle-vis">Ins. Sales</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Inside Sales" class="form-control select2 filterStyle" name="isFilter[]" id="isFilter" multiple="multiple">
                                                                    <?php selectIDsF("", "notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Calculating person"><input type="checkbox" data-column="29" class="chbox toggle-vis">Calc. pers.</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Calculating person" class="form-control select2 filterStyle" name="cpFilter[]" id="cpFilter" multiple="multiple">
                                                                    <?php listUsersFilters("notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Team Leader"><input type="checkbox" data-column="30" class="chbox toggle-vis">TL</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Team leader" class="form-control select2 filterStyle" name="tlFilter[]" id="tlFilter" multiple="multiple">
                                                                    <?php selectTLs("", "notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Key Account Manager"><input type="checkbox" data-column="31" class="chbox toggle-vis">KAM</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Key Account Manager" class="form-control select2 filterStyle" name="kamFilter[]" id="kamFilter" multiple="multiple">
                                                                    <?php selectKAMs("notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel" title="Area Sales Manager"><input type="checkbox" data-column="32" class="chbox toggle-vis">ASM</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Area Sales Manager" class="form-control select2 filterStyle" name="asmFilter[]" id="asmFilter" multiple="multiple">
                                                                    <?php selectASMs("notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="39" class="chbox toggle-vis">FU pers.</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Follow up person" class="form-control select2 filterStyle" name="followupFilter[]" id="followupFilter" multiple="multiple">
                                                                    <?php listFollowUpUsersFilters("notall"); ?>
                                                                </select></span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="filterContainer order">
                                                    <div class="filterContainer_title">Order</div>
                                                    <div class="filterContainer_body">
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="48" class="chbox toggle-vis">OC</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Order company" class="form-control select2 filterStyle" name="ocFilter[]" id="ocFilter" multiple="multiple">
                                                                    <!-- <option value="all" selected>Show all</option> -->
                                                                    <?php echo getCompanies(); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row hideFont">-</div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="49" class="chbox toggle-vis">PM</label>
                                                            <span class="filterSpanNonStyle"><select placeholder="Project manager" class="form-control select2 filterStyle" name="pmFilter[]" id="pmFilter" multiple="multiple">
                                                                    <?php echo listPMsFilter("notall"); ?>
                                                                </select></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="46" class="chbox toggle-vis">SON</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Sales order number" type="text" class="form-control filterStyle" style="height:28px;" name="orderNoFilter" id="orderNoFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <label class="filterLabel"><input type="checkbox" data-column="47" class="chbox toggle-vis">PON</label>
                                                            <span class="filterSpanNonStyle"><input placeholder="Production order number" type="text" class="form-control filterStyle" style="height:28px;" name="prodOrderNoFilter" id="prodOrderNoFilter"></span>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                                <label class="filterLabel"><input type="checkbox" data-column="54" class="chbox toggle-vis">CM (k&euro;)</label>
                                                                <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                    <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minOFVCM" id="minOFVCM">
                                                                    <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxOFVCM" id="maxOFVCM">
                                                                </span>
                                                            <?php } ?>
                                                        </div>
                                                        <div class="filterContainer_body-row">
                                                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['externalCompany'] == 0) { ?>
                                                                <label class="filterLabel"><input type="checkbox" data-column="55" class="chbox toggle-vis">CM%</label>
                                                                <span class="filterSpanNonStyle d-flex justify-content-between">
                                                                    <input type="number" placeholder="Min" min="0" max="100" class="form-control filterControl filterStyle" name="minORVCM" id="minORVCM">
                                                                    <input type="number" placeholder="Max" min="0" max="100" class="form-control filterControl filterStyle" name="maxORVCM" id="maxORVCM">
                                                                </span>
                                                            <?php } ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div style="padding: 0px 0px 1px 0px;">
                                    <div class="card" style="border-radius:0;">
                                        <div class="card-header" style="margin-bottom:0;padding: 5px 10px 5px 20px;">
                                            <div class="row">
                                                <div class="text-center">
                                                    <a href="<?php if ($_SESSION['plasticonDigitalUser']['crm']['statistics'] == 1) echo 'statistics.php';
                                                                else echo '#'; ?>" title="Statistics"><button title="Summary" class="btn btn-primary" style="padding:2px 9px; margin-right:10px;width:38px;height:38px;"><span style="font-size:24px;line-height:28px;"><strong>&Sigma;</strong></span></button></a>
                                                </div>
                                                <div class="col-lg-2 borderRight text-center" style="line-height:37px;">Quantity: <strong><span id="Qsummary"></span></strong></div>
                                                <div class="col-lg-2 borderRight text-center" style="line-height:37px;">OVE: <strong><span id="OVsummary"></span></strong></div>
                                                <div class="col-lg-2 borderRight text-center" style="line-height:37px;">OVEgg: <strong><span id="OVggsummary"></span></strong></div>
                                                <div class="col-lg-2 borderRight text-center" style="line-height:37px;">OV: <strong><span id="OValsummary"></span></strong></div>
                                                <div class="col-lg-3 borderRight text-center" style="line-height:37px;">
                                                    <div class="row justify-content-center">
                                                        <div class="col-lg-3 text-center">
                                                            GxG:
                                                        </div>
                                                        <div class="col-lg-9 text-center" style="padding-top:7px;">
                                                            <div class="progress" style="height:1.4rem;">
                                                                <div id="GxGprogress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"><span id="GxGsummary"></span></div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="alert"></div>
                                <?php

                                ?>

                                <?php

                                if (isset($_GET['products_export'])) {
                                    alertDanger("Error exporting products");
                                }

                                if (isset($_GET['no_permissions'])) {
                                    alertDanger("No permissions. Please contact the administrator.");
                                }

                                // Handle alerts when folder created with error
                                if (isset($_GET['folder_error'])) {
                                    $code = (int) $_GET['folder_error'];

                                    switch ($code) {
                                        case 1:
                                            alertDanger("Error creating offer folder.");
                                            break;
                                        case 2:
                                            alertDanger("Error creating company folders.");
                                            break;
                                        case 3:
                                            alertDanger("Error creating company subfolders.");
                                            break;
                                        default:
                                            alertDanger("Error creating folder.");
                                            break;
                                    }
                                }

                                if (isset($_GET['exists']) && $_GET['exists'] == 1) {
                                    $existing_id = getOfferId($_GET['offer']);


                                    alertDanger("Offer no already exists. Please check <a href='offer.php?id=" . $existing_id . "'>" . $_GET['offer'] . "</a>.");
                                }

                                if (isset($_GET['addedSuccess']))
                                    alertSuccess("Quote has been added.");

                                if (isset($_POST['deleteOffer'])) {
                                    $link = connect();
                                    $offer = $_POST['offToDel'];
                                    $link->query("DELETE FROM offers WHERE offerNo='$offer'");
                                    $link->query("DELETE FROM components WHERE offerNo='$offer'");
                                    $link->query("DELETE FROM stats WHERE offerNo='$offer'");
                                    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                    $ip = $_SERVER['REMOTE_ADDR'];
                                    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Deleted offer','Deleted offer/s number $offer','$ip')");
                                    alertSuccess("Quote " . $offer . " has been deleted.");
                                }
                                if (isset($_POST['duplicateOfferNow'])) {
                                    $link = connect();
                                    $offer = $_POST['offToDUplicate'];
                                    $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                    $ip = $_SERVER['REMOTE_ADDR'];
                                    $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Duplicated offer','Duplicated offer/s number $offer','$ip')");
                                    $newOffer = createOfferCopy($offer);

                                    $new_offer_id = (int) getOfferId($newOffer);

                                    if ($new_offer_id) {
                                        $folders_ok = createOfferCopyFolders($new_offer_id);
                                    }

                                    if (!$folders_ok) {
                                        alertDanger("Quote " . $newOffer . " has been added but folder creation failed.");
                                    }

                                    alertSuccess("Quote " . $newOffer . " has been added.");
                                }
                                if (isset($_POST['save'])) {
                                    if (!empty($_POST['OT']) && !empty($_POST['scope']) && !empty($_POST['id']) && !empty($_SESSION['plasticonDigitalUser']['company'])) {
                                        include('assets/libs/PHPMailer/mail.php');
                                        $lastOfferNo = lastOfferNo();
                                        if (date("m") >= 7) {
                                            $offerNoYear = substr($lastOfferNo, 0, 2);
                                            $sumThisYear = substr(date("Y") + 1, 2, 2);
                                            if ($offerNoYear != $sumThisYear)
                                                $offerNo = $sumThisYear . "700001";
                                            else {
                                                $offerNo = explode("-", $lastOfferNo);
                                                $offerNo = $offerNo[0] + 1;
                                            }
                                        } else {
                                            $offerNo = explode("-", $lastOfferNo);
                                            $offerNo = $offerNo[0] + 1;
                                        }

                                        $existing_id = getOfferId($offerNo);

                                        if ($existing_id) {
                                            header("Location: index.php?offer=$offerNo&exists=1");
                                            exit();
                                        }

                                        $link = connect();
                                        $OT = strip_tags($_POST['OT']);
                                        $client = strip_tags($_POST['clientLocation']);
                                        $scope = strip_tags($_POST['scope']);
                                        $ID = strip_tags($_POST['id']);
                                        $v = strip_tags($_POST['v']);
                                        $ove = strip_tags($_POST['ove']);
                                        $query = sprintf(
                                            "INSERT INTO `offers`(`offerNo`, `OT`, `scope`, OVE, oID, InID, V, InR, F, InF, projectId, projectName, company, inquiry, request, `requestedOrderDate`, client, purchase, technican, inquiryNo, finalClient, endclientContactPurchase, endclientContactTechnican, endclientInquiryNo, step, SOC, nextContactDate, oldOfferNo) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                            mysqli_real_escape_string($link, $offerNo),
                                            mysqli_real_escape_string($link, $OT),
                                            mysqli_real_escape_string($link, $scope),
                                            mysqli_real_escape_string($link, $ove),
                                            mysqli_real_escape_string($link, $ID),
                                            mysqli_real_escape_string($link, strip_tags(inicialy($ID))),
                                            mysqli_real_escape_string($link, $v),
                                            mysqli_real_escape_string($link, strip_tags(inicialy($v))),
                                            mysqli_real_escape_string($link, $_POST['followUp']),
                                            mysqli_real_escape_string($link, strip_tags(inicialy($_POST['followUp']))),
                                            mysqli_real_escape_string($link, strip_tags($_POST['project'])),
                                            mysqli_real_escape_string($link, strip_tags(getProjectName($_POST['project']))),
                                            mysqli_real_escape_string($link, $_POST['company']),
                                            mysqli_real_escape_string($link, $_POST['inquiry']),
                                            mysqli_real_escape_string($link, $_POST['request']),
                                            mysqli_real_escape_string($link, $_POST['requestedOrderDate']),
                                            mysqli_real_escape_string($link, $_POST['clientLocation']),
                                            mysqli_real_escape_string($link, $_POST['contactPurchase']),
                                            mysqli_real_escape_string($link, $_POST['contactTechnican']),
                                            mysqli_real_escape_string($link, $_POST['fillInquiryNo']),
                                            mysqli_real_escape_string($link, $_POST['endClientLocation']),
                                            mysqli_real_escape_string($link, $_POST['contactPurchaseEnd']),
                                            mysqli_real_escape_string($link, $_POST['contactTechnicanEnd']),
                                            mysqli_real_escape_string($link, $_POST['fillInquiryNoEnd']),
                                            mysqli_real_escape_string($link, strip_tags(1)),
                                            mysqli_real_escape_string($link, strip_tags(1)),
                                            mysqli_real_escape_string($link, strip_tags($_POST['inqFollowUpDate'])),
                                            mysqli_real_escape_string($link, $_POST['internalNumber'])
                                        );
                                        $link->query($query);
                                        $idOferty = $link->insert_id;
                                        $tresc = "
New quote has been added to offer list.<br>
Quote number: $offerNo <br>
Responsibility (R): " . getNameAndSurname($_POST['v']) . "<br>
Inside Sales (IS): " . getNameAndSurname($_POST['id']) . "<br><br>
Scope: " . $_POST['scope'] . "<br>
Reseller: " . getClientName($_POST['clientLocation']) . "<br>
City: " . getClientCity($_POST['clientLocation']) . "<br>
Reseller inquiry number: " . $_POST['fillInquiryNo'] . " <br><br>
End client: " . getClientName($_POST['endClientLocation']) . "<br>
City: " . getClientCity($_POST['endClientLocation']) . "<br>
End client inquiry number: " . $_POST['fillInquiryNoEnd'] . " <br>
										";
                                        echo "<div class='hidden'>";
                                        /* if($_POST['v']==$_POST['id'])
                                          {
                                          if(!empty(getUserEmail($_POST['v'])))
                                          {
                                          wyslijMaila(getUserEmail($_POST['v']), $tresc, 'New quote No '.$offerNo.' has been added.');
                                          }
                                          }
                                          else
                                          {
                                          if(!empty(getUserEmail($_POST['v'])))
                                          wyslijMaila(getUserEmail($_POST['v']), $tresc, 'New quote No '.$offerNo.' has been added.');
                                          if(!empty(getUserEmail($_POST['id'])))
                                          wyslijMaila(getUserEmail($_POST['id']), $tresc, 'New quote No '.$offerNo.' has been added.');
                                          } */
                                        echo "</div>";
                                        $query = sprintf(
                                            "INSERT INTO `components`(`offerNo`, `position`, `revision`, scope, counts) VALUES ('%s','%s','%s','%s','1')",
                                            mysqli_real_escape_string($link, $offerNo),
                                            mysqli_real_escape_string($link, 1),
                                            mysqli_real_escape_string($link, 0),
                                            mysqli_real_escape_string($link, $scope)
                                        );
                                        $idUserAction = $_SESSION['plasticonDigitalUser']['id'];
                                        $ip = $_SERVER['REMOTE_ADDR'];
                                        $link->query($query);
                                        $idCmp = $link->insert_id;
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new component','Added component with position 1 to quote number $offerNo','$ip')");
                                        $query = sprintf(
                                            "INSERT INTO `stats`(`offerNo`, idOferty, idCmp, scope, clientShortName, OV, `GO`, `GET`, `inquiryDate`, inR, inIS, company, orderDate, OT) VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s')",
                                            mysqli_real_escape_string($link, $offerNo),
                                            mysqli_real_escape_string($link, $idOferty),
                                            mysqli_real_escape_string($link, $idCmp),
                                            mysqli_real_escape_string($link, $scope),
                                            mysqli_real_escape_string($link, getClientName($_POST['clientLocation']) . " / " . getClientName($_POST['endClientLocation'])),
                                            mysqli_real_escape_string($link, $ove),
                                            mysqli_real_escape_string($link, 0),
                                            mysqli_real_escape_string($link, 0),
                                            mysqli_real_escape_string($link, $_POST['inquiry']),
                                            mysqli_real_escape_string($link, strip_tags(inicialy($v))),
                                            mysqli_real_escape_string($link, strip_tags(inicialy($ID))),
                                            mysqli_real_escape_string($link, $_POST['company']),
                                            mysqli_real_escape_string($link, $_POST['requestedOrderDate']),
                                            mysqli_real_escape_string($link, $OT)
                                        );
                                        $link->query($query);
                                        $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Added new offer','offerNo=" . $offerNo . ", OT=" . $OT . ", description=" . $scope . ", OVE=" . $ove . ", IS=" . $ID . ", InIS=" . inicialy($ID) . ", R=" . $v . ", InR=" . inicialy($v) . ", F=" . $_POST['followUp'] . ", InF=" . inicialy($_POST['followUp']) . ", projectId=" . $_POST['project'] . ", projectName=" . getProjectName($_POST['project']) . ", company=" . $_POST['company'] . ", inquiry=" . $_POST['inquiry'] . ", request=" . $_POST['request'] . ", requestedOrderDate=" . $_POST['requestedOrderDate'] . ", reseller=" . $_POST['clientLocation'] . ", purchase=" . $_POST['contactPurchase'] . ", technican=" . $_POST['contactTechnican'] . ", inquiryNo=" . $_POST['fillInquiryNo'] . ", endClient=" . $_POST['endClientLocation'] . ", endclientContactPurchase=" . $_POST['contactPurchaseEnd'] . ", endclientContactTechnican=" . $_POST['contactTechnicanEnd'] . ", endclientInquiryNo=" . $_POST['fillInquiryNoEnd'] . ", step=1, AoC=1','$ip')");


                                        $dirname = trim(cleanStr(str_replace("|", "", str_replace("/", "-", str_replace(":", "", str_replace("\\", "-", $offerNo . "_" . getClientName($_POST['clientLocation']) . "-" . getClientName($_POST['endClientLocation']) . "_" . $scope))))));

                                        $dirname = preg_replace('!\s+!', ' ', $dirname);

                                        // Create folders for offer
                                        $folder_error = false;
                                        $parent_folder_created = mkdir("../../offers/$dirname");

                                        if (!$parent_folder_created) {
                                            $folder_error = 1;
                                            $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating offer folder', 'Error creating offer folder for offerNo $offerNo', '$ip')");
                                        }

                                        if (!$folder_error) {
                                            $companies = ["PP", "PG", "PTN", "TP", "PT"];

                                            foreach ($companies as $com) {
                                                $company_folder_created = mkdir("../../offers/$dirname/$com");

                                                if (!$company_folder_created) {
                                                    $folder_error = 2;
                                                    $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating company folders', 'Error creating company $com folder for offerNo $offerNo', '$ip')");
                                                    continue;
                                                }

                                                $nested_folders = ["1 Inquiry", "2 Subsuplier", "3 Calculation", "4 Offer", "5 Order", "6 Others"];

                                                foreach ($nested_folders as $subfolder) {
                                                    $nested_folder_created = mkdir("../../offers/$dirname/$com/$subfolder");

                                                    if (!$nested_folder_created) {
                                                        $folder_error = 3;
                                                        $link->query("INSERT INTO `logs` (`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction', 'Error creating company subfolders', 'Error creating company $com subfolder $subfolder for offerNo $offerNo', '$ip')");
                                                        continue;
                                                    }
                                                }
                                            }
                                        }

                                        if ($parent_folder_created) {
                                            $link->query(sprintf(
                                                "UPDATE offers SET folder_link='%s' WHERE offerNo='%s'",
                                                mysqli_real_escape_string($link, $dirname),
                                                mysqli_real_escape_string($link, $offerNo)
                                            ));
                                            $link->query("INSERT INTO `logs`(`userId`, `action`, `details`, `ip`) VALUES ('$idUserAction','Created offer folder','Created offer folder $dirname','$ip')");
                                        }


                                        $link->close();

                                        countHitrates($client);

                                        $go_to = "Location: index.php?addedSuccess=1";


                                        if ($folder_error) {
                                            $go_to .= "&folder_error=$folder_error";
                                        }

                                        header($go_to);
                                    } else
                                    if (empty($_SESSION['plasticonDigitalUser']['company']))
                                        header("Location: login.php");
                                    else
                                        alertDanger("Fill all required fields!");
                                }
                                ?>
                                <div class="table-responsive">
                                    <table id="offers" class="table table-bordered table-hover display" style="margin:auto;width:100%;">
                                        <thead>
                                            <tr class="tableNoBorder">
                                                <th style="padding: 2px 9px 2px 9px;" title="Reason"></th>
                                                <th title="Reason"></th>
                                                <th title="Quote type"></th>
                                                <th title="Quote number"></th>
                                                <th title="Old quote number"></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th title="Reseller Inquiry"></th>
                                                <th title="Reseller ZIP"></th>
                                                <th title="Reseller location"></th>
                                                <th></th>
                                                <th></th>
                                                <th title="End client Inquiry"></th>
                                                <th title="End client ZIP"></th>
                                                <th title="End client location"></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th title="Segment"></th>
                                                <th title="Amount of Sales Articles"></th>
                                                <th title="Production company"></th>
                                                <th title="Production hours"></th>
                                                <th title="Service hours"></th>
                                                <th title="Sales company"></th>
                                                <th title="Responsible"></th>
                                                <th title="Inside sales"></th>
                                                <th title="Team leader"></th>
                                                <th title="Key account manager"></th>
                                                <th title="Area sales manager"></th>
                                                <th></th>
                                                <th title="Requested quote date"></th>
                                                <th></th>
                                                <th title="Offer intake"></th>
                                                <th title="Requested order date"></th>
                                                <th title="Next contact date"></th>
                                                <th title="Follow up"></th>
                                                <th title="Last contact"></th>
                                                <th title="Order value estimated"></th>
                                                <th title="Quote value"></th>
                                                <th></th>
                                                <th></th>
                                                <th title="Order value estimated x probability"></th>
                                                <th title="Sales order number"></th>
                                                <th title="Production order number"></th>
                                                <th title="Order company"></th>
                                                <th title="Project manager"></th>
                                                <th></th>
                                                <th title="Requested delivery date"></th>
                                                <th></th>
                                                <th title="Order value"></th>
                                                <th title="Contribution margin &euro;"></th>
                                                <th title="Contribution margin %"></th>
                                            </tr>
                                            <tr>
                                                <th colspan="6" class="color-general">General&nbsp;<button class="btn btn-table btnTable tableGeneralAll">+</button></th>
                                                <th colspan="13" class="color-client">Customer&nbsp;<button class="btn btn-table btnTable tableClientAll">+</button></th>
                                                <th colspan="7" class="color-offer">Offer details&nbsp;<button class="btn btn-table btnTable tableOfferAll">+</button></th>
                                                <th colspan="7" class="color-sales">Sales&nbsp;<button class="btn btn-table btnTable tableSalesAll">+</button></th>
                                                <th colspan="8" class="color-timeline">Timeline&nbsp;<button class="btn btn-table btnTable tableTimelineAll">+</button></th>
                                                <th colspan="5" class="color-pricing">Pricing&nbsp;<button class="btn btn-table btnTable tablePriceAll">+</button></th>
                                                <th colspan="10" class="color-order">Order details&nbsp;<button class="btn btn-table btnTable tableOrderAll">+</button></th>
                                            </tr>
                                            <tr>
                                                <th style="padding: 2px 9px 2px 9px;" title="Reason" class="color-general">
                                                    S
                                                </th>
                                                <th title="Reason" class="color-general">Re</th>
                                                <th title="Quote type" class="text-center color-general p0">T</th>
                                                <th title="Quote number" class="color-general">QN</th>
                                                <th title="Internal number" class="color-general">IN</th>
                                                <th class="color-general">Project</th>
                                                <th class="color-client">Customer</th>
                                                <th class="color-client">Reseller</th>
                                                <th title="Reseller Inquiry" class="color-client">Inquiry</th>
                                                <th title="Reseller ZIP" class="color-client">ZIP</th>
                                                <th title="Reseller location" class="color-client">Location</th>
                                                <th class="color-client">Industry</th>
                                                <th class="color-client">Country</th>
                                                <th class="color-client">End client</th>
                                                <th title="End client Inquiry" class="color-client">Inquiry</th>
                                                <th title="End client ZIP" class="color-client">ZIP</th>
                                                <th title="End client location" class="color-client">Location</th>
                                                <th class="color-client">Industry</th>
                                                <th class="color-client">Country (e)</th>
                                                <th class="color-offer">Description</th>
                                                <th class="color-offer">Comments</th>
                                                <th title="Segment" class="color-offer">Seg</th>
                                                <th title="Amount of Sales Articles" class="text-left-important color-offer">ASA</th>
                                                <th title="Production company" class="color-offer">PC</th>
                                                <th title="Production hours" class="text-left-important color-offer">EPHP</th>
                                                <th title="Service hours" class="text-left-important color-offer">EPHS</th>
                                                <th title="Sales company" class="color-sales">SC</th>
                                                <th title="Responsible" class="color-sales">R</th>
                                                <th title="Inside sales" class="color-sales">IS</th>
                                                <th title="Calculating person" class="color-sales">CP</th>
                                                <th title="Team leader" class="color-sales">TL</th>
                                                <th title="Key account manager" class="color-sales">KAM</th>
                                                <th title="Area sales manager" class="color-sales">ASM</th>
                                                <th class="color-timeline">Inquiry</th>
                                                <th title="Requested quote date" class="color-timeline">Quote(R)</th>
                                                <th class="color-timeline">Quote</th>
                                                <th title="Offer intake" class="text-left-important color-timeline">O-I</th>
                                                <th title="Requested order date" class="color-timeline">Order(R)</th>
                                                <th title="Next contact date" class="color-timeline">Next con. date</th> <!-- Contact(R) -->
                                                <th title="Follow up" class="color-timeline">FU</th>
                                                <th title="Last contact" class="color-timeline">Last contact</th>
                                                <th title="Order value estimated" class="text-left-important color-pricing">OVE</th>
                                                <th title="Quote value" class="text-left-important color-pricing">QV</th>
                                                <th class="text-left-important color-pricing">GO</th>
                                                <th class="text-left-important color-pricing">GET</th>
                                                <th title="Order value estimated x probability" class="text-left-important color-pricing">OVEgg</th>
                                                <th title="Sales order number" class="color-order">SON</th>
                                                <th title="Production order number" class="color-order">PON</th>
                                                <th title="Order company" class="color-order">OC</th>
                                                <th title="Project manager" class="color-order">PM</th>
                                                <th class="color-order">Order</th>
                                                <th title="Requested delivery date" class="color-order">Delivery(R)</th>
                                                <th class="color-order">Delivery</th>
                                                <th title="Order value" class="text-left-important color-order">OrV</th>
                                                <th title="Contribution margin &euro;" class="text-left-important color-order">CM&euro;</th>
                                                <th title="Contribution margin %" class="text-left-important color-order">CM%</th>
                                            </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php include('footer.php'); ?>
    <script src="assets/plugins/waypoints/lib/jquery.waypoints.min.js"></script>
    <script>
        var tableInt = "";
        var table = "";
        var init = 0;
        var initPerm = 0;

        function saveColumns(columns, table) {
            var visibleCols = [];
            table.columns().every(function() {
                if (this.visible() == "true" || this.visible() == true)
                    visibleCols.push(table.settings().init().columns[this[0][0]].name);
            })
            $.ajax({
                url: "assets/php/ajaxHandeler.php",
                method: "get",
                data: {
                    action: 'saveOfferCols',
                    cols: visibleCols
                }
            })
        }

        function saveCurState(state, tableUniqueId) {
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "saveCurState",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: state,
                    tableUniqueId: tableUniqueId
                }
            });
        }

        function changeState(state, sesLoad = 1) {
            $('#offers').DataTable().clear().destroy();
            table.clear().destroy();
            $('.table-responsive').html(tableInt);
            table = genTable(state, sesLoad);
            saveCurState(state, "CRM-offers-management");
            $("#stateSelect").val(state);
        }

        function saveFilters(tableUniqueId) {
            const name = $("#saveStateName").val();
            const filters = table.state();
            const columns = filters.columns;
            // ADD COLUMN NAMES TO FILTERS
            table.columns().every(function() {
                const index = this[0][0];
                const colName = table.settings().init().columns[index].name;
                if (columns[index] && !columns[index].hasOwnProperty('name')) {
                    columns[index].name = colName;
                }
            });
            $.ajax({
                type: 'POST',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "saveFilters",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: name,
                    tableUniqueId: tableUniqueId,
                    filters: table.state()
                },
                success: function() {
                    getFiltersList(tableUniqueId);
                    setTimeout(function() {
                        $("#stateSelect").val(name).trigger("change");
                    }, 500);
                    $("#saveStateName").val("");
                    $("#saveStateModal").modal("hide");
                }
            });
        }

        function overwriteFilters(tableUniqueId) {
            const name = $("#overwriteStateName").val();
            const curState = $("#stateSelect").val();

            const filters = table.state();
            const columns = filters.columns;

            // ADD COLUMN NAMES TO FILTERS
            table.columns().every(function() {
                const index = this[0][0];
                const colName = table.settings().init().columns[index].name;
                if (columns[index] && !columns[index].hasOwnProperty('name')) {
                    columns[index].name = colName;
                }
            });

            $.ajax({
                type: 'POST',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "overwriteFilters",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: name,
                    curState: curState,
                    tableUniqueId: tableUniqueId,
                    filters: table.state()
                },
                success: function(res) {
                    if (res == "error") {
                        alert("You can't overwrite Default view!");
                    } else {
                        getFiltersList(tableUniqueId);
                        setTimeout(function() {
                            $("#stateSelect").val(name).trigger("change");
                        }, 500);
                        $("#overwriteState").modal("hide");
                    }
                }
            });
        }

        function saveDynamic(tableUniqueId) {
            var name = "Dynamic view";
            $.ajax({
                type: 'POST',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "overwriteFilters",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: name,
                    curState: name,
                    tableUniqueId: tableUniqueId,
                    filters: table.state()
                },
                success: function(res) {
                    if (res == "error") {
                        alert("You can't overwrite Default view!");
                    }
                }
            });
        }

        function saveSessionView(tableUniqueId) {
            const name = $("#stateSelect").val();
            const filters = table.state();
            const columns = filters.columns;

            // ADD COLUMN NAMES TO FILTERS
            table.columns().every(function() {
                const index = this[0][0];
                const colName = table.settings().init().columns[index].name;
                if (columns[index] && !columns[index].hasOwnProperty('name')) {
                    columns[index].name = colName;
                }
            });

            $.ajax({
                type: 'POST',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "saveSessionView",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: name,
                    tableUniqueId: tableUniqueId,
                    filters: table.state()
                },
                success: function(res) {
                    if (res == "error") {
                        alert("You can't overwrite Default view!");
                    }
                }
            });
        }

        function removeState(tableUniqueId) {
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "removeState",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    name: $('#stateSelect').val(),
                    tableUniqueId: tableUniqueId
                },
                success: function() {
                    getFiltersList(tableUniqueId);
                    changeState("Default");
                }
            });
        }

        function getFiltersList(tableUniqueId) {
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "getFiltersList",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    tableUniqueId: tableUniqueId
                },
                success: function(res) {
                    $("#stateSelect").html("");
                    var data = JSON.parse(res);
                    for (var i = 0; i < data.length; i++)
                        $("#stateSelect").append("<option value='" + data[i] + "'>" + data[i] + "</option>");
                }
            });
        }

        function getCurrentUserState(tableUniqueId, initialFiltersState) {
            var cur = "Default";
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php',
                data: {
                    action: "getCurrentUserState",
                    user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                    tableUniqueId: tableUniqueId
                },
                success: function(res) {
                    $("#stateSelect").val(res);
                    table = genTable(res, 1, initialFiltersState);
                }
            });
        }

        function countFilters() {
            var filters = 0;
            $(".filterStyle").each(function() {
                if ($(this).val() != null && $(this).val().length != 0 && $(this).val() != "") {
                    filters++;
                    $(this).css("color", "#000");
                }
            })
            $(".select2-selection__rendered").each(function() {
                if ($(this).html().substring(0, 2) != "" && $(this).html() != "")
                    $(this).css("color", "#000");
                else
                    $(this).css("color", "#6C757D");
            })
            $("#filtersCountSpan").html("&nbsp;<strong>(" + filters + ")</strong>")
        }

        function genTable(state, sesLoad = 1, initialFiltersState = {}) {
            table = "";
            init = 0;

            let order = [3, "desc"];
            const visibleColumns = [];

            // NEED TO EXCLUDE THEM TO WORK BUT IDK WHY... 
            const colNamesToExclude = ["endClientInquiryNoFilter", "inquiryNoFilter"];

            if (initialFiltersState.length) {

                order = initialFiltersState.order[0];

                initialFiltersState.columns.forEach(function(col, index) {

                    const isVisible = JSON.parse(col.visible);

                    if (colNamesToExclude.includes(col.name) === false) {
                        visibleColumns.push({
                            index: index,
                            name: col.name,
                            visible: isVisible
                        });
                    }

                });

            }

            return $('#offers').DataTable({
                "iDisplayLength": 50,
                stateSave: true,
                "stateDuration": 0,
                "orderMulti": true,
                fixedHeader: {
                    header: true,
                    footer: false
                },
                "columnDefs": [{
                        "orderable": false,
                        "targets": 0
                    },
                    {
                        "targets": [37],
                        "createdCell": function(td, cellData, rowData, row, col) {
                            data = cellData.split("[-]");
                            if (data[1] == "" || data[1] == null) {
                                if (data[2] == "Past") {
                                    $(td).css("background-color", "rgba(255,0,0,0.5)").html(data[0]);
                                }
                                if (data[0] == null || data[0] == "" || data[0] == "0" || data[0] == "0%" || data[0] == "0&euro;" || data[0] == '0000-00-00') {
                                    $(td).addClass('emptyBox');
                                }
                            }
                            $(td).html(data[0]);
                        }
                    },
                    {
                        "targets": [6, 7, 13],
                        "createdCell": function(td, cellData, rowData, row, col) {
                            if (cellData === null) {
                                /*
                                console.log('createdCell');
                                console.log(td);
                                console.log(cellData);
                                console.log(rowData);
                                console.log(row);
                                console.log(col);
                                */
                                return;
                            }
                            var data = cellData.split('{-}');
                            $(td).html(data[1]);
                            var pomoc = "";
                            switch (data[0]) {
                                case 'A+':
                                    pomoc = "A";
                                    $(td).css("color", "white");
                                    break;
                                case 'B+':
                                    pomoc = "B";
                                    break;
                                case 'A-':
                                    pomoc = "C";
                                    $(td).css("color", "white");
                                    break;
                                case 'New':
                                    pomoc = "New";
                                    $(td).css("color", "white");
                                    break;
                                case 'B-':
                                    pomoc = "D";
                                    break;
                            }
                            $(td).addClass("type" + pomoc + "-box");
                        }
                    },
                    {
                        "targets": 0,
                        "createdCell": function(td, cellData, rowData, row, col) {
                            c = cellData.split("[-]");
                            $(td).parent().addClass('pointer');
                            $(td).parent().children().first().attr("offerno", c[1]);
                            $(td).parent().contextmenu(function() {
                                window.open(
                                    'offer.php?id=' + $(td).parent().children().first().attr("offerno"),
                                    '_blank'
                                );
                            });
                            $(td).html(c[0]);
                            if (c[0] == "<div class='circle-grey'></div>" || c[0] == "<div class='circle-grey'>R</div>") {
                                switch (c[2]) {
                                    case 'Order':
                                        $(td).html("<i class='fa fa-check' style='color:#28A745;margin:4px 3px;font-size:20px;'></i>");
                                        break;
                                    case 'Terminated':
                                        $(td).html("<i class='fa fa-times' style='color:#FFC107;margin:4px 5px;font-size:20px;'></i>");
                                        break;
                                    case 'Lost':
                                        $(td).html("<i class='fa fa-times' style='color:#DC3545;margin:4px 5px;font-size:20px;'></i>");
                                        break;
                                }
                            }
                        }
                    }
                ],
                "order": [
                    order
                ],
                dom: "<'row'<'col-xs-12 col-sm-12 col-xl-4 addDeleteClass w-100 d-flex'l><'col-xs-12 col-sm-12 col-xl-1 pagePerPage pushPagePerPage'><''p>>" +
                    "<'row'<'col-sm-12'tr>>" +
                    "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                "processing": true,
                "serverSide": true,
                'ajax': {
                    'url': 'assets/php/offersProcessing.php',
                    type: "POST",
                    "data": {
                        "client": function(d) {
                            return $('#clientFilter').val()
                        },
                        "end_client": function(d) {
                            return $('#endClientFilter').val()
                        },
                        //"clientLocationFilter": function ( d ) { return  $('#clientLocationFilter').val()},
                        "startOrderDateFilter": function(d) {
                            return $('#startOrderDateFilter').val()
                        },
                        "endOrderDateFilter": function(d) {
                            return $('#endOrderDateFilter').val()
                        },
                        "startReqOrderDateFilter": function(d) {
                            return $('#startReqOrderDateFilter').val()
                        },
                        "endReqOrderDateFilter": function(d) {
                            return $('#endReqOrderDateFilter').val()
                        },
                        "vFilter": function(d) {
                            return $('#vFilter').val()
                        },
                        "tlFilter": function(d) {
                            return $('#tlFilter').val()
                        },
                        "axFilter": function(d) {
                            return $('#axFilter').val()
                        },
                        "reasonFilter": function(d) {
                            return $('#reasonFilter').val()
                        },
                        "startInquiryDateFilter": function(d) {
                            return $('#startInquiryDateFilter').val()
                        },
                        "endInquiryDateFilter": function(d) {
                            return $('#endInquiryDateFilter').val()
                        },
                        "startORDFilter": function(d) {
                            return $('#startORDFilter').val()
                        },
                        "endORDFilter": function(d) {
                            return $('#endORDFilter').val()
                        },
                        "startOfferFilter": function(d) {
                            return $('#startOfferFilter').val()
                        },
                        "endOfferFilter": function(d) {
                            return $('#endOfferFilter').val()
                        },
                        "startNCDFilter": function(d) {
                            return $('#startNCDFilter').val()
                        },
                        "endNCDFilter": function(d) {
                            return $('#endNCDFilter').val()
                        },
                        "startDRDFilter": function(d) {
                            return $('#startDRDFilter').val()
                        },
                        "endDRDFilter": function(d) {
                            return $('#endDRDFilter').val()
                        },
                        "startDeliveryFilter": function(d) {
                            return $('#startDeliveryFilter').val()
                        },
                        "endDeliveryFilter": function(d) {
                            return $('#endDeliveryFilter').val()
                        },
                        "clientA": function(d) {
                            if ($('#clientA').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientAEnd": function(d) {
                            if ($('#clientAEnd').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientB": function(d) {
                            if ($('#clientB').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientBEnd": function(d) {
                            if ($('#clientBEnd').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientC": function(d) {
                            if ($('#clientC').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientCEnd": function(d) {
                            if ($('#clientCEnd').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientD": function(d) {
                            if ($('#clientD').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "clientDEnd": function(d) {
                            if ($('#clientDEnd').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "statusGreen": function(d) {
                            if ($('#statusGreen').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "statusYellow": function(d) {
                            if ($('#statusYellow').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "statusRed": function(d) {
                            if ($('#statusRed').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "statusBlue": function(d) {
                            if ($('#statusBlue').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "statusGrey": function(d) {
                            if ($('#statusGrey').prop("checked") == true) return 1;
                            else return 0;
                        },
                        "isFilter": function(d) {
                            return $('#isFilter').val()
                        },
                        "cpFilter": function(d) {
                            return $('#cpFilter').val()
                        },
                        "kamFilter": function(d) {
                            return $('#kamFilter').val()
                        },
                        "asmFilter": function(d) {
                            return $('#asmFilter').val()
                        },
                        "otFilter": function(d) {
                            return $('#otFilter').val()
                        },
                        "countryFilter": function(d) {
                            return $('#countryFilter').val()
                        },
                        "countryEFilter": function(d) {
                            return $('#countryEFilter').val()
                        },
                        "ocFilter": function(d) {
                            return $('#ocFilter').val()
                        },
                        <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1) { ?> "cmpFilter": function(d) {
                                return $('#cmpFilter').val()
                            },
                            "prodFilter": function(d) {
                                return $('#prodFilter').val()
                            },
                        <?php } ?> "pmFilter": function(d) {
                            return $('#pmFilter').val()
                        },
                        "orderNoFilter": function(d) {
                            return $('#orderNoFilter').val()
                        },
                        "prodOrderNoFilter": function(d) {
                            return $('#prodOrderNoFilter').val()
                        },
                        "projectFilter": function(d) {
                            return $('#projectFilter').val()
                        },
                        "projectNoFilter": function(d) {
                            return $('#projectNoFilter').val()
                        },
                        "segmentFilter": function(d) {
                            return $('#segmentFilter').val()
                        },
                        "followupFilter": function(d) {
                            return $('#followupFilter').val()
                        },
                        //"endClientLocationFilter": function ( d ) { return  $('#endClientLocationFilter').val()},
                        "minOVE": function(d) {
                            return $('#minOVE').val()
                        },
                        "maxOVE": function(d) {
                            return $('#maxOVE').val()
                        },
                        "minOFV": function(d) {
                            return $('#minOFV').val()
                        },
                        "maxOFV": function(d) {
                            return $('#maxOFV').val()
                        },
                        "minGO": function(d) {
                            return $('#minGO').val()
                        },
                        "maxGO": function(d) {
                            return $('#maxGO').val()
                        },
                        "minGET": function(d) {
                            return $('#minGET').val()
                        },
                        "maxGET": function(d) {
                            return $('#maxGET').val()
                        },
                        "minGxG": function(d) {
                            return $('#minGxG').val()
                        },
                        "maxGxG": function(d) {
                            return $('#maxGxG').val()
                        },
                        "minOVgg": function(d) {
                            return $('#minOVgg').val()
                        },
                        "maxOVgg": function(d) {
                            return $('#maxOVgg').val()
                        },
                        "minOFVCM": function(d) {
                            return $('#minOFVCM').val()
                        },
                        "maxOFVCM": function(d) {
                            return $('#maxOFVCM').val()
                        },
                        "minORVCM": function(d) {
                            return $('#minORVCM').val()
                        },
                        "maxORVCM": function(d) {
                            return $('#maxORVCM').val()
                        },
                        "minORV": function(d) {
                            return $('#minORV').val()
                        },
                        "maxORV": function(d) {
                            return $('#maxORV').val()
                        },
                        "minWHP": function(d) {
                            return $('#minWHP').val()
                        },
                        "maxWHP": function(d) {
                            return $('#maxWHP').val()
                        },
                        "minWHS": function(d) {
                            return $('#minWHS').val()
                        },
                        "maxWHS": function(d) {
                            return $('#maxWHS').val()
                        },
                        "offerNoFilter": function(d) {
                            return $('#offerNoFilter').val()
                        },
                        "aocFilter": function(d) {
                            return $('#aocFilter').val()
                        },
                        "oldOfferNoFilter": function(d) {
                            return $('#oldOfferNoFilter').val()
                        },
                        "scopeFilter": function(d) {
                            return $('#scopeFilter').val()
                        },
                        "commentsFilter": function(d) {
                            return $('#commentsFilter').val()
                        },
                        "lastContactFilter": function(d) {
                            return $('#lastContactFilter').val()
                        },
                        "clientCityFilter": function(d) {
                            return $('#clientCityFilter').val()
                        },
                        "clientZipFilter": function(d) {
                            return $('#clientZipFilter').val()
                        },
                        "endClientCityFilter": function(d) {
                            return $('#endClientCityFilter').val()
                        },
                        "endClientZipFilter": function(d) {
                            return $('#endClientZipFilter').val()
                        },
                        "marketRFilter": function(d) {
                            return $('#marketRFilter').val()
                        },
                        "marketEFilter": function(d) {
                            return $('#marketEFilter').val()
                        },
                        "initPerm": function(d) {
                            return initPerm
                        },
                        "inquiryNoFilter": function(d) {
                            return $('#inquiryNoFilter').val()
                        },
                        "endClientInquiryNoFilter": function(d) {
                            return $('#endClientInquiryNoFilter').val()
                        }
                    },
                },
                "stateSaveParams": function(settings, data) {
                    data.client = $('#clientFilter').val();
                    //data.clientLocationFilter = $('#clientLocationFilter').val();
                    data.endClient = $('#endClientFilter').val();
                    //data.endClientLocationFilter = $('#endClientLocationFilter').val();
                    data.startOrderDateFilter = $('#startOrderDateFilter').val();
                    data.endOrderDateFilter = $('#endOrderDateFilter').val();
                    data.startReqOrderDateFilter = $('#startReqOrderDateFilter').val();
                    data.endReqOrderDateFilter = $('#endReqOrderDateFilter').val();
                    data.vFilter = $('#vFilter').val();
                    data.tlFilter = $('#tlFilter').val();
                    data.axFilter = $('#axFilter').val();
                    data.reasonFilter = $('#reasonFilter').val();
                    data.startInquiryDateFilter = $('#startInquiryDateFilter').val();
                    data.endInquiryDateFilter = $('#endInquiryDateFilter').val();
                    data.startORDFilter = $('#startORDFilter').val();
                    data.endORDFilter = $('#endORDFilter').val();
                    data.startOfferFilter = $('#startOfferFilter').val();
                    data.endOfferFilter = $('#endOfferFilter').val();
                    data.startNCDFilter = $('#startNCDFilter').val();
                    data.endNCDFilter = $('#endNCDFilter').val();
                    data.startDRDFilter = $('#startDRDFilter').val();
                    data.endDRDFilter = $('#endDRDFilter').val();
                    data.startDeliveryFilter = $('#startDeliveryFilter').val();
                    data.endDeliveryFilter = $('#endDeliveryFilter').val();
                    data.clientA = 0;
                    if ($('#clientA').prop("checked") == true)
                        data.clientA = 1;
                    data.clientAEnd = 0;
                    if ($('#clientAEnd').prop("checked") == true)
                        data.clientAEnd = 1;
                    data.clientB = 0;
                    if ($('#clientB').prop("checked") == true)
                        data.clientB = 1;
                    data.clientBEnd = 0;
                    if ($('#clientBEnd').prop("checked") == true)
                        data.clientBEnd = 1;
                    data.clientC = 0;
                    if ($('#clientC').prop("checked") == true)
                        data.clientC = 1;
                    data.clientCEnd = 0;
                    if ($('#clientCEnd').prop("checked") == true)
                        data.clientCEnd = 1;
                    data.clientD = 0;
                    if ($('#clientD').prop("checked") == true)
                        data.clientD = 1;
                    data.clientDEnd = 0;
                    if ($('#clientDEnd').prop("checked") == true)
                        data.clientDEnd = 1;
                    data.statusGreen = 0;
                    if ($('#statusGreen').prop("checked") == true)
                        data.statusGreen = 1;
                    data.statusYellow = 0;
                    if ($('#statusYellow').prop("checked") == true)
                        data.statusYellow = 1;
                    data.statusRed = 0;
                    if ($('#statusRed').prop("checked") == true)
                        data.statusRed = 1;
                    data.statusBlue = 0;
                    if ($('#statusBlue').prop("checked") == true)
                        data.statusBlue = 1;
                    data.statusGrey = 0;
                    if ($('#statusGrey').prop("checked") == true)
                        data.statusGrey = 1;
                    data.isFilter = $('#isFilter').val();
                    data.cpFilter = $('#cpFilter').val();
                    data.kamFilter = $('#kamFilter').val();
                    data.asmFilter = $('#asmFilter').val();
                    data.otFilter = $('#otFilter').val();
                    data.countryFilter = $('#countryFilter').val();
                    data.countryEFilter = $('#countryEFilter').val();
                    data.ocFilter = $('#ocFilter').val();
                    <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1) { ?>
                        data.cmpFilter = $('#cmpFilter').val();
                        data.prodFilter = $('#prodFilter').val();
                    <?php } ?>
                    data.pmFilter = $('#pmFilter').val();
                    data.orderNoFilter = $('#orderNoFilter').val();
                    data.prodOrderNoFilter = $('#prodOrderNoFilter').val();
                    data.projectFilter = $('#projectFilter').val();
                    data.projectNoFilter = $('#projectNoFilter').val();
                    data.segmentFilter = $('#segmentFilter').val();
                    data.followupFilter = $('#followupFilter').val();
                    data.minOVE = $('#minOVE').val();
                    data.maxOVE = $('#maxOVE').val();
                    data.minOFV = $('#minOFV').val();
                    data.maxOFV = $('#maxOFV').val();
                    data.minGO = $('#minGO').val();
                    data.maxGO = $('#maxGO').val();
                    data.minGET = $('#minGET').val();
                    data.maxGET = $('#maxGET').val();
                    data.minGxG = $('#minGxG').val();
                    data.maxGxG = $('#maxGxG').val();
                    data.minGxG = $('#minGxG').val();
                    data.maxGxG = $('#maxGxG').val();
                    data.minOVgg = $('#minOVgg').val();
                    data.maxOVgg = $('#maxOVgg').val();
                    data.minOFVCM = $('#minOFVCM').val();
                    data.maxOFVCM = $('#maxOFVCM').val();
                    data.minORVCM = $('#minORVCM').val();
                    data.maxORVCM = $('#maxORVCM').val();
                    data.minORV = $('#minORV').val();
                    data.maxORV = $('#maxORV').val();
                    data.minWHP = $('#minWHP').val();
                    data.maxWHP = $('#maxWHP').val();
                    data.minWHS = $('#minWHS').val();
                    data.maxWHS = $('#maxWHS').val();
                    data.offerNoFilter = $('#offerNoFilter').val();
                    data.aocFilter = $('#aocFilter').val();
                    data.oldOfferNoFilter = $('#oldOfferNoFilter').val();
                    data.scopeFilter = $('#scopeFilter').val();
                    data.commentsFilter = $('#commentsFilter').val();
                    data.clientCityFilter = $('#clientCityFilter').val();
                    data.clientZipFilter = $('#clientZipFilter').val();
                    data.endClientCityFilter = $('#endClientCityFilter').val();
                    data.endClientZipFilter = $('#endClientZipFilter').val();
                    data.lastContactFilter = $('#lastContactFilter').val();
                    data.marketRFilter = $('#marketRFilter').val();
                    data.marketEFilter = $('#marketEFilter').val();
                    data.inquiryNoFilter = $('#inquiryNoFilter').val();
                    data.endClientInquiryNoFilter = $('#endClientInquiryNoFilter').val();
                },
                'columns': [{
                        data: 'conDate',
                        name: 'conDate',
                        className: 'text-center tableS'
                    },
                    {
                        visible: false,
                        data: 'reason',
                        name: 'reason',
                        className: 'tableM'
                    },
                    {
                        data: 'OT',
                        name: 'OT',
                        className: 'text-center fs20 tableS'
                    },
                    {
                        data: 'offerNo',
                        name: 'offerNo',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'oldOfferNo',
                        name: 'oldOfferNo',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'projectName',
                        name: 'projectName',
                        className: 'tableM'
                    },
                    {
                        data: 'oneClient',
                        name: 'oneClient',
                        className: 'tableB'
                    },
                    {
                        visible: false,
                        data: 'client',
                        name: 'client',
                        className: 'tableB'
                    },
                    {
                        visible: false,
                        data: 'inquiryNo',
                        name: 'inquiryNo',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'zip',
                        name: 'zip',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'city',
                        name: 'city',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'marketR',
                        name: 'marketR',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'country',
                        name: 'country',
                        className: 'tableM'
                    }, //10
                    {
                        visible: false,
                        data: 'shortNameEnd',
                        name: 'shortNameEnd',
                        className: 'tableB'
                    },
                    {
                        visible: false,
                        data: 'endclientInquiryNo',
                        name: 'endclientInquiryNo',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'Ezip',
                        name: 'Ezip',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'cityEnd',
                        name: 'cityEnd',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'marketE',
                        name: 'marketE',
                        className: 'tableM'
                    }, //14
                    {
                        visible: false,
                        data: 'countryE',
                        name: 'countryE',
                        className: 'tableM'
                    }, //14
                    {
                        data: 'scope',
                        name: 'scope',
                        className: 'tableB'
                    },
                    {
                        visible: false,
                        orderable: false,
                        data: 'comments',
                        name: 'comments',
                        className: 'td-comments tableB'
                    },
                    {
                        data: 'segment',
                        name: 'segment',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'SOC',
                        name: 'SOC',
                        className: 'text-right tableS'
                    },
                    {
                        visible: false,
                        data: 'productionLocation',
                        name: 'productionLocation',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'WHP',
                        name: 'WHP',
                        className: "text-right tableS"
                    },
                    {
                        visible: false,
                        data: 'WHS',
                        name: 'WHS',
                        className: "text-right tableS"
                    },
                    {
                        data: 'Company',
                        name: 'Company',
                        className: 'tableS'
                    },
                    {
                        data: 'InR',
                        name: 'InR',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'InID',
                        name: 'InID',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'calcPersons',
                        name: 'calcPersons',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'InTL',
                        name: 'InTL',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'InKAM',
                        name: 'InKAM',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'InASM',
                        name: 'InASM',
                        className: 'tableS'
                    },
                    {
                        data: 'inquiry',
                        name: 'inquiry',
                        className: 'tableM-date'
                    },
                    {
                        visible: false,
                        data: 'request',
                        name: 'request',
                        className: 'tableM-date'
                    }, //Quote (R)
                    {
                        visible: false,
                        data: 'offer',
                        name: 'offer',
                        className: 'tableM-date'
                    },
                    {
                        visible: false,
                        data: 'oi',
                        name: 'oi',
                        className: 'tableS'
                    },
                    {
                        data: 'requestedOrderDate',
                        name: 'requestedOrderDate',
                        className: 'tableM-date'
                    },
                    {
                        data: 'nextContactDate',
                        name: 'nextContactDate',
                        className: 'tableM-date'
                    },
                    {
                        data: 'InF',
                        name: 'InF',
                        className: 'tableS'
                    },
                    {
                        visible: false,
                        data: 'lastComment',
                        name: 'lastComment'
                    },
                    {
                        data: 'OVE',
                        name: 'OVE',
                        className: "text-right tableS-50"
                    },
                    {
                        data: 'OV',
                        name: 'OV',
                        className: "text-right tableS"
                    },
                    {
                        data: 'GO',
                        name: 'GO',
                        className: "text-right tableS"
                    },
                    {
                        data: 'GET',
                        name: 'GET',
                        className: "text-right tableS"
                    },
                    {
                        visible: false,
                        data: 'OVgg',
                        name: 'OVgg',
                        className: "text-right tableS"
                    },
                    {
                        data: 'orderNo',
                        name: 'orderNo',
                        className: 'tableM'
                    },
                    {
                        visible: false,
                        data: 'productionOrderNo',
                        name: 'productionOrderNo',
                        className: 'tableM'
                    },
                    {
                        data: 'orderCompany',
                        name: 'orderCompany',
                        className: "tableS"
                    },
                    {
                        data: 'inPM',
                        name: 'inPM',
                        className: 'tableS'
                    },
                    {
                        data: 'order',
                        name: 'order',
                        className: 'tableM-date'
                    },
                    {
                        visible: false,
                        data: 'requestedDeliveryDate',
                        name: 'requestedDeliveryDate',
                        className: 'tableM-date'
                    },
                    {
                        visible: false,
                        data: 'deliveryDate',
                        name: 'deliveryDate',
                        className: 'tableM-date'
                    },
                    {
                        data: 'orderValue',
                        name: 'orderValue',
                        className: "text-right tableS"
                    },
                    {
                        data: 'gCMe',
                        name: 'gCMe',
                        className: "text-right tableS"
                    },
                    {
                        data: 'gCMp',
                        name: 'gCMp',
                        className: "text-right tableS"
                    },
                    /*{ data: 'lastComment', name: 'lastComment' }, 
                     { visible: false, data: 'AX', name: 'AX' },
                     { visible: false, data: 'productionOrderNo', name: 'productionOrderNo'},
                     { visible: false, data: 'ORVCM', name: 'ORVCM', className: "text-right"},
                     { visible: false, data: 'prodValCM', name: 'prodValCM', className: "text-right"},
                     { visible: false, data: 'country', name: 'country'},
                     { visible: false, data: 'Ecountry', name: 'Ecountry'},*/
                ],
                "drawCallback": function() {
                    $('#Qsummary').html(table.ajax.json().iTotalDisplayRecordsTop);
                    $('#OVsummary').html(table.ajax.json().iTotalOVE + " k€");
                    $('#OValsummary').html(table.ajax.json().iTotalOV + " k€");
                    $('#OVggsummary').html(table.ajax.json().iTotalOVgg + " k€");
                    $('#GxGsummary').html(table.ajax.json().iAvgGxG + "%");
                    $('#GxGprogress').attr('aria-valuenow', table.ajax.json().iAvgGxG).css("width", table.ajax.json().iAvgGxG + "%");
                    $("td").mouseenter(function() {
                        if (!$(this).hasClass("td-comments")) {
                            $(this).attr("title", $(this).context.innerHTML.replace(/(<([^>]+)>)/gi, ""));
                        }
                    })
                    setInterval(function() {
                        var left = $(".table-responsive").scrollLeft();
                        var floatingLeft = 78;
                        if ($(document).width() <= 764)
                            floatingLeft = 20;
                        var le = parseInt(floatingLeft) - parseInt(left);
                        $(".fixedHeader-floating").css("left", "" + le + "px");
                    }, 10);
                    if ($('#stateSelect').val() == "Dynamic view")
                        saveDynamic("CRM-offers-management");
                    saveSessionView("CRM-offers-management");
                    <?php
                    if (isset($_GET['backId'])) {
                    ?>
                        try {
                            $('html').animate({
                                scrollTop: $("[offerNo='<?php echo $_GET['backId']; ?>']").offset().top
                            }, 250);
                        } catch (error) {}
                    <?php
                    }
                    ?>
                },
                stateLoadCallback: function(settings, callback) {
                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php',
                        data: {
                            action: "loadFilters",
                            user: <?= $_SESSION['plasticonDigitalUser']['id'] ?>,
                            name: state,
                            sesLoad: sesLoad,
                            tableUniqueId: "CRM-offers-management"
                        },
                        success: function(res) {
                            var st = JSON.parse(res);
                            callback(st);
                            // przyciski +/-
                            var general = '-';
                            $.each([0, 1, 2, 3, 4, 5], function(index, colNr) { // S, T, QN
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    general = '+';
                            });
                            $('.tableGeneralAll').html(general);
                            $(".tableGeneralAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [0, 1, 2, 3, 4, 5];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [1, 4, 5];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var clients = '-';
                            $.each([6], function(index, colNr) { // Customer
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    clients = '+';
                            });
                            $(".tableClientAll").html(clients);
                            $(".tableClientAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var offer = '-';
                            $.each([17, 18], function(index, colNr) { //Description, Segment
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    offer = '+';
                            });
                            $(".tableOfferAll").html(offer);
                            $(".tableOfferAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [19, 20, 21, 22, 23, 24];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [21, 22, 23, 24];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var sales = '-';
                            $.each([23, 24], function(index, colNr) { //SC, R
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    sales = '+';
                            });
                            $(".tableSalesAll").html(sales);
                            $(".tableSalesAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [27, 28, 29, 30, 31];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [27, 28, 29, 30, 31];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var timeline = '-';
                            $.each([32, 36, 37, 38], function(index, colNr) { //inquiry, order R, contact R, FU
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    timeline = '+';
                            });
                            $(".tableTimelineAll").html(timeline);
                            $(".tableTimelineAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [33, 34, 36, 37, 38, 39]; //  poprzednie kolumny (09.11.22) - 27,28,29,30,31,32,33,34
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [33, 34, 38, 39];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var price = '-';
                            $.each([40, 41, 42, 44], function(index, colNr) { // OVE, QV, GO, GET
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    price = '+';
                            });
                            $(".tablePriceAll").html(price);
                            $(".tablePriceAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [40, 41, 42, 43, 44];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [44];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            var order = '-';
                            $.each([45, 47, 48, 49, 52, 53, 54], function(index, colNr) { //SON, OC, PM, ORDER, ORV, CM(eur), CM%
                                var column = table.column(colNr);
                                if (column.visible() != true)
                                    order = '+';
                            });
                            $(".tableOrderAll").html(order);
                            $(".tableOrderAll").click(function() {
                                if ($(this).html() == "+") {
                                    $(this).html("-");
                                    var generalAll = [45, 46, 47, 48, 49, 50, 51, 52, 53, 54];
                                    $.each(generalAll, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", true);
                                        var column = table.column(colNr);
                                        column.visible(true);
                                    });
                                } else {
                                    $(this).html("+");
                                    var generalDefault = [45, 47, 48, 49, 51, 53, 54];
                                    $.each(generalDefault, function(index, colNr) {
                                        $('[data-column="' + colNr + '"]').prop("checked", false);
                                        var column = table.column(colNr);
                                        column.visible(false);
                                    });
                                }
                                saveColumns(table.columns(), table);
                                saveDynamic("CRM-offers-management");
                                saveSessionView("CRM-offers-management");
                            })
                            // koniec przyciski +/-
                            if (st.client != '' && st.client != null) {
                                $(".select2-reseller-filter").append("<option value='" + st.client + "' selected>" + st.client_name + "</option>").trigger('change');
                                //getCities(st.client, $('#clientLocationFilter'));
                            } else {
                                //$("#clientLocationFilter").html("<option value='' selected>--Location--</option>").prop("disabled", true);
                                $(".select2-reseller-filter").html("<option value='' selected>--Location--</option>");
                            }
                            setTimeout(function() {
                                $("#clientLocationFilter").val(st.clientLocationFilter).trigger('change');
                            }, 100);
                            if (st.endClient != '' && st.endClient != null) {
                                $(".select2-endclient-filter").append("<option value='" + st.endClient + "' selected>" + st.end_client_name + "</option>").trigger('change');
                                //getCities(st.endClient, $('#endClientLocationFilter'));
                            } else {
                                //$("#endClientLocationFilter").html("<option value='' selected>--Location--</option>").prop("disabled", true);
                                $(".select2-endclient-filter").html("<option value='' selected>--Location--</option>");
                            }
                            //setTimeout(function(){ $("#endClientLocationFilter").val(st.endClientLocationFilter).trigger('change'); }, 100);
                            $("#startOrderDateFilter").val(st.startOrderDateFilter).trigger('change');
                            $("#endOrderDateFilter").val(st.endOrderDateFilter).trigger('change');
                            $("#startReqOrderDateFilter").val(st.startReqOrderDateFilter).trigger('change');
                            $("#endReqOrderDateFilter").val(st.endReqOrderDateFilter).trigger('change');
                            $("#vFilter").val((st.vFilter) != null ? st.vFilter : null).trigger('change');
                            $("#tlFilter").val((st.tlFilter) != null ? st.tlFilter : null).trigger('change');
                            $("#axFilter").val((st.axFilter) != null ? st.axFilter : null).trigger('change');
                            $("#reasonFilter").val((st.reasonFilter) != null ? st.reasonFilter : null).trigger('change');
                            $("#startInquiryDateFilter").val(st.startInquiryDateFilter).trigger('change');
                            $("#endInquiryDateFilter").val(st.endInquiryDateFilter).trigger('change');
                            $("#startORDFilter").val(st.startORDFilter).trigger('change');
                            $("#endORDFilter").val(st.endORDFilter).trigger('change');
                            $("#startOfferFilter").val(st.startOfferFilter).trigger('change');
                            $("#endOfferFilter").val(st.endOfferFilter).trigger('change');
                            $("#startNCDFilter").val(st.startNCDFilter).trigger('change');
                            $("#endNCDFilter").val(st.endNCDFilter).trigger('change');
                            $("#startDRDFilter").val(st.startDRDFilter).trigger('change');
                            $("#endDRDFilter").val(st.endDRDFilter).trigger('change');
                            $("#startDeliveryFilter").val(st.startDeliveryFilter).trigger('change');
                            $("#endDeliveryFilter").val(st.endDeliveryFilter).trigger('change');
                            if (st.clientA == 1 || st.clientA == null)
                                $("#clientA").prop("checked", true);
                            else
                                $("#clientA").prop("checked", false);
                            if (st.clientAEnd == 1 || st.clientAEnd == null)
                                $("#clientAEnd").prop("checked", true);
                            else
                                $("#clientAEnd").prop("checked", false);
                            if (st.clientB == 1 || st.clientB == null)
                                $("#clientB").prop("checked", true);
                            else
                                $("#clientB").prop("checked", false);
                            if (st.clientBEnd == 1 || st.clientBEnd == null)
                                $("#clientBEnd").prop("checked", true);
                            else
                                $("#clientBEnd").prop("checked", false);
                            if (st.clientC == 1 || st.clientC == null)
                                $("#clientC").prop("checked", true);
                            else
                                $("#clientC").prop("checked", false);
                            if (st.clientCEnd == 1 || st.clientCEnd == null)
                                $("#clientCEnd").prop("checked", true);
                            else
                                $("#clientCEnd").prop("checked", false);
                            if (st.clientD == 1 || st.clientD == null)
                                $("#clientD").prop("checked", true);
                            else
                                $("#clientD").prop("checked", false);
                            if (st.clientDEnd == 1 || st.clientDEnd == null)
                                $("#clientDEnd").prop("checked", true);
                            else
                                $("#clientDEnd").prop("checked", false);
                            if (st.statusGreen == 1 || st.statusGreen == null)
                                $("#statusGreen").prop("checked", true);
                            else
                                $("#statusGreen").prop("checked", false);
                            if (st.statusYellow == 1 || st.statusYellow == null)
                                $("#statusYellow").prop("checked", true);
                            else
                                $("#statusYellow").prop("checked", false);
                            if (st.statusRed == 1 || st.statusRed == null)
                                $("#statusRed").prop("checked", true);
                            else
                                $("#statusRed").prop("checked", false);
                            if (st.statusBlue == 1 || st.statusBlue == null)
                                $("#statusBlue").prop("checked", true);
                            else
                                $("#statusBlue").prop("checked", false);
                            if (st.statusGrey == 1 || st.statusGrey == null)
                                $("#statusGrey").prop("checked", true);
                            else
                                $("#statusGrey").prop("checked", false);
                            $("#isFilter").val((st.isFilter) != null ? st.isFilter : null).trigger('change');
                            $("#cpFilter").val((st.cpFilter) != null ? st.cpFilter : null).trigger('change');
                            $("#kamFilter").val((st.kamFilter) != null ? st.kamFilter : null).trigger('change');
                            $("#asmFilter").val((st.asmFilter) != null ? st.asmFilter : null).trigger('change');
                            $("#otFilter").val((st.otFilter) != null ? st.otFilter : null).trigger('change');
                            $("#countryFilter").val((st.countryFilter) != null ? st.countryFilter : null).trigger('change');
                            $("#countryEFilter").val((st.countryEFilter) != null ? st.countryEFilter : null).trigger('change');
                            if (st.ocFilter != "" && st.ocFilter != null)
                                $("#ocFilter").val(st.ocFilter).trigger('change');
                            else
                                $("#ocFilter").val('').trigger('change');
                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['visibleOtherCompanies'] == 1) { ?>
                                if (st.cmpFilter != "" && st.cmpFilter != null)
                                    $("#cmpFilter").val(st.cmpFilter).trigger('change');
                                else
                                    $("#cmpFilter").val('').trigger('change');
                                if (st.prodFilter != "" && st.prodFilter != null)
                                    $("#prodFilter").val(st.prodFilter).trigger('change');
                                else
                                    $("#prodFilter").val('').trigger('change');
                            <?php } ?>
                            $("#pmFilter").val((st.pmFilter) != null ? st.pmFilter : null).trigger('change');
                            $("#orderNoFilter").val(st.orderNoFilter);
                            $("#prodOrderNoFilter").val(st.prodOrderNoFilter);
                            $("#projectFilter").val((st.projectFilter) != null ? st.projectFilter : null).trigger('change');
                            $("#segmentFilter").val((st.segmentFilter) != null ? st.segmentFilter : null).trigger('change');
                            $("#followupFilter").val((st.followupFilter) != null ? st.followupFilter : null).trigger('change');
                            $("#minOVE").val(st.minOVE);
                            $("#maxOVE").val(st.maxOVE);
                            $("#minOFV").val(st.minOFV);
                            $("#maxOFV").val(st.maxOFV);
                            $("#minGO").val(st.minGO);
                            $("#maxGO").val(st.maxGO);
                            $("#minGET").val(st.minGET);
                            $("#maxGET").val(st.maxGET);
                            $("#minGxG").val(st.minGxG);
                            $("#maxGxG").val(st.maxGxG);
                            $("#minOVgg").val(st.minOVgg);
                            $("#maxOVgg").val(st.maxOVgg);
                            $("#minOFVCM").val(st.minOFVCM);
                            $("#maxOFVCM").val(st.maxOFVCM);
                            $("#minORVCM").val(st.minORVCM);
                            $("#maxORVCM").val(st.maxORVCM);
                            $("#minORV").val(st.minORV);
                            $("#maxORV").val(st.maxORV);
                            $("#minWHP").val(st.minWHP);
                            $("#maxWHP").val(st.maxWHP);
                            $("#minWHS").val(st.minWHS);
                            $("#maxWHS").val(st.maxWHS);
                            $("#offerNoFilter").val(st.offerNoFilter);
                            $("#aocFilter").val(st.aocFilter);
                            $("#oldOfferNoFilter").val(st.oldOfferNoFilter);
                            $("#scopeFilter").val(st.scopeFilter);
                            $("#commentsFilter").val(st.commentsFilter);
                            $("#clientCityFilter").val(st.clientCityFilter);
                            $("#clientZipFilter").val(st.clientZipFilter);
                            $("#endClientCityFilter").val(st.endClientCityFilter);
                            $("#endClientZipFilter").val(st.endClientZipFilter).trigger("change");
                            $("#marketRFilter").val(st.marketRFilter).trigger("change");
                            $("#marketEFilter").val(st.marketEFilter).trigger("change");
                            $("#inquiryNoFilter").val(st.inquiryNoFilter);
                            $("#endClientInquiryNoFilter").val(st.endClientInquiryNoFilter);
                            const visibleColumnsIndexes = [];
                            if (visibleColumns.length) {
                                visibleColumns.forEach(function(col, index) {
                                    if (col.visible === true) {
                                        visibleColumnsIndexes.push(index);
                                    }
                                });
                            }

                            table.columns().every(function() {
                                const columnIndex = this[0][0];
                                let isVisible = JSON.parse(this.visible());

                                if (visibleColumnsIndexes.length) {
                                    isVisible = visibleColumnsIndexes.includes(columnIndex);
                                }

                                $("[data-column='" + columnIndex + "']").prop("checked", isVisible);
                                let column = table.column(columnIndex);
                                column.visible(isVisible);
                            });
                            countFilters();
                            saveColumns(table.columns(), table);
                            var addDeleteHtml = "";
                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['addOffer'] == 1) { ?>
                                addDeleteHtml += '<div class="text-center"><button title="Add inquiry" style="padding:2px 6px;width:38px;height:38px;float:left;" class="btn btn-primary" data-toggle="modal" data-target="#addInquiry"><i style="font-size:22px;padding-top:3px;" class="fas fa-plus"></i></button></div><div class="text-center"><button title="Duplicate offer" style="padding:2px 6px;width:38px;height:38px;float:left;margin-left:10px;" class="btn btn-primary" data-toggle="modal" data-target="#duplicateOffer"><i style="font-size:22px;padding-top:3px;" class="fas fa-copy"></i></button></div>';
                            <?php }
                            if ($_SESSION['plasticonDigitalUser']['crm']['deleteOffer'] == 1) {
                            ?>
                                addDeleteHtml += '<button class="btn btn-danger text-center" data-toggle="modal" data-target="#deleteOffer" style="margin-left:10px;height:38px;" title="Delete offer"><i style="font-size:15px;" class="fas fa-minus"></i></button>';
                            <?php }
                            if ($_SESSION['plasticonDigitalUser']['crm']['export'] == 1) {
                            ?>
                                addDeleteHtml += '<a href="assets/php/export.php" target="_blank"><button class="btn btn-primary text-center" style="margin-left:10px;height:38px;" title="Export"><i style="font-size:15px;" class="fas fa-download"></i> offers</button></a>';
                            <?php } ?>

                            <?php if ($_SESSION['plasticonDigitalUser']['crm']['exportProducts'] == 1) { ?>
                                addDeleteHtml += '<a href="assets/php/exportProducts.php" target="_blank"><button class="btn btn-primary text-center" style="margin-left:10px;height:38px;" title="Export Products"><i style="font-size:15px;" class="fas fa-download"></i> products</button></a>';
                                addDeleteHtml += '<a href="assets/php/exportProducts.php?csv=1" target="_blank"><button class="btn btn-primary text-center" style="margin-left:10px;height:38px;" title="Export Products CSV"><i style="font-size:15px;" class="fas fa-download"></i> products csv</button></a>';
                            <?php } ?>

                            $(".addDeleteClass").html(addDeleteHtml);
                            $(".pagePerPage").html('<select id="myPageLen" class="form-control"><option value="10">10</option><option value="50">50</option><option value="100">100</option><option value="200">200</option></select>');
                            $("#offers_wrapper").css("padding", "0").children().first().css("margin", "5px 0px");
                            $("#offers_length").remove();
                            $("#offers_filter").remove();
                            $("#mySearchBox").val(st.search.search);
                            $("#myPageLen").val(st.length);
                            $("#myPageLen").change(function() {
                                table.page.len($(this).val()).draw();
                            })


                            setTimeout(function() {
                                init = 1;
                                initPerm = 1;
                                table.ajax.reload();
                            }, 150);
                        }

                    })
                }
            });
        }

        async function getUserStateForLoadingFilters() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php',
                    data: {
                        action: "getCurrentUserState",
                        user: <?php echo $_SESSION['plasticonDigitalUser']['id']; ?>,
                        tableUniqueId: "CRM-offers-management"
                    },
                    success: function(res) {
                        resolve(res);
                    },
                    error: function(err) {
                        reject(err);
                    }
                });
            });
        }

        async function loadFilters(forcedState = [], sesLoad = 1) {
            try {

                let state = forcedState;

                if (forcedState.length === 0) {
                    state = await getUserStateForLoadingFilters();
                }

                return new Promise((resolve, reject) => {
                    $.ajax({
                        type: 'get',
                        url: 'assets/php/ajaxHandeler.php',
                        data: {
                            action: "loadFilters",
                            user: <?php echo $_SESSION['plasticonDigitalUser']['id']; ?>,
                            name: state,
                            sesLoad: sesLoad,
                            tableUniqueId: "CRM-offers-management"
                        },
                        success: function(res) {
                            resolve(JSON.parse(res)); // Zwracamy przetworzone dane
                        },
                        error: function(err) {
                            reject(err);
                        }
                    });
                });

            } catch (error) {
                console.error("Error loading filters:", error);
                return null;
            }
        }

        $(document).ready(function() {




            $(document).keypress(
                function(event) {
                    if (event.which == '13') {
                        event.preventDefault();
                    }
                });
            tableInt = $(".table-responsive").html();
            getFiltersList("CRM-offers-management");
            loadFilters().then(initialFiltersState => {
                if (initialFiltersState) {
                    getCurrentUserState("CRM-offers-management", initialFiltersState);
                } else {
                    console.log("Nie udało się załadować filtrów.");
                }
            }).catch(error => {
                console.error("Błąd podczas ładowania filtrów:", error);
            });

            $('#project').change(function() {
                var id = $(this).val();
                $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=getProjectInfo&id=' + id,
                    success: function(clientName) {
                        if (clientName != "brak") {
                            $('[name="clientEnd"]').html("<option value='" + clientName + "'>" + clientName + "</option>");
                            $('[name="clientEnd"]').val(clientName).trigger("change");
                        } else
                            $('[name="clientEnd"]').html("").val("").trigger("change");
                    }
                });
            })
            $('[data-toggle="popover"]').popover();


            $('.select2').select2({
                placeholder: {
                    id: '-1',
                    text: () => {
                        "hmm"
                    },
                    width: "max-content"
                }
            }, 'reload');

            // Reload select2 for sales company so value is selected

            function placeholderyXD(rodzaj, data) {
                data.forEach(element => {
                    let highestElement;
                    setTimeout(() => {
                        element.style.width = "max-content";
                    }, 1000);
                    if (rodzaj == 0) highestElement = element.parentElement.parentElement.parentElement.parentElement.parentElement.parentElement;
                    if (rodzaj == 1) highestElement = element.parentElement.parentElement.parentElement.parentElement.parentElement;
                    if (rodzaj == 0) element.placeholder = highestElement.children[0].getAttribute("placeholder")
                    if (rodzaj == 1) element.innerText = highestElement.children[0].getAttribute("placeholder")

                    element.addEventListener("blur", () => {
                        if (rodzaj == 0 && element.parentElement.parentElement.children.length == 1) {
                            element.placeholder = highestElement.children[0].getAttribute("placeholder")
                            element.style.display = "block";
                        } else {
                            element.style.display = "none";
                        }
                        if (rodzaj == 1 && element.parentElement.parentElement.children.length == 1) {
                            element.innerText = highestElement.children[0].getAttribute("placeholder")
                            element.style.display = "block";
                        } else {
                            element.style.display = "none";
                        }
                        element.style.width = "max-content";
                        element.parentElement.parentElement.style.height = "max-content";
                        element.parentElement.parentElement.parentElement.style.height = "max-content";
                    })
                })
            }
            setTimeout(() => {
                let parent = document.querySelector(".RAM")
                placeholderyXD(0, [...parent.querySelectorAll(".select2-search__field")]);
                placeholderyXD(1, [...parent.querySelectorAll(".select2-selection__placeholder")]);
            }, "300")

            $(".select2-ajax").select2({
                allowClear: true,
                placeholder: 'Select',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            offer: params.term,
                            action: 'selectOffers'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.offerNo.indexOf(params.term) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.offerNo,
                                    id: item.offerNo
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            var endClientFilter = $(".select2-endclient-filter").select2({
                allowClear: true,
                placeholder: 'Select',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getEndclientNames'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            var resellerFilter = $(".select2-reseller-filter").select2({
                allowClear: true,
                placeholder: 'Reseller',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getResellerNames'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            if (value.longName.toLowerCase().indexOf(params.term.toLowerCase()) != -1)
                                resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            $(".select2-reseller").select2({
                allowClear: true,
                placeholder: 'Reseller',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getClients'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            $(".select2-endclient").select2({
                allowClear: true,
                placeholder: 'End client',
                ajax: {
                    method: "GET",
                    url: "assets/php/ajaxHandeler.php",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        n = encodeURIComponent(params.term)
                        return {
                            name: n,
                            action: 'getClients'
                        };
                    },
                    processResults: function(data, params) {
                        var resData = [];
                        data.forEach(function(value) {
                            resData.push(value)
                        })
                        return {
                            results: $.map(resData, function(item) {
                                return {
                                    text: item.longName,
                                    id: item.id
                                }
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 3
            })
            <?php if ($_SESSION['plasticonDigitalUser']['company'] == "PP") {
            ?>
                $("[name='v']").val("<?php echo $_SESSION['plasticonDigitalUser']['company']; ?>").trigger('change');
                $("[name='id']").val("<?php echo $_SESSION['plasticonDigitalUser']['company']; ?>").trigger('change');
                $("[name='followUp']").val("<?php echo $_SESSION['plasticonDigitalUser']['company']; ?>").trigger('change');
            <?php } ?>
            $('#client, #clientLocation, #contactPurchase, #contactTechnican, [name="id"], [name="v"], #project').select2({
                dropdownParent: $("#addInquiry")
            });
            $('.filterStyle').each(function() {
                var t = $(this).next().children().first().children().first();
                $(t).css('height', '28px').children().first().css({
                    'line-height': '24px',
                    'height': '28px'
                });
                $(t).children().last().css('height', '30px');
                $(t).children().first().children().first().css("margin-top", "2px");
            })
            // $("#getFilters").click(function(){
            // 	table.ajax.reload();
            // 	setTimeout(function(){
            // 		$('.filterSpanNonStyle').each(function(){
            // 			var t=$(this);
            // 			var labelWidth=$(t).prev().outerWidth();
            // 			var parentWidth=$(t).parent().outerWidth();
            // 			$(t).css("width",(parentWidth/2)-50);
            // 		})
            // 	}, 200);
            // 	setTimeout(function(){
            // 		$('.filterSpanNonStyle').each(function(){
            // 			var t=$(this);
            // 			if($(t).children().length==2)
            // 			{
            // 				var child1=$(t).children().first();
            // 				var child2=$(t).children().last();
            // 				var w=$(t).outerWidth();
            // 				var w=w/2-3;
            // 				$(child1).css("width",w);
            // 				$(child2).css("width",w);
            // 			}
            // 		})
            // 	}, 200);
            // })
            $('#collapseOneTrigger').click(function() {
                if (!$('#collapseOne').hasClass('show')) {
                    $('#inquiriesThisMonth').html(<?php echo inquiriesThisMonth(date("Y-m-d")); ?>);
                    $('#inquiriesThisFY').html(<?php echo inquiriesThisFY(date("Y-m-d")); ?>);
                    $('#offersThisMonth').html(<?php echo offersThisMonth(date("Y-m-d")); ?>);
                    $('#offersThisFY').html(<?php echo offersThisFY(date("Y-m-d")); ?>);
                }
            })
            $('.filterStyle, .filterControl, .filterCheckbox').on('change input paste', function() {
                if (init == 1) {
                    table.ajax.reload();
                    countFilters();
                }
            })
            $("#filterReset").click(function() {
                changeState("Default", 0);
                setTimeout(() => {
                    let parent = document.querySelector(".RAM")
                    placeholderyXD(0, [...parent.querySelectorAll(".select2-search__field")]);
                    placeholderyXD(1, [...parent.querySelectorAll(".select2-selection__placeholder")]);
                }, "300")
            })
            /*$('#clientFilter').change(function() {
             if($(this).val()!="")
             getCities($(this).val(), $('#clientLocationFilter'));
             else
             $('#clientLocationFilter').html("<option value='' selected>--Location--</option>").prop("disabled", true);
             });
             $('#endClientFilter').change(function() {
             if($(this).val()!="")
             getCities($(this).val(), $('#endClientLocationFilter'));
             else
             $('#endClientLocationFilter').html("<option value='' selected>--Location--</option>").prop("disabled", true);
             });*/
            $('.toggle-vis').click(function() {
                var colNr = $(this).attr('data-column');
                var column = table.column($(this).attr('data-column'));
                $('[data-column="' + colNr + '"]').prop("checked", !column.visible());
                column.visible(!column.visible());
                saveColumns(table.columns(), table);
                saveDynamic("CRM-offers-management");
                saveSessionView("CRM-offers-management");
            });
            var typingTimer;
            var doneTypingInterval = 500;
            $('#mySearchBox').keyup(function() {
                clearTimeout(typingTimer);
                typingTimer = setTimeout(doneTyping, doneTypingInterval);
            });

            function doneTyping() {
                table.search($("#mySearchBox").val()).draw();
            }
            $('.reset-button').click(function() {
                table.search('').draw();
            })
            $('#checkAll').click(function() {
                if ($(this).html() == 'Select all') {
                    // $(this).html('<i style="font-size:20px;margin-right:2px;" class="fas fa-check"></i>Deselect all');
                    $(this).html('Deselect all');
                    $('.chbox').each(function() {
                        if ($(this).prop('disabled') == false) {
                            $(this).prop('checked', true);
                            var column = table.column($(this).attr('data-column'));
                            column.visible(true);
                        }
                    })
                    saveColumns(table.columns(), table);
                } else {
                    // $(this).html('<i style="font-size:20px;margin-right:2px;" class="fas fa-check"></i>Select all');
                    $(this).html('Select all');
                    $('.chbox').each(function() {
                        if ($(this).prop('disabled') == false) {
                            $(this).prop('checked', false);
                            var column = table.column($(this).attr('data-column'));
                            column.visible(false);
                        }
                    })
                    saveColumns(table.columns(), table);
                }
            })
            $('#resp').val('<?= $_SESSION['plasticonDigitalUser']['id'] ?>').trigger('change');
            $('#iSales').val('<?= $_SESSION['plasticonDigitalUser']['id'] ?>').trigger('change');
            $('#fUp').val('<?= $_SESSION['plasticonDigitalUser']['id'] ?>').trigger('change');
            $('input[name=internalNumber').on('keyup', function() {

                const input = $(this);
                input.parent().find('.info-msg').remove();

                const salesCompany = $('select[name=company]').val();

                if (input.val().length < 1 || salesCompany < 1) {
                    return;
                }

                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: "checkCrmInternalNumber",
                            internal_number: input.val(),
                            sales_company: salesCompany
                        },
                        success: function(response) {
                            if (response.status === "error") {
                                const alertMessage = $('<div class="alert alert-info p-1 info-msg  form-100">' + response.message + '</div>');
                                input.parent().append(alertMessage);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                    });
                }, 1000);

            });

            $('select[name=company').on('change', function() {

                const input = $('input[name=internalNumber');
                input.parent().find('.info-msg').remove();

                const salesCompany = $(this).val();

                if (input.val().length < 1 || salesCompany < 1) {
                    return;
                }

                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: "checkCrmInternalNumber",
                            internal_number: input.val(),
                            sales_company: salesCompany
                        },
                        success: function(response) {
                            if (response.status === "error") {
                                const alertMessage = $('<div class="alert alert-info p-1 info-msg  form-100">' + response.message + '</div>');
                                input.parent().append(alertMessage);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                    });
                }, 1000);

            });

            function checkCrmInquiryNo(input) {
                const salesCompany = $('select[name=company]').val();
                const client = $('select[name=client]').val();

                if (input.val().length < 1 || salesCompany < 1 || !client) {
                    return;
                }

                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: "checkCrmInquiryNo",
                            inquiry_no: input.val(),
                            sales_company: salesCompany,
                            client: client,
                        },
                        success: function(response) {
                            if (response.status === "error") {
                                const alertMessage = $('<div class="alert alert-info p-1 info-msg form-100">' + response.message + '</div>');
                                input.parent().append(alertMessage);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                    });
                }, 1000);
            }

            function checkCrmInquiryNoEnd(input) {
                const salesCompany = $('select[name=company]').val();
                const endClient = $('select[name=clientEnd]').val();

                if (input.val().length < 1 || salesCompany < 1 || !endClient) {
                    return;
                }

                clearTimeout(typingTimer);
                typingTimer = setTimeout(function() {
                    $.ajax({
                        url: "assets/php/ajaxHandeler.php",
                        method: "get",
                        data: {
                            action: "checkCrmInquiryNoEnd",
                            inquiry_no: input.val(),
                            sales_company: salesCompany,
                            end_client: endClient
                        },
                        success: function(response) {
                            if (response.status === "error") {
                                const alertMessage = $('<div class="alert alert-info p-1 info-msg form-100">' + response.message + '</div>');
                                input.parent().append(alertMessage);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.log(error);
                        },
                    });
                }, 1000);
            }

            $('input[name=fillInquiryNo]').on('keyup', function() {
                const input = $(this);
                input.parent().find('.info-msg').remove();
                checkCrmInquiryNo(input);
            });

            $('input[name=fillInquiryNoEnd]').on('keyup', function() {
                const input = $(this);
                input.parent().find('.info-msg').remove();
                checkCrmInquiryNoEnd(input);
            });

            $('select[name=client]').on('change', function() {
                const input = $('input[name=fillInquiryNo]');
                input.parent().find('.info-msg').remove();
                checkCrmInquiryNo(input);
            });

            $('select[name=clientEnd]').on('change', function() {
                const input = $('input[name=fillInquiryNoEnd]');
                input.parent().find('.info-msg').remove();
                checkCrmInquiryNoEnd(input);
            });

            $(document).on('click', function(event) {
                const modal = $('.modal');
                const modalContent = modal.find('.modal-content');

                // Check if the click is outside the modal content and not on an interactive element
                if (!$(event.target).closest(modalContent).length && modal.is(':visible') &&
                    !$(event.target).is('input, textarea, select, option')) {
                    modal.modal('hide');
                }
            });




            $(document).on('click', '.submit-del-comment', function() {
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');
                console.log(id, offerId);

                $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=deleteComment&id=' + id,
                    success: function(res) {
                        if (res.status === "ok") {
                            $('#delCommentModal').modal('hide'); // Ukryj modal usuwania
                            // Ponownie wczytaj komentarze
                            $.ajax({
                                type: 'get',
                                url: 'assets/php/ajaxHandeler.php?action=getComments&id=' + offerId + "&case=home",
                                success: function(comments) {
                                    $('#offerComments').find('.modal-body').html(comments);
                                    $('#offerComments').modal('show'); // Pokaż modal z nowymi danymi
                                    $('.offers-list-comment-add').attr('offerno', offerId);

                                    // Zresetuj dane w przyciskach w modalach edycji/usuwania
                                    $('#delCommentModal, #editCommentModal').find('.modal-footer button').removeAttr('data-id').removeAttr('data-offer_id');

                                    showHideCommentsModalAlert("show", "success", "Comment deleted"); // Pokaż alert o sukcesie

                                    $('#offers').DataTable().ajax.reload();
                                },
                                error: function(xhr, status, error) {
                                    console.log(error);
                                }
                            });
                        } else {
                            console.log('Error: Could not delete comment.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log(error);
                    }
                });
            });


            $(document).on('click', '.comment-del', function() {
                $('#offerComments').modal('hide');
                const id = $(this).data('id');
                const offerId = $(this).data('offer_id');

                $('#delCommentModal').modal('show');
                $('#delCommentModal').find('.submit-del-comment')
                    .attr('data-id', id)
                    .attr('data-offer_id', offerId)
            });

            const getCommentContent = async (id) => {
                return $.ajax({
                    type: 'get',
                    url: 'assets/php/ajaxHandeler.php?action=getCommentContent&id=' + id,
                });
            }

            const editComment = async (id, content) => {
                return $.ajax({
                    type: 'post',
                    url: 'assets/php/ajaxHandeler.php?action=editComment',
                    data: {
                        id: id,
                        content: content
                    }
                });
            }

            $(document).on('click', '.comment-edit', async function() {
                $('#offerComments').modal('hide');
                const id = $(this).data('id');
                const offerId = $(this).data('offer_id');
                const contentData = await getCommentContent(id);

                $('#editCommentModal').find('.modal-body').find('.alert').remove();
                addRemoveElementClass($('#editCommentModal').find('.modal-body textarea'), 'borderRed', 'remove');

                if (contentData.status == "ok") {
                    $('#editCommentModal').find('.modal-body').find('textarea').val(contentData.content);
                    $('#editCommentModal').modal('show');
                    $('#editCommentModal').find('.modal-footer').find('.btn-primary').attr('data-id', id);
                    $('#editCommentModal').find('.modal-footer').find('.btn-primary').attr('data-offer_id', offerId);
                }
            });

            $(document).on('click', '#editCommentModal .save-edit-comment', async function() {
                // Odczytaj aktualne dane z atrybutów
                const id = $(this).attr('data-id');
                const offerId = $(this).attr('data-offer_id');
                const content = $('#editCommentModal').find('.modal-body textarea').val();

                $('#editCommentModal').find('.modal-body').find('.alert').remove();
                addRemoveElementClass($('#editCommentModal').find('.modal-body textarea'), 'borderRed', 'remove');

                if (content.trim().length < 1) {
                    addRemoveElementClass($('#editCommentModal').find('.modal-body textarea'), 'borderRed', 'add');
                    $('#editCommentModal').find('.modal-body').prepend('<div class="alert alert-danger">Comment is empty.</div>');
                    return;
                }

                try {
                    const res = await editComment(id, content);

                    if (res.status === "ok") {
                        $('#editCommentModal').modal('hide'); // Ukryj modal edycji

                        // Ponownie załaduj komentarze
                        $.ajax({
                            type: 'get',
                            url: 'assets/php/ajaxHandeler.php?action=getComments&id=' + offerId + "&case=home",
                            success: function(comments) {
                                $('#offerComments').find('.modal-body').html(comments); // Zaktualizuj treść modala
                                $('#offerComments').modal('show'); // Pokaż modal z nowymi danymi
                                $('.offers-list-comment-add').attr('offerno', offerId);

                                // Zresetuj dane w przyciskach w modalach edycji/usuwania
                                $('#delCommentModal, #editCommentModal').find('.modal-footer button').removeAttr('data-id').removeAttr('data-offer_id');

                                showHideCommentsModalAlert("show", "success", "Comment edited"); // Pokaż alert o sukcesie

                                $('#offers').DataTable().ajax.reload();

                            },
                            error: function(xhr, status, error) {
                                console.error("Error loading comments after edit:", error);
                            }
                        });
                    } else {
                        console.error("Error editing comment:", res.message);
                    }
                } catch (error) {
                    console.error("Error during comment edit AJAX:", error);
                }
            });



            $(document).on('click', '.offers-list-comment-add', function() {
                addComment(this);
            });

            $(document).on('keydown', '.offers-list-comment-add', function(event) {
                if (event.key === 'Enter' && !event.shiftKey) {
                    addComment(this);
                }
            });

            $(document).on('keydown', 'textarea[name=offers_list_comment]', function(e) {
                if (e.keyCode == 13) {
                    e.preventDefault();
                    this.value = this.value.substring(0, this.selectionStart) + "" + "\n" + this.value.substring(this.selectionEnd, this.value.length);
                }
            });

        });

        $(document).on("ajaxComplete", function() {

            $('textarea[name=offers_list_comment]').text("");
            addRemoveElementClass($('textarea[name=offers_list_comment]'), 'borderRed', 'remove');

            $('.pointer').children().each(function() {
                if ($(this).is('td')) {
                    if (!$(this).hasClass('td-comments')) {
                        const offerId = $(this).parent().children().first().attr('offerno');
                        $(this).attr("onclick", "(location.href='offer.php?id=" + offerId + "')");
                    } else {
                        $(this).attr("title", "Show comments").attr("onclick", 'showComments(this)');
                    }
                }
            });

        });

        const showComments = (element) => {
            const offerId = $(element).parent().children().first().attr('offerno');
            $.ajax({
                type: 'get',
                url: 'assets/php/ajaxHandeler.php?action=getComments&id=' + offerId + "&case=home",
                success: function(comments) {
                    $('#offerComments').find('.modal-body').html(comments);
                    $('#offerComments').modal('show'); // Pokaż modal z nowymi danymi
                    $('.offers-list-comment-add').attr('offerno', offerId);

                    // Zresetuj dane w przyciskach w modalach edycji/usuwania
                    $('#delCommentModal, #editCommentModal').find('.modal-footer button').removeAttr('data-id').removeAttr('data-offer_id');
                },
                error: function(xhr, status, error) {
                    console.log(error);
                }
            });
        };


        function checkKiloEuro(x) {
            if (x.value >= 500) {
                let confirmation = confirm("Are you sure? Remember that this value should be expressed in k€");
                if (confirmation) {
                    x.blur();
                } else {
                    x.value = 0;
                    x.select();
                }
            }
        }

        const addComment = (btn) => {

            const offerId = $(btn).attr('offerno');
            const creator = $(btn).data('creator');
            const comment = $('textarea[name=offers_list_comment]').val();
            const commentContainer = $(btn).parent().parent().find('.offers-list-comment-container');

            let errorMessage = "";

            if (comment.trim().length < 1) {
                errorMessage = "Comment is empty.";
            } else if (!creator || !offerId) {
                errorMessage = "Comment couldn't be saved.";
            }

            if (errorMessage.length > 0) {
                addRemoveElementClass($('textarea[name=offers_list_comment]'), 'borderRed', 'add');
                showHideCommentsModalAlert("show", "danger", errorMessage);
                $('#offerComments').find('.modal-body').animate({
                    scrollTop: 0
                }, 'smooth');
                return;
            }

            $.ajax({
                url: "assets/php/ajaxHandeler.php",
                method: "get",
                data: {
                    action: "insertComment",
                    content: comment,
                    userId: [],
                    creator: creator,
                    caseId: offerId,
                    caseName: "offer",
                },
                success: function(response) {

                    let responseAlertType = response.status === "success" ? "success" : "danger";

                    if (response.status === "success") {
                        const newComment = response.data;
                        $('#offerComments').find('.modal-body').prepend(newComment);
                        addRemoveElementClass($('textarea[name=offers_list_comment]'), 'borderRed', 'remove');
                        $('#offers').DataTable().ajax.reload();
                    }

                    $('textarea[name=offers_list_comment]').val("");
                    showHideCommentsModalAlert("show", responseAlertType, response.message);
                    $('#offerComments').find('.modal-body').animate({
                        scrollTop: 0
                    }, 'smooth');


                },
                error: function(xhr, status, error) {
                    console.log(error);
                    $('#offerComments').find('.modal-body').prepend('<div class="alert alert-danger" style="border-radius: 0;">Error adding comments</div>');
                },
            });
        }

        const showHideCommentsModalAlert = (showOrHide, type = "", message = "") => {
            const alert = $('#offerComments').find('.modal-body').find('.alert');
            alert.remove();

            if (showOrHide === "show") {
                $('#offerComments').find('.modal-body').prepend(`<div class='alert alert-${type}' style='display: flex;'><div style='width:97%;'><strong>${message}</strong></div><div style='width:3%;' class='text-center'></div></div>`);
            }
        }


        const addRemoveElementClass = (element, className, action) => {
            if (action === "add") {
                element.addClass(className);
            } else if (action === "remove") {
                element.removeClass(className);
            }
        }
    </script>