<?php

function consoleLog($data, $filename = "_log")
{
    try {
        $globalDir = MAIN_DIR_GLOBAL;
        $file = "$globalDir/logs/$filename.log";

        // Add timestamp to the log
        $timestamp = date('Y-m-d H:i:s');

        // Prepare data for logging
        if (is_array($data) || is_object($data)) {
            $logData = [
                'timestamp' => $timestamp,
                'data' => $data
            ];
            $text = json_encode($logData, JSON_PRETTY_PRINT);
        } else {
            $text = "[$timestamp] " . $data;
        }

        // Check if directory exists and is writable
        $logDir = dirname($file);
        if (!is_dir($logDir)) {
            if (!mkdir($logDir, 0755, true)) {
                throw new Exception("Unable to create log directory: $logDir");
            }
        }

        if (!is_writable($logDir)) {
            throw new Exception("Log directory is not writable: $logDir");
        }

        // Add double line break for better readability
        if (!empty($text)) {
            $text = $text . PHP_EOL . PHP_EOL;

            if (file_put_contents($file, $text, FILE_APPEND | LOCK_EX) === false) {
                throw new Exception("Failed to write to log file: $file");
            }
        }
    } catch (Exception $e) {
        error_log("Error in consoleLog(): " . $e->getMessage());
        // Optionally throw the exception if you want the calling code to handle it
        throw $e;
    }
}
